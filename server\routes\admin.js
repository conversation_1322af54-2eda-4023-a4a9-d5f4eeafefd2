/**
 * 描述: 用户路由模块
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/

const express = require('express');
const router = express.Router();
const service = require('../services/adminService');

router.post('/queryBais', service.queryAll);
router.post('/querysNotice', service.querysNotice);
router.post('/queryCurrency', service.queryCurrency);
router.post('/queryKline', service.queryKline);

router.post('/blockchain', service.blockChain);
module.exports = router;


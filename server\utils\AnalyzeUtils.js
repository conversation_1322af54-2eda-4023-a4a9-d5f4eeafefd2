const {default: BigNumber} = require("bignumber.js");
const http = require("http");
const {errorToRedis, redis} = require("./redisUtils");
BigNumber.config({DECIMAL_PLACES: 100});

const Mutex = require('async-mutex').Mutex;//锁工程
const sendLock = new Mutex();//发送锁
let sendOk = true;//发送状态


class BatchProcessor {

    constructor({name, maxInterval, options}) {
        this.maxInterval = maxInterval;
        this.buffer = [];
        this.bufferNo = new Set();
        this.options = options;
        this.errorKey = name + "_error";
        this.key_listen = name + "_ok";
        this.startInterval();
    }

    /**
     * streamLoad导入doris方式
     * @param sendOk
     * @param sendLock
     * @param array
     * @param no
     * @param batchProcessor
     * @returns {Promise<void>}
     */
    streamLoadAdd(array, no) {
        let sendlock = sendLock.acquire();//上锁为了解决重置缓冲区状态的异步问题
        let that=this;
        sendlock.then(release => {
            try {
                array.forEach(item => {
                    that.addData(item, no);
                });
            } finally {
                release();
            }
        }).catch(error => {
            // 处理拒绝错误
            console.log("lock:" + error)
        });
    }

    sendData(options, content, blockNumber, followRedirects = 5) {
        let startTime = Date.now();
        if (followRedirects <= 0) {
            throw new Error('Too many redirects');
        }
        const req = http.request(options, (res) => {
            if (res.statusCode === 307 && res.headers.location) {
                // 解析新的URL
                const newUrl = new URL(res.headers.location);
                // 创建新的请求选项
                const newOptions = {
                    hostname: newUrl.hostname,
                    port: newUrl.port || 80,
                    path: newUrl.pathname + newUrl.search,
                    method: options.method,
                    headers: options.headers
                };
                // 重新发送请求
                this.sendData(newOptions, content, blockNumber, followRedirects - 1);
            } else {
                res.on('data', (chunk) => {
                    let duration = Date.now() - startTime;
                    let res_d = JSON.parse(chunk);
                    if (res_d["Status"] != "Success") {
                        if (blockNumber.size > 0) {
                            let setArray = Array.from(blockNumber).sort();
                            if (setArray.length > 0) {
                                errorToRedis(this.errorKey, setArray);
                            }
                        }
                        console.error("写入错误status:" + chunk + ",耗时" + duration + " ms");
                    } else {
                        let msg = "ok";
                        if (blockNumber.size > 0) {
                            let setArray = Array.from(blockNumber).sort();
                            redis.set(this.key_listen, setArray[setArray.length - 1]);
                            msg = setArray[setArray.length - 1];
                        }
                        console.log("写入成功:" + msg + ",耗时" + duration + " ms");
                    }
                });
                res.on('error', (e) => {
                    console.error("发送异常" + e);
                });
                res.on('end', () => {
                });
            }
        });
        req.on('error', (error) => {
            let setArray = Array.from(blockNumber).sort();
            errorToRedis(this.errorKey, setArray);
            console.error(`请求遇到问题: ${error}`);
        });
        req.write(content);
        req.end();
    }

    addData(data, no) {
        this.buffer.push(data);//
        if (no) {
            this.bufferNo.add(no);//记录当前所有的block_number
        }
    }

    send() {
        if (sendOk) {//发送状态为完成则开始争抢锁
            sendOk = false;
            let sendlock = sendLock.acquire();//上锁为了解决重置缓冲区状态的异步问题
            let that=this;
            sendlock.then(release => {
                try {
                    // 处理解决结果
                    if (that.buffer.length > 0) {
                        that.sendData(that.options, that.buffer.join('\n'), that.bufferNo);// 实际发送数据的逻辑
                        // 重置缓冲区状态
                        that.buffer = [];
                        that.bufferNo = new Set();
                    }
                } finally {
                    release();
                    sendOk = true;//发送完成
                }
            }).catch(error => {
                // 处理拒绝错误
                sendOk = true;
                console.log("lock:" + error)
            });
        }
    }

    startInterval() {
        this.intervalId = setInterval(() => {
            this.send(); // 定时检查并发送数据
        }, this.maxInterval);
    }

    // 清理资源，停止定时器
    distroyInterval() {
        clearInterval(this.intervalId);
    }
}

module.exports = {BatchProcessor};
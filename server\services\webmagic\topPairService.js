let https = require("https");
const {img_url_upload_ave, img_url_upload} = require('./task/imgUpload');
const FirstAnalyzeEnum = require("../../utils/enums/AnalyzeDorisKeyEnum");
const userCollectModel = require("../../model/mysql/userCollectModel");
const {formatAddress} = require("../../utils/functions");
const {getHeaders} = require("./config/httpHead")
const {redis, errorToRedis} = require("../../utils/redisUtils");
const {Contract, JsonRpcProvider, formatUnits} = require("ethers");
const {allAbi} = require("../../abi");
const {RPC_Nginx, RPC, RPC_NEW} = require("../../utils/constant");
const BigNumber = require("bignumber.js");
BigNumber.config({DECIMAL_PLACES: 100});
const provider = new JsonRpcProvider(RPC_NEW, undefined, {polling: true});
window = global;
const ave_url = "api.eskegs.com";

function decode(encode_data) {
    return JSON.parse(window.decodeURIComponent(window.atob(encode_data).replace(/\+/g, " ")));
}


// 热搜榜
async function Hot_Search_List(request, result) {
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=trending&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data)
                de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    // console.log("tokenaddress" + item.tokenAddress + ":" + item.imgUrl);
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                });
                // let data = {
                //     "id": 334440,
                //     "chain": "bsc",
                //     "pair": "0x976cad3fe7c07e36d4e11e8f076f51890f82eaca",
                //     "targetToken": "0x274b148b6a1b4ebaba1054e5230192fcacaba899",
                //     "token0Id": 321774,
                //     "token0Address": "0x274b148b6a1b4ebaba1054e5230192fcacaba899",
                //     "token0Symbol": "MGDG",
                //     "token0LogoUrl": null,
                //     "token1Id": 8,
                //     "token1Address": "0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c",
                //     "token1Symbol": "WBNB",
                //     "token1LogoUrl": "/token_icon/bsc/0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c.png",
                //     "currentPriceUsd": 0.007911181163281095573301526525,
                //     "priceChange24h": 23790135203677518276.460001956711403719880322690523,
                //     "txVolumeU24h": 2.044702720000000000E-12,
                //     "txCount24h": 1,
                //     "holders": 1577,
                //     "amm": "cakev2",
                //     "openTime": null,
                //     "marketCap": 0E-30,
                //     "reserveUsd": 0E-30,
                //     "tokenIndex": 0,
                //     "tokenId": 321774
                // }
                result.json({
                    code: 200,
                    msg: '请求成功！',
                    data: de_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();
}

// 交易榜
async function Trading_List(request, result) {
    let options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=trade&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data)
                de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    // console.log("tokenaddress" + item.tokenAddress + ":" + item.imgUrl);
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                });
                result.json({
                    code: 200,
                    msg: '请求成功！',
                    data: de_data
                });
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

// 新币榜 
async function New_Currency_List(request, result) {
    let options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=new&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("New_Currency_List:认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("New_Currency_List:encode_data为空");
                    result({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data);
                let data = [];
                for (let i = 0; i < de_data.length; i++) {
                    let item = de_data[i];
                    // de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    // console.log("tokenaddress" + item.tokenAddress + ":" + item.imgUrl);
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                    // });
                    data.push(item);
                    if (data.length > 50) {
                        break;
                    }
                }
                result({
                    code: 200,
                    msg: '请求成功！',
                    data: data
                });
            } catch (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', function (e) {
        result({
            code: 500, msg: e, data: null
        })
    })
    req.on('timeout', () => {
        console.error('请求超时:New_Currency_List');
        result({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}

// 涨幅榜
async function Increase_List(request, result) {
    let options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=gainer&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("Increase_List:认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("encode_data为空");
                    result({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    ;
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data);
                let data = [];
                for (let i = 0; i < de_data.length; i++) {
                    let item = de_data[i];
                    // de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];

                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                    // });
                    data.push(item);
                    if (data.length > 50) {
                        break;
                    }
                }
                result({
                    code: 200,
                    msg: '请求成功！',
                    data: data
                });
            } catch (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', function (e) {
        result({
            code: 500, msg: e, data: null
        })
    })
    req.on('timeout', () => {
        console.error('请求超时:Increase_List');
        result({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}

// 跌幅榜
async function Decrease_List(request, result) {
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=losers&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("Decrease_List:认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("Decrease_List:encode_data为空");
                    result({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data);
                let data = [];
                for (let i = 0; i < de_data.length; i++) {
                    let item = de_data[i];
                    // de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                    // });
                    data.push(item);
                    if (data.length > 50) {
                        break;
                    }
                }
                result({
                    code: 200,
                    msg: '请求成功！',
                    data: data
                });
            } catch (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', function (e) {
        result({
            code: 500, msg: e, data: null
        })
    })
    req.on('timeout', () => {
        console.error('请求超时:Decrease_List');
        result({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}

// 池子榜
async function Pool_List(request, result) {
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=pool&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };
    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("Pool_List:认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("encode_data为空");
                    result({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data);
                let data = [];
                for (let i = 0; i < de_data.length; i++) {
                    let item = de_data[i];
                    // de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    item.bottomPoolVolume = item['liguidity'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                    data.push(item);
                    if (data.length > 100) {
                        break;
                    }
                    // });
                }
                result({
                    code: 200,
                    msg: '请求成功！',
                    data: data
                });
            } catch (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', function (e) {
        result({
            code: 500, msg: e, data: null
        })
    })
    req.on('timeout', () => {
        console.error('请求超时:Pool_List');
        result({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}

// 成交榜
async function Transaction_List(request, result) {

    const options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=volume&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("Transaction_List:认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("Transaction_List:encode_data为空");
                    result({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    ;
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data);
                let data = [];
                for (let i = 0; i < de_data.length; i++) {
                    let item = de_data[i];
                    // de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];

                    //处理图片
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                    // });
                    data.push(item);
                    if (data.length > 50) {
                        break;
                    }
                }
                result({
                    code: 200,
                    msg: '请求成功！',
                    data: data
                });
            } catch (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', function (e) {
        result({
            code: 500, msg: e, data: null
        })
    })
    req.on('timeout', () => {
        console.error('请求超时:Transaction_List');
        result({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}


/**
 * 自选榜单
 * @returns {Promise<void>}
 */
async function myself_choose(request, result) {
    let data = [];
    try {
        if (request.query.user_id) {
            const userCollects = await userCollectModel().findAll({
                attributes: ['id', 'token_address'],
                where: {
                    uid: request.query.user_id,
                }
            });
            if (userCollects && userCollects.length > 0) {
                let tokenAddrs = [];
                userCollects.forEach(item => {
                    tokenAddrs.push(item.dataValues['token_address']);
                });

                // let tokens = await dbTokenQuery([{
                //     type: "in",
                //     column: "address",
                //     value: tokenAddrs
                // }]);
                if (tokenAddrs.length > 0) {
                    let obj;
                    let thread = [];
                    for (let i = 0; i < tokenAddrs.length; i++) {
                        let item = tokenAddrs[i];
                        thread.push(new Promise((r2, j2) => {
                            getTokenBase(item, function (call) {
                                if (call.code == 200) {
                                    if (call.data) {
                                        obj = {
                                            // id: map[item.id].id,
                                            tokenAddress: call.data.address,
                                            tokenSymbol: call.data.symbol,
                                            tokenLogoUrl: call.data.logo_url,
                                            priceChange24h: call.data.price_change,
                                            currentPriceUsd: call.data.current_price_usd,
                                            liguidity: call.data.liguidity
                                        }
                                        data.push(obj);
                                    }
                                    r2();
                                } else {
                                    j2(call.msg);
                                }
                            }, true);
                        }));
                    }
                    Promise.allSettled(thread).then(obj => {
                        result({
                            code: 200,
                            msg: "成功！",
                            data: data
                        });
                    }, e => {
                        result({
                            code: 500,
                            msg: e,
                            data: null
                        })
                    });
                } else {
                    result({
                        code: 200,
                        msg: "合约地址没有找到！",
                        data: null
                    })
                }
            } else {
                result({
                    code: 200,
                    msg: "没有添加自选！",
                    data: null
                })
            }
        } else {
            result({
                code: 400,
                msg: "user_id不能为空！",
                data: null
            })
        }
    } catch (e) {
        result({
            code: 500,
            msg: e,
            data: null
        })
    }
}

async function getTokenBase(address, call, cacheOpen) {
    try {
        if (cacheOpen !== false) {
            cacheOpen = true;
        }
        if (cacheOpen) {
            let objStr = await redis.hGet("webmagic_api:getTokenBase", address);
            if (objStr) {
                call({
                    code: 200, msg: '请求成功！', data: JSON.parse(objStr)
                });
                return;
            }
        }
        let url = '/v1api/v3/tokens/' + address + "-bsc"
        const options = {
            hostname: ave_url, path: url, agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        let response = {};
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', function () {
                try {
                    let thread = [];
                    let jsonData = JSON.parse(html);
                    if (!jsonData['data']) {
                        console.log("getTokenBase:encode_data为空");
                        call({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let data = jsonData['data'];
                    let imgPath = "/image/" + data["token"].token + ".png";
                    let mysqlObj = {
                        chain_id: 56,
                        address: formatAddress(data["token"].token),
                        logo_url: imgPath,
                        name: data["token"].name,
                        symbol: data["token"].symbol,
                        total_supply: data["token"].total,
                        decimals: data["token"].decimal,
                        holders: data["token"].holders,
                        is_open_source: 1,
                        current_price_usd: data["token"].current_price_usd,
                        price_change: data["token"].price_change,
                        risk_score: data["token"].risk_score,
                        status: 1,
                        publish_time: data["token"].publish_at,
                    };
                    let pair = data.pairs[0];
                    let reserve0 = 0;
                    let token_index = 0;
                    if (pair.target_token == pair.token0_address) {
                        token_index = 0;
                    } else {
                        token_index = 1;
                    }
                    reserve0 = pair['reserve' + token_index];
                    let liguidity = new BigNumber(reserve0).multipliedBy(mysqlObj.current_price_usd).multipliedBy(2).toFixed(30);
                    mysqlObj.liguidity = liguidity;
                    //创建代币
                    // if (!tokenRedis) {
                    //     redis.hSet("token_json_hdata", formatAddress(mysqlObj.address), JSON.stringify(mysqlObj));
                    // }
                    thread.push(new Promise((r, j) => {
                        try {
                            let img_url = data['token']['logo_url']
                            let imgFilePath = "." + imgPath;
                            img_url_upload(img_url, imgFilePath);
                            r();
                        } catch (e) {
                            j();
                        }
                    }));
                    Promise.all(thread).then(obj => {
                        redis.hSet("webmagic_api:getTokenBase", formatAddress(address), JSON.stringify(mysqlObj));
                        call({
                            code: 200, msg: "请求成功！", data: mysqlObj
                        });
                    }, e => {
                        call({
                            code: 500, msg: e, data: null
                        })
                    });
                } catch (e) {
                    call({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                call({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', function (e) {
            call({
                code: 500, msg: e, data: null
            })
        });
        req.on('timeout', () => {
            console.error('请求超时:getTokenBase');
            call({
                code: 500,
                msg: null,
                data: null
            })
        });
        req.setTimeout(60000);
        req.end();
    } catch (e) {
        call({
            code: 500, msg: e, data: null
        })
    }
}

// 换手榜
async function Change_Hands_List(request, result) {
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/tokens/token_list?chain=bsc&category=exchange&pageSize=100&sort=&direction=desc&group=0',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("Change_Hands_List:认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['encode_data'] == "") {
                    console.log("Change_Hands_List:encode_data为空");
                    result({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                let de_data = decode(encode_data);
                let data = [];
                for (let i = 0; i < de_data.length; i++) {
                    let item = de_data[i];
                    // de_data.forEach((item) => {
                    item.targetToken = item['target_token'];
                    if (item.targetToken.includes(item.token0_address)) {
                        item.tokenAddress = item['token0_address'];
                        item.tokenSymbol = item['token0_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token0_logo_url'];
                        item.tokenIndex = 0;
                    } else {
                        item.tokenAddress = item['token1_address'];
                        item.tokenSymbol = item['token1_symbol'];
                        item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                        item.imgUrl = item['token1_logo_url'];
                        item.tokenIndex = 1;
                    }
                    item.currentPriceUsd = item['current_price_usd'];
                    item.priceChange24h = item['price_change_24h'];
                    item.txVolumeU24h = item['tx_volume_u_24h'];
                    item.txCount24h = item['tx_count_24h'];
                    item.openTime = item['opening_at'];
                    item.marketCap = item['market_cap'];
                    delete item['target_token'];
                    delete item['token0_address'];
                    delete item['token0_symbol'];
                    delete item['token0_logo_url'];
                    delete item['token1_address'];
                    delete item['token1_symbol'];
                    delete item['token1_logo_url'];
                    delete item['current_price_usd'];
                    delete item['price_change_24h'];
                    delete item['tx_volume_u_24h'];
                    delete item['tx_count_24h'];
                    delete item['opening_at'];
                    delete item['market_cap'];
                    //处理图片
                    let imgFilePath = "." + item.tokenLogoUrl;
                    img_url_upload_ave(item.imgUrl, imgFilePath);
                    // });
                    data.push(item);
                    if (data.length > 50) {
                        break;
                    }
                }
                result({
                    code: 200,
                    msg: '请求成功！',
                    data: data
                });
            } catch (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', function (e) {
        result({
            code: 500, msg: e, data: null
        })
    })
    req.on('timeout', () => {
        console.error('请求超时:two_forty_k');
        result({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}

/**
 * 写入底池价格
 * @param data
 * @param openLink 表示是否使用链上数据，false默认值，true使用链上数据
 * @returns {Promise<void>}
 */
async function setBottomPoolVolume(data, openLink) {
    /**
     * 计算地址底池价值
     */
    if (!data) {
        return;
    }
    let contract;
    let promise = [];
    for (let i = 0; i < data.length; i++) {
        promise.push(new Promise(async (r1, j1) => {
            try {
                let item = data[i];
                if (!openLink) {
                    item.bottomPoolVolume = item.liguidity;
                } else {
                    let token = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", item.tokenAddress);
                    if (!token) {
                        throw Error("未找到token,token地址：" + item.tokenAddress);
                    }
                    let tokenObj = JSON.parse(token);
                    /**
                     * pair和tokenIndex为null的填补处理
                     */

                    if (!item.pair) {
                        throw Error("未找到pair_address,token地址：" + item.tokenAddress);
                    }
                    let pairJson = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata+"_v2", tokenObj.pair_address);
                    pairJson = JSON.parse(pairJson);
                    item.tokenIndex = pairJson.token_index;

                    /**
                     * 从链获取储备量，计算底池价格
                     * @type {Contract}
                     */
                    contract = new Contract(item.pair, allAbi, provider);
                    let getReserves = await contract.getReserves();
                    let Reserves = formatUnits(getReserves[item.tokenIndex], tokenObj.decimals)
                    let reserve0 = new BigNumber(Reserves);
                    let bottomPoolVolume = reserve0.multipliedBy(item.currentPriceUsd).multipliedBy(2).toFixed(30);
                    item.bottomPoolVolume = bottomPoolVolume;
                }
                r1();
            } catch (e) {
                j1(e);
            }
        }));
    }
    //等待所有任务完成
    await Promise.allSettled(promise).then(obj => {
        console.info("setBottomPoolVolume更新完成");
    }, e => {
        console.error("serviceTask:error:" + e);
    });
}

async function hotTop(request, result) {
    try {
        let cacheOpen = request.cacheOpen;
        if (cacheOpen !== false) {
            cacheOpen = true;
        }
        if (!request.query.id) {
            result.json({
                code: 400,
                msg: 'id参数不能为空',
                data: null
            })
            return;
        }
        switch (request.query.id) {
            case "2":
                //池子榜
                if (cacheOpen) {//是否打开缓存
                    let objStr2 = await redis.hGet("webmagic_api:Pool_List", request.query.id);
                    if (objStr2 && objStr2 != "null") {
                        result.json({
                            code: 200, msg: '请求成功！', data: JSON.parse(objStr2)
                        });
                        return;
                    }
                }
                await Pool_List(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    if (call.data) {
                        await redis.hSet("webmagic_api:Pool_List", request.query.id, JSON.stringify(call.data));
                    }
                    result.json(call);
                });
                break;
            case "3":
                //涨幅榜
                if (cacheOpen) {//是否打开缓存
                    let objStr3 = await redis.hGet("webmagic_api:Increase_List", request.query.id);
                    if (objStr3 && objStr3 != "null") {
                        result.json({
                            code: 200, msg: '请求成功！', data: JSON.parse(objStr3)
                        });
                        return;
                    }
                }
                await Increase_List(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    if (call.data) {
                        await redis.hSet("webmagic_api:Increase_List", request.query.id, JSON.stringify(call.data));
                    }
                    result.json(call);
                });
                break;
            case "4":
                //跌幅榜
                if (cacheOpen) {//是否打开缓存
                    let objStr4 = await redis.hGet("webmagic_api:Decrease_List", request.query.id);
                    if (objStr4 && objStr4 != "null") {
                        result.json({
                            code: 200, msg: '请求成功！', data: JSON.parse(objStr4)
                        });
                        return;
                    }
                }
                await Decrease_List(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    if (call.data) {
                        await redis.hSet("webmagic_api:Decrease_List", request.query.id, JSON.stringify(call.data));
                    }
                    result.json(call);
                });
                break;
            case "5":
                //成交榜
                if (cacheOpen) {//是否打开缓存
                    let objStr5 = await redis.hGet("webmagic_api:Transaction_List", request.query.id);
                    if (objStr5 && objStr5 != "null") {
                        result.json({
                            code: 200, msg: '请求成功！', data: JSON.parse(objStr5)
                        });
                        return;
                    }
                }
                await Transaction_List(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    if (call.data) {
                        await redis.hSet("webmagic_api:Transaction_List", request.query.id, JSON.stringify(call.data));
                    }
                    result.json(call);
                });

                break;
            case "6":
                //回报率榜
                break;
            case "7":
                //换手榜
                if (cacheOpen) {//是否打开缓存
                    let objStr7 = await redis.hGet("webmagic_api:Change_Hands_List", request.query.id);
                    if (objStr7 && objStr7 != "null") {
                        result.json({
                            code: 200, msg: '请求成功！', data: JSON.parse(objStr7)
                        });
                        return;
                    }
                }
                await Change_Hands_List(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    if (call.data) {
                        await redis.hSet("webmagic_api:Change_Hands_List", request.query.id, JSON.stringify(call.data));
                    }
                    result.json(call);
                });
                break;
            case "8":
                //"LP占比榜"
                break;
            case "9":
                //"市值榜"
                break;
            case "10":
                //"持币榜"
                break;
            case "11":
                //"新币榜"
                if (cacheOpen) {//是否打开缓存
                    let objStr11 = await redis.hGet("webmagic_api:New_Currency_List", request.query.id);
                    if (objStr11 && objStr11 != "null") {
                        result.json({
                            code: 200, msg: '请求成功！', data: JSON.parse(objStr11)
                        });
                        return;
                    }
                }
                await New_Currency_List(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    if (call.data) {
                        await redis.hSet("webmagic_api:New_Currency_List", request.query.id, JSON.stringify(call.data));
                    }
                    result.json(call);
                });
                break;
            case "12":
                //"自选"
                // if (cacheOpen) {//自选需要关闭缓存
                //     let objStr12 = await redis.hGet("webmagic_api:myself_choose", request.query.id);
                //     if (objStr12) {
                //         result.json({
                //             code: 200, msg: '请求成功！', data: JSON.parse(objStr12)
                //         });
                //         return;
                //     }
                // }
                await myself_choose(request, async function (call) {
                    await setBottomPoolVolume(call.data);
                    // await redis.hSet("webmagic_api:myself_choose", request.query.id, JSON.stringify(call.data));
                    result.json(call);
                });
                break;
        }
    } catch (e) {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    }
}

// module.exports = {
//     Hot_Search_List,
//     Trading_List,
//     New_Currency_List,
//     Increase_List,
//     Decrease_List,
//     Pool_List,
//     Transaction_List,
//     Change_Hands_List
// }

module.exports = {
    hotTop, getTokenBase
}

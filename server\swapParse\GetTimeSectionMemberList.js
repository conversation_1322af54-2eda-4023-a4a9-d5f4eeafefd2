const {Contract} = require('ethers')
const {ethers} = require('ethers')
const marketAbi = require("../abi/market.json")
const netbodyAbi = require("../abi/netbody.json")
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
const {userlist} = require("./userList");
const {mannalist} = require("./mannaList");
const rpc_listen = "https://bsc.blockpi.network/v1/rpc/156f516494b3a4024171a8045490d2d622e3a3b8"
const provider = new ethers.JsonRpcProvider(rpc_listen)
const marketAddress = "******************************************"
const netbodyAddresss = "******************************************"
const resultsList = [];
const sectionAddress = [
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "0xfB9292410460F92AE7C4F89C441c87b44F39cA31",
    "0x0a91e5EC70A2622A7EbF4A644a0fF7e5dFB1C57e",
    "0x7dBF30D395b00eE251B1CcC20342f64d5834323C",
    "0x2FCCd0eD3506c4667C006bdCD4c4E109b7d13268",
    "0x8CDEDC02C010442C6D331bCd28b91D663E2E06A7",
    "0xF598a0585Ecb9f098F86f0d0Fb01b92e0D0510f4",
]
async function main(netbody, market, address) {
    let result = await netbody.getNetBodyInfo(address)
    let list = result[2];
    list.forEach(async item => {
        let res = await market.identity(item);
        if (res[0] == 1) {
            let infos = {
                address: item,
                supperAddress: address,
                identity: res[0],
                strTime:res[3]
            }
            console.log("还在执行---", item)
            resultsList.push(infos);
            // resultsList.push(`${item},`);
        }
        await main(netbody, market, item)
    })
}

async function getMemberList() {

    const netbody = new Contract(netbodyAddresss, netbodyAbi, provider)
    const market = new Contract(marketAddress, marketAbi, provider)

    sectionAddress.forEach(async item => {
        let res = await market.identity(item);
        if (res[0] == 1) {
            let strTime;
            if(res[2]>0){
                strTime = res[2];
            }
            else {
                strTime = res[3];
            }
            let infos = {
                address: item,
                supperAddress: "0x4e9109D4d4726B04816f887Bb63dA661c5658d90",
                identity: res[0],
                strTime:strTime
            }
            // resultsList.push(`${item},`);
            resultsList.push(infos);
        }
        // await main(netbody, market, item)
    })
    setTimeout(() => {
        try {
            let allStr = JSONbig.stringify(resultsList)
            // redis.set("member_section_identity_list", allStr);
            console.log("存储结束", JSONbig.stringify(resultsList))
            resultsList.length = 0;
        } catch (e) {
            console.error("加载会员网体报错:", e);
        }
    }, 10000)
}

function buildTreeWithStats(data, targetAddress = null) {
    // 构建一个字典方便快速查找节点
    const map = new Map();
    data.forEach(item => {
        // 将地址转换为小写，避免大小写不一致问题
        const addressLower = item.address.toLowerCase();
        const supperAddressLower = item.supperAddress ? item.supperAddress.toLowerCase() : null;

        map.set(addressLower, {
            ...item,
            address: addressLower,  // 将原地址替换为小写地址
            supperAddress: supperAddressLower, // 将上级地址替换为小写地址
            children: [],
            identityOperatorCount: 0,
            identityVipCount: 0
        });
    });

    // 构造树
    const tree = [];
    data.forEach(item => {
        const addressLower = item.address.toLowerCase();
        const supperAddressLower = item.supperAddress ? item.supperAddress.toLowerCase() : null;

        // 处理子节点和父节点的关系，确保比较时地址是小写的
        if (map.has(supperAddressLower)) {
            map.get(supperAddressLower).children.push(map.get(addressLower));
        } else {
            tree.push(map.get(addressLower));
        }
    });

    // 递归统计子节点的 identity 总数（不包含自己）
    function calculateStats(node) {
        let identityOperatorCount = 0;
        let identityVipCount = 0;

        // 遍历子节点并累加统计数
        for (const child of node.children) {
            const childStats = calculateStats(child);

            identityOperatorCount += childStats.identityOperatorCount; // 子节点的 identity 统计
            identityVipCount += childStats.identityVipCount;
        }


        // 加上直接子节点的 identity 值
        // identityOperatorCount += node.children.filter(child => child.identity === 1).length;
        identityOperatorCount += node.children.filter(
            child => child.identity === 1 && child.strTime < 1735228800
        ).length;
        identityVipCount += node.children.filter(child => child.identity === 2).length;

        // 更新当前节点的统计数
        node.identityOperatorCount = identityOperatorCount;
        node.identityVipCount = identityVipCount;

        return { identityOperatorCount, identityVipCount };
    }

    // 如果传了 targetAddress，则只从指定地址开始统计
    if (targetAddress) {
        const targetNode = map.get(targetAddress.toLowerCase()); // 同样传入的 targetAddress 也要转换为小写
        if (!targetNode) {
            throw new Error(`Address ${targetAddress} not found in the data`);
        }
        calculateStats(targetNode);
        return targetNode; // 返回单一节点的树
    }

    // 对每个根节点递归计算统计数
    tree.forEach(root => calculateStats(root));

    return tree; // 返回整个树
}

async function getSectionList(){

    let json = await redis.get("member_identity_list");
    let jsonList= JSONbig.parse(json);
    // console.log("内容",jsonList)
    // 0x4e9109D4d4726B04816f887Bb63dA661c5658d90
    let data = buildTreeWithStats(jsonList);
    let newJsonList = [];
    data.forEach(item=>{
        // if(item.startTime > 1735228800 || item.identity!=1)return;
        let count = 0;
        for (let i = 0; i < jsonList.length; i++) {
            if(jsonList[i].supperAddress.toLowerCase()==item.address.toLowerCase() && jsonList[i].identity==1  && jsonList[i].startTime > 1735228800){
                count++;
            }
        }
        if(count>=6){
            newJsonList.push({
                address:item.address,
                count:count
            })
            // console.log(`address:${item.address} - count:${count}`)
        }
    })

    newJsonList.sort((a, b) => b.count - a.count);
    console.log("执行完毕",JSONbig.stringify(newJsonList))
    for (let i = 0; i < newJsonList.length; i++) {
        console.log(`address:${newJsonList[i].address} - count:${newJsonList[i].count}`)

    }
    // console.log(JSONbig.stringify(newJsonList));
}
getMemberList();
// getSectionList()

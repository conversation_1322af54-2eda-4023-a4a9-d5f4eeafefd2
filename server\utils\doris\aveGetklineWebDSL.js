const {BatchProcessor} = require("../AnalyzeUtils");
// Constants
const DORIS_HOST = '**************';
const DORIS_DB = 'asdic';
const DORIS_TABLE = 'getklineWebD';
const DORIS_USER = 'dev';
const DORIS_PASSWORD = 'dev!sonar';
const DORIS_HTTP_PORT = 48523;//这里是doris的http端口

function basicAuthHeader(username, password) {
    const toBeEncoded = username + ':' + password;
    return 'Basic ' + Buffer.from(toBeEncoded).toString('base64');
}

const loadUrl = `/api/${DORIS_DB}/${DORIS_TABLE}/_stream_load`;
const options = {
    hostname: DORIS_HOST,
    port: DORIS_HTTP_PORT,
    path: loadUrl,
    method: 'PUT',
    headers: {
        'Expect': '100-continue',
        'Authorization': basicAuthHeader(DORIS_USER, DORIS_PASSWORD),
        'Content-Type': 'text/plain; charset=utf-8',
        'column_separator': '|'
    }
};
const batchProcessor = new BatchProcessor({
    name:"getklineWebD",
    maxInterval: 5000, // 每30秒发送一次数据
    options:options
});
function slGetklineWebD(array, no) {
    batchProcessor.streamLoadAdd(array, no);
}

module.exports = {
    slGetklineWebD
}
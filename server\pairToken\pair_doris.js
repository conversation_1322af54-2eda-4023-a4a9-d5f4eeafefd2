const { ethers } = require('ethers');
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const { queryProvider } = require('../avi/factory_provider');
const provider = queryProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const { multicall, allAbi } = require("../abi");
const { slAvePairsOne } = require("../utils/doris/avePairsSL");
const { delay, isUsdt, isBusd, formatAddress } = require("../utils/functions");
const { } = require("../utils")
const key = "pair_address_data";
const key_doris = "pair_doris_data";
const {NumberToDate, formatTimestamp} = require("../utils/DateUtils");
async function batchCallContracts() {
    let flag = true;
    const contractAddress = '******************************************';
    // 使用 ABI 和地址创建合约实例
    const contract = new ethers.Contract(contractAddress, multicall, provider);
    let all_address = [];
    let all_function = [];
    let results = [];
    console.log("开始时间：", Date.now())
    while (flag) {
        try {
            //console.log(number);

            let pair_address = await redis.lPop(key_doris);

            if (pair_address) {
               
                let element = JSONbig.parse(pair_address);
                element['created_time'] = formatTimestamp(element.created_time);
                element['publish_time'] = formatTimestamp(element.publish_time);
                
                console.log(element);
                let row = `${element.address ? element.address : ''}|${element.created_time ? element.created_time : ''}|${element.symbol ? element.symbol : ''}|${element.token0_addr ? element.token0_addr : ''}|${element.token1_addr ? element.token1_addr : ''}|${element.swap_id ? element.swap_id : 1}|${element.decimals ? element.decimals : 18}|${element.created_block ? element.created_block : 0}|${element.chain_id ? element.chain_id : 0}|${element.created_hash ? element.created_hash : ''}|${element.reserve0 ? element.reserve0 : ''}|${element.reserve1 ? element.reserve1 : ''}|${element.pair_index ? element.pair_index : 0}|${element.token0_price_usd ? element.token0_price_usd : ''}|${element.token0_price_eth ? element.token0_price_eth : ''}|${element.token1_price_usd ? element.token1_price_usd : ''}|${element.token1_price_eth ? element.token1_price_eth : ''}|${element.reserve_usd ? element.reserve_usd : ''}|${element.path ? element.path : ''}|${element.token_addr ? element.token_addr : ''}|${element.token_index ? element.token_index : 0}|${element.status ? element.status : 1}|${element.holders ? element.holders : 0}|${element.tx_count ? element.tx_count : 0}|${element.tx_volume ? element.tx_volume : ''}|${element.tx_volume_usd ? element.tx_volume_usd : ''}|${element.tx_volume_u_24h ? element.tx_volume_u_24h : ''}|${element.tx_count_24h ? element.tx_count_24h : 0}|${element.tx_volume_24h ? element.tx_volume_24h : ''}|${element.price_change_24h ? element.price_change_24h : ''}|${element.token_confirm ? element.token_confirm : 0}|${element.open_price ? element.open_price : ''}|${element.open_time ? element.open_time : 0}|${element.is_open ? element.is_open : 0}|${element.total_supply ? element.total_supply : ''}|${element.create_time ? element.create_time : ''}|${element.update_time ? element.update_time : ''}|${element.lp_proportion ? element.lp_proportion : 0}|${element.publish_time ? element.publish_time : ''}|${element.open_tag ? element.open_tag : ''}`;
                console.log(row);
                slAvePairsOne(row, '');
                //await redis.rPush(key_doris,row);
                
            } else {

            }


        } catch (e) {
            console.log(e);
            flag = true;
            all_address = [];
            all_function = [];
            number = 0;
        }
    }
}

batchCallContracts();
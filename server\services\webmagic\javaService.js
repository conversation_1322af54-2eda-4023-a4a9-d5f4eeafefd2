const {getTokenPsqlUsdt} = require("./duneService");
// const {PqueryQL_sonar} = require("../../database/PqueryQL/PostgresSQL");
const https = require("https");
const {getHeaders} = require("./config/httpHead");
const {formatAddress} = require("../../utils/functions");
const vipAbi = require('../../abi/buyMemberAbi.json');
const {JsonRpcProvider, Contract, formatUnits} = require("ethers");
const constant = require("../../utils/constant");
const redis = require("../../database/redis");
const {default: BigNumber} = require("bignumber.js");
const {img_isExist} = require("./task/imgUpload");
const userSearchHistoryMysql = require("../../model/mysql/UserSearchHistoryModel");
const ave_url = "api.eskegs.com";

function decode(encode_data){
    return JSON.parse(global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")));
}

let provider = new JsonRpcProvider(constant.RPC_NEW, undefined, {polling: true});

/**
 * 检验response合法性
 * @param jsonData
 * @returns {Promise<boolean>}
 */
function checkData(jsonData){
    if(jsonData['msg'].indexOf("Authorization") > - 1){
        return true;
    } else if(jsonData['msg'].indexOf("failure") > - 1){
        return true;
    }
    return false;
}

/**
 * 获取Pk基本信息1
 */
async function getBase(address, call){
    try{
        let url = '/v1api/v3/tokens/' + address + "-bsc"
        const options = {
            hostname: ave_url, path: url, agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(), timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        let response = {};
        const req = https.get(options, function(res){
            res.on('data', function(chunk){
                html += chunk;
            });
            res.on('end', async function(){
                try{
                    let jsonData = JSON.parse(html);
                    if( ! jsonData['data']){
                        console.log("getPairBase:encode_data为空");
                        return null;
                    }
                    let data;
                    if(typeof jsonData['data'] === 'string'){
                        data = JSON.parse(jsonData['data']);
                    } else{
                        data = jsonData['data'];
                    }
                    let pairMax = data.pairs[0];
                    if(formatAddress(pairMax['token0_address']) == address){
                        response['reserve'] = pairMax['reserve0'];
                    } else{
                        response['reserve'] = pairMax['reserve1'];
                    }
                    response['current_price_usd'] = data['token']['current_price_usd'];//当前价格
                    response['tx_24h_count'] = pairMax['tx_24h_count'];//当前交易数
                    response['volume_u'] = pairMax['volume_u'];//当前交易额
                    call({
                        code: 200, msg: '请求成功！', data: response
                    });
                } catch(e){
                    call({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function(e){
                call({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', function(e){
            call({
                code: 500, msg: e, data: null
            })
        })
        req.end();
    } catch(e){
        call({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 获取Pk基本数据2
 * @param token
 * @param call
 * @returns {Promise<void>}
 */
async function getSecurity(token, call){
    try{
        if( ! token){
            call({
                code: 400, msg: 'token参数不能为空', data: null
            })
            return;
        }
        // https://febweb002mail.com/v1api/v3/tokens/contract?token_id=******************************************-bsc&type=token&user_address=
        const options = {
            hostname: ave_url,
            path: '/v1api/v2/tokens/contract?token_id=' + token + '-bsc&type=token&user_address=',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        const req = https.get(options, function(res){
            res.on('data', function(chunk){
                html += chunk;
            });
            res.on('end', function(){
                try{
                    let jsonData = JSON.parse(html);
                    if(jsonData['msg'].indexOf("Authorization") > - 1){
                        console.log("认证过期！");
                        call({
                            code: 200, msg: "认证过期！", data: null
                        });
                        return;
                    }
                    if(jsonData['data']){
                        let contract_data = jsonData['data']['token_contract']['contract_data'];
                        let map = {
                            base: {
                                buy_tax: Number(contract_data['buy_tax']) * 0.01,//换算为%小数字
                                sell_tax: Number(contract_data['sell_tax']) * 0.01,//换算为%小数字
                                sell_gas: contract_data['sell_gas'],
                                buy_gas: contract_data['buy_gas'],
                                slippage_modifiable: contract_data['slippage_modifiable'],
                                hidden_owner: contract_data['hidden_owner'],
                                has_white_method: contract_data['has_white_method']
                            }, poolAmount: contract_data['dex'][0]['liquidity'],//池子大小$
                            holder_count: contract_data['holders'],//token持币人数
                            holders: contract_data['token_holders_rank'],//token持币人
                            lp_holder_count: contract_data['pair_holders'],//lp持币人数
                            lp_holders: contract_data['pair_holders_rank'],//lp持币人
                            total: contract_data['total']//最大供应量
                        }
                        call({
                            code: 200, msg: "成功！", data: map
                        });
                    } else{
                        call({
                            code: 200, msg: '无数据！', data: null
                        });
                    }
                } catch(e){
                    call({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function(e){
                call({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch(e){
        call({
            code: 500, msg: e, data: null
        })
    }
}

async function pkSecurity(request, result){
    let thread = [];
    thread.push(new Promise((r, j) => {
        getSecurity(request.body.token, function(result){
            r({type: 'getSecurity', json: result});
        });
    }));
    thread.push(new Promise((r, j) => {
        getBase(request.body.token, function(result){
            r({type: 'getBase', json: result});
        });
    }));
    // thread.push(new Promise((r, j) => {
    //     getLpHold(request.body.token, function (result) {
    //         r({type: 'getLpHold', json: result});
    //     });
    // }));
    await Promise.allSettled(thread).then(async obj => {
        let resultThread = {};
        let responseJson = {};
        for(let i = 0; i < obj.length; i ++){
            resultThread[obj[i].value.type] = obj[i].value.json;
        }
        let transUser = 0;
        let sellGas = 0;
        let buyGas = 0;
        let poolAmount = 0;
        let poolCoin = 0;
        let rankCount = '<100';
        let lpHolders = 0;
        let lpRate = 0.0;
        let poolTokenAmount = 0;
        let isDiscardPermission = '-';
        let lpSum = 0;
        let htradeAmount24 = 0;
        let hVolume24 = 0;
        let hTradeNum24 = 0;
        let hTurnover24 = 0;
        if((resultThread['getBase'].code == 200) && resultThread['getBase']['data']){
            poolCoin = resultThread['getBase']['data'].reserve;
            let price = resultThread['getBase']['data'].current_price_usd;
            let reserve = resultThread['getBase']['data'].reserve;
            poolTokenAmount = new BigNumber(price).times(reserve).toFixed();
            htradeAmount24 = resultThread['getBase']['data'].volume_u;
            hVolume24 = new BigNumber(htradeAmount24).div(price).toFixed();
            hTradeNum24 = resultThread['getBase']['data'].tx_24h_count;
        }
        let total = 0;
        if((resultThread['getSecurity'].code == 200) && resultThread['getSecurity']['data']){
            transUser = resultThread['getSecurity']['data']['holder_count'];
            sellGas = resultThread['getSecurity']['data'].base.sell_gas;
            buyGas = resultThread['getSecurity']['data'].base.buy_gas;
            poolAmount = resultThread['getSecurity']['data'].poolAmount;
            lpHolders = resultThread['getSecurity']['data'].lp_holder_count;
            total = resultThread['getSecurity']['data'].total;//已经减去了黑洞(流通量)
            hTurnover24 = Number(new BigNumber(hVolume24).div(total).toFixed());
        }
        // if ((resultThread['getLpHold'].code == 200) && resultThread['getLpHold']['data']) {
        //     lpRate = Number(resultThread['getLpHold']['data'].lpRate);
        //     lpSum = resultThread['getLpHold']['data'].lpSum;
        // }
        responseJson['total'] = total;//'最大供应量'
        responseJson['transUser'] = transUser;//'持币人'
        responseJson['sellGas'] = sellGas;//'手续费(卖)'
        responseJson['buyGas'] = buyGas;//'手续费(买)'
        responseJson['poolAmount'] = poolAmount;//池子大小
        responseJson['poolCoin'] = poolCoin;//底池币
        //池子排行榜
        let rank = await redis.hGet("webmagic_api:Pool_List", '2');
        if(rank){
            let rankList = JSON.parse(rank);
            let index = 0;
            for(; index < rankList.length; index ++){
                if(formatAddress(rankList[index]['targetToken']) == request.body.token){
                    //如果找到了则返回、
                    rankCount = (index + 1).toString();//写入排名
                    break;
                }
            }
        }
        responseJson['rankCount'] = rankCount;
        //持有lp人数
        responseJson['lpHolders'] = lpHolders;
        //lp占比
        responseJson['lpRate'] = lpRate;
        //底池币价值
        responseJson['poolTokenAmount'] = poolTokenAmount;
        //权限丢弃
        responseJson['isDiscardPermission'] = isDiscardPermission;
        //lp质押
        responseJson['lpSum'] = lpSum;
        //交易额
        responseJson['htradeAmount24'] = htradeAmount24;
        //交易量
        responseJson['hVolume24'] = hVolume24;
        //交易数
        responseJson['hTradeNum24'] = hTradeNum24;
        //换手率->24H换手(24H换手=24H量/流通量（发行量-黑洞地址）)
        responseJson['hTurnover24'] = hTurnover24;
        result.json({
            code: 200, msg: '请求成功！', data: responseJson
        });
    }, e => {
        result.json({
            code: 500, msg: e, data: null
        })
    });
}

/**
 * token搜索栏目信息
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function findToken(request, result){
    try{
        if( ! request.body.query || ! request.body.user_id){
            result.json({
                code: 400, msg: 'query,uid参数不能为空', data: null
            })
            return;
        }
        /**
         * 记录搜索历史
         */
        let user_id= Number(request.body.user_id);
        let history = await userSearchHistoryMysql().findOne({
            where: {
                uid: user_id
            }
        });
        let nowDate=new Date();
        if(history){
            let keywords = history.dataValues.keywords;
            let keywordStr = request.body.query
            if(keywords!==undefined){
                let keywordList = keywords.split(",");
                keywordList.push(request.body.query);
                if(keywordList.length>0){
                    keywordList.slice(-20);
                }
                keywordStr = keywordList.join(',');
            }
            await history.update({
                keywords: keywordStr,
                update_time: nowDate
            });
        } else{
            await userSearchHistoryMysql().create({
                uid: user_id,
                keywords: request.body.query,
                create_time: nowDate,
                update_time: nowDate
            });
        }
        // let upper = request.body.query.toUpperCase();
        let lower = request.body.query.toLowerCase().trim();//where: {or: [{symbol: "${upper}"},{symbol: "${lower}"},{id: "${upper}"},  {id: "${lower}"}]},
        // let response = await GqueryQL(`{
        //               tokens(
        //                 where: {or: [{symbol_starts_with_nocase: "${lower}"},{id: "${lower}"}]},
        //                 orderBy: totalSupply, orderDirection: desc
        //               ) {
        //                 id
        //                 name
        //                 symbol
        //               }
        //             }`);
        const options = {
            hostname: 'api.aveapi.com', path: '/v1api/v2/tokens/query?keyword=' + lower, agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(), timeout: 60000 // 设置超时时间为5秒
        };
        // https://api.dryespah.com/v1api/v3/tokens/******************************************-bsc/extraDetail
        let html = ''
        let data = []
        const req = https.get(options, function(res){
            res.on('data', function(chunk){
                html += chunk;
            });
            res.on('end', function(){
                try{
                    let jsonData = JSON.parse(html);
                    if(checkData(jsonData)){
                        console.log("认证过期！");
                        result.json({
                            code: 200, msg: "认证过期！", data: null
                        });
                        return;
                    }
                    if(jsonData['data']){
                        let tokens = jsonData['data']['token_list'];
                        for(let i = 0; i < tokens.length; i ++){
                            tokens[i]['id'] = tokens[i].token;
                            tokens[i]['address'] = tokens[i].token;
                            if(img_isExist("./image/" + tokens[i]['address'] + ".png")){
                                tokens[i].logoUrl = constant.IMAGE_URL + tokens[i]['address'] + ".png";
                            } else{
                                tokens[i].logoUrl = "https://www.iconaves.com/token_icon/bsc/" + tokens[i]['address'] + ".png";
                            }
                            delete tokens[i].token;
                        }
                        result.json({
                            code: 200, msg: "成功！", data: tokens
                        });
                    } else{
                        result.json({
                            code: 200, msg: '无数据！', data: null
                        });
                    }
                } catch(e){
                    result.json({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function(e){
                result.json({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            result.json({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch(e){
        result.json({
            code: 200, msg: e, data: null
        })
    }
}

async function findWalletAddressU(request, result){
    try{
        if( ! request.body.address){
            result.json({
                code: 400, msg: 'address参数不能为空', data: null
            })
            return;
        }
        // const signer = provider.getSigner();
        const vipContract = new Contract(constant.buyVip_address, vipAbi, provider);
        const payTrade = await vipContract.identity(request.body.address);
        if(payTrade != null && payTrade.length > 0){
            let data = {
                identity: Number(payTrade[0]),//0普通，1会员，2运行商（会员）
                period: Number(payTrade[1]),//持续时间
                updateTime: Number(payTrade[2]),//更新时间
                startTime: Number(payTrade[3])//第一次充值时间
            }
            // for (let i = 0; i < payTrade.length; i++) {
            //     let pay = payTrade[i];
            //     let num = pay[0];
            //     let time = pay[1];
            //     num = Number(formatUnits(num, 18));
            //     time = Number(time);
            //     data[time] = {num: num, time: time};
            //     let next = i + 1;//获取下一个值的下标
            //     if (next < payTrade.length) {
            //         data[time]["next"] = Number(payTrade[next][1]);//链指向下一个值时间
            //     }
            // }
            result.json({
                code: 200, msg: '查询成功！', data: data
            });
        } else{
            result.json({
                code: 500, msg: '查询列表为空！', data: null
            });
        }
    } catch(e){
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

async function tokenSecurity(request, result){
    if( ! request.body.token){
        result.json({
            code: 400, msg: 'token参数不能为空', data: null
        })
        return;
    }
    // https://febweb002mail.com/v1api/v3/tokens/contract?token_id=******************************************-bsc&type=token&user_address=
    const options = {
        hostname: ave_url,
        path: '/v1api/v2/tokens/contract?token_id=' + request.body.token + '-bsc&type=token&user_address=',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };
    // https://api.dryespah.com/v1api/v3/tokens/******************************************-bsc/extraDetail
    let html = ''
    let data = []
    const req = https.get(options, function(res){
        res.on('data', function(chunk){
            html += chunk;
        });
        res.on('end', function(){
            try{
                let jsonData = JSON.parse(html);
                if(jsonData['msg'].indexOf("Authorization") > - 1){
                    console.log("认证过期！");
                    result.json({
                        code: 200, msg: "认证过期！", data: null
                    });
                    return;
                }
                if(jsonData['data']){
                    let contract_data = jsonData['data']['token_contract']['contract_data'];
                    let map = {
                        base: {
                            owner_change_balance: contract_data['owner_change_balance'],
                            selfdestruct: contract_data['selfdestruct'],//代币自毁灭
                            hidden_owner: contract_data['hidden_owner'],//隐藏所有者
                            external_call: contract_data['external_call'],//外部合约调用风险
                            is_honeypot: contract_data['is_honeypot'],//是否貔貅
                            buy_tax: Number(contract_data['buy_tax']) * 0.01,//换算为%小数字
                            sell_tax: Number(contract_data['sell_tax']) * 0.01,//换算为%小数字
                            sell_gas: contract_data['sell_gas'],
                            slippage_modifiable: contract_data['slippage_modifiable'], //has_white_method: contract_data['has_white_method'],
                            hidden_owner: contract_data['hidden_owner'],
                            owner: contract_data['owner'],
                            has_black_method: contract_data['has_black_method'],
                            has_mint_method: contract_data['has_mint_method'],
                            is_proxy: contract_data['is_proxy']
                        },
                        risk_score: contract_data.risk_score,
                        holder_count: contract_data['holders'],
                        holders: contract_data['token_holders_rank'],//token持币人
                        lp_holder_count: contract_data['pair_holders'],
                        lp_holders: contract_data['pair_holders_rank'],//lp持币人
                        pair_lock_percent: contract_data['pair_lock_percent']//锁仓占比
                    }
                    result.json({
                        code: 200, msg: "成功！", data: map
                    });
                } else{
                    result.json({
                        code: 200, msg: '无数据！', data: null
                    });
                }
            } catch(e){
                result.json({
                    code: 500, msg: e, data: null
                })
            }
        });
        res.on('error', function(e){
            result.json({
                code: 500, msg: e, data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500, msg: e, data: null
        })
    });
    req.end();
}

async function findUsdt(request, result){
    try{
        if( ! request.body.token){
            result.json({
                code: 400, msg: 'token参数不能为空', data: null
            })
            return;
        }
        let usdt = await getTokenPsqlUsdt(request.body.token);
        result.json({
            code: 200, msg: '请求成功', data: usdt
        })
    } catch(e){
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

module.exports = {
    findToken, tokenSecurity, pkSecurity, findWalletAddressU, findUsdt
}

const { ethers } = require('ethers');
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const {queryProvider} = require('../avi/factory_provider');
const provider = queryProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const { parseLogAbi, factoryAbi, allAbi,V3AllAbi } = require("../abi");
async function batchCallContracts(i,k,j) {
    while(true){
        console.log("线程"+i+"开始，数字是：",k);
        
        const contractAddress = '******************************************';

        // 使用 ABI 和地址创建合约实例
        const contract = new ethers.Contract(contractAddress, factoryAbi, provider);
        const args = [k]; // 函数的参数
        // 使用 callStatic 调用合约函数
        let result = await contract.allPairs(...args);//contract.callStatic[functionName](...args);//contract.allPairs(...args);//contract.callStatic[functionName](...args);
        await redis.hSet("pair_json_chain",result,'');
        console.log("线程"+i+"完成，数字是：",k);
        k++;
        if(k>=j){
            return;
        }
    }
    
    // 如果需要发送事务，则等待它们被挖掘
    // if (transactions.length > 0) {
    //     const txPromises = transactions.map(tx => tx.wait());
    //     await Promise.all(txPromises);
    // }
}

// 使用示例
// const contracts = "******************************************";
// const functionsAndArgs = ['allPairs', [0]];

// batchCallContracts(contracts, functionsAndArgs);
module.exports = {
    batchCallContracts
}
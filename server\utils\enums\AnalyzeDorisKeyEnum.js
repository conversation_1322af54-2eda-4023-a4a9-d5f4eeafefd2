const FirstAnalyzeEnum = {
    initial_read_err: 'initial_read_err',
    initial_json: 'initial_json',
    priceTime_init_err: 'select:priceTime_error',
    priceTime_init: 'select:priceTime',
    priceTime_lock: 'select:priceTime_lock',
    priceTime_start: 'select:priceTime_start',
    initial_json_error: 'initial_json_error',
    holerCount_error: 'holerCount_error',
    initial_json_transfer: 'initial_json_transfer',
    initial_json_transfer_error: 'initial_json_transfer_error',
    token_json: 'token_json',
    token_json_hdata: 'token_json_hdata',
    token_json_consumer: 'token_json_consumer',
    token_json_doris_consumer: 'token_json_doris_consumer',
    pair_json: 'pair_json',
    pair_json_hdata: 'pair_json_hdata',
    pair_json_consumer: 'pair_json_consumer',
    pair_path_consumer: 'pair_path_consumer',
    pair_qualified_table:'pair_qualified_table',
    pair_marketValue:'pair_marketValue',
    pair_task_vKey:'task:pair:v_key',
    token_task_vKey:'task:token:v_key'
}

module.exports = FirstAnalyzeEnum;
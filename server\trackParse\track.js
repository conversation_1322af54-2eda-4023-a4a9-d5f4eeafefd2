
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis_in");
const {getTokenAAndBPriceByTime} = require("../utils/klineUtils");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const {dbTradeQuery} = require('../model/mysql/AviTradeModel');
const {dbTransferQuery} = require('../model/mysql/AviTransferModel');
const {dbLiquidityQuery} = require('../model/mysql/AviLiquidityModel');
const {slAveTrade} = require('../utils/doris/aveTradeSL')
const key_all = "block_swap_list";
const { formatUnits } = require("ethers");
const { default: BigNumber } = require('bignumber.js');
const mapLimit = require('async/mapLimit');
const {StringToTimeStamp, formatTimestamp} = require("../utils/DateUtils");
async function startSwaps(user_address,token_address) {
    try{
        let token =await redis.hGet("token_json_hdata",token_address);
        if(token){
            let start = Date.now();
            token = JSONbig.parse(token);
            console.log("开始查询",start)
            if (token.pair_address) {
                let pair_addr = JSONbig.parse(await redis.hGet("pair_qualified_table", token.pair_address));
                if(pair_addr){
                    //根据pair查询出用户相对应的交易和池子记录
                    let tradeList = await dbTradeQuery([{
                        type: "string",
                        column: "address",
                        value: pair_addr.address
                    }, {
                        type: "string",
                        column: "wallet_address",
                        value: user_address
                    }], [{
                        type: "desc",
                        column: "block_time",
                    }, {
                        type: "desc",
                        column: "log_index",
                    }]);
                    console.log("swap查询结束",Date.now()-start)
                    let transfer_from = await dbTransferQuery([{
                        type: "string",
                        column: "token_address",
                        value: token.address
                    }, {
                        type: "string",
                        column: "wallet_from_address",
                        value: user_address
                    }], [{
                        type: "desc",
                        column: "block_time",
                    }, {
                        type: "desc",
                        column: "log_index",
                    }]);
                    console.log("transfer查询结束",Date.now()-start)

                    let transfer_to = await dbTransferQuery([{
                        type: "string",
                        column: "token_address",
                        value: token.address
                    }, {
                        type: "string",
                        column: "wallet_to_address",
                        value: user_address
                    }], [{
                        type: "desc",
                        column: "block_time",
                    }, {
                        type: "desc",
                        column: "log_index",
                    }]);
                    console.log("transfer2查询结束",Date.now()-start)
                    let liquidityList = await dbLiquidityQuery([{
                        type: "string",
                        column: "address",
                        value: pair_addr.address
                    }, {
                        type: "string",
                        column: "wallet_address",
                        value: user_address
                    }], [{
                        type: "desc",
                        column: "block_time",
                    }, {
                        type: "desc",
                        column: "log_index",
                    }]);
                    console.log("liquidity查询结束",Date.now()-start)
                    let transferList = transfer_from.concat(transfer_to);
                    
                    transferList.sort(function(a, b) {
                        // 将日期字符串转换为日期对象进行比较
                        let dateA = new Date(a.block_time), dateB = new Date(b.block_time);
                        return dateB - dateA;  // 降序排序
                        // 对于升序，可以使用 return dateA - dateB;
                    });
                    console.log("总耗时长",(Date.now()-start)/1000,"秒")
                    let data_res = {
                        transferList:transferList,
                        liquidityList:liquidityList,
                        tradeList:tradeList,
                    }
                    console.log(data_res)
                }
            }
        }
        
     
    }catch(ex){
        console.log(ex);
    }
    
    

}

startSwaps('0xb8c3405905f02c9c67a5129d23303202f53c239b','0x82030cdbd9e4b7c5bb0b811a61da6360d69449cc');

module.exports={
    startSwaps
}
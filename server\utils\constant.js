/**
 * 描述: 自定义常量
 * 作者: <PERSON>
 * 日期: 2020-06-20
 */


module.exports = {
    CODE_TOKEN_EXPIRED: 401, // 授权失败
    PRIVATE_KEY: 'asdicavi', // 自定义jwt加密的私钥
    JWT_EXPIRED: 60 * 60 * 24, // 过期时间24小时

    POOL_ACTIVE_EXPIRD: 60 * 60 * 24,//交易对地址白名单失效时间 单位秒

    YZ_RPC: "https://bsc.linknode.vip/da990b36c68a67dc7eba4ecf9ee110064247e958/",//"https://bsc.blockpi.network/v1/rpc/cbf0342d944f263f140ce283f427fa8b33707296",
    JP_RPC: "https://jp-lx8-bsc.linknode.vip/b935507c-953b-4499-bb62-91fbc17dd2dd/",
    BLOCK_RPC: "https://bsc.blockpi.network/v1/rpc/cbf0342d944f263f140ce283f427fa8b33707296",
    // RPC:"https://bsc.getblock.io/3ed78a75-6791-4650-b640-e8e2345165d5/mainnet/",
    RPC: "http://*************/http/6663f0990000f95729b88cdb926a888",
    RPC_Nginx: "http://*************:9898/http/6663f0990000f95729b88cdb926a888",
    // http://*************/http/6663f0990000f95729b88cdb926a888//最新
    // RPC:"https://bsc.blockpi.network/v1/rpc/cbf0342d944f263f140ce283f427fa8b33707296",
    RPC_GUIDANG: "https://bsc.blockpi.network/v1/rpc/6046ca02b7d1dbe6c70fa74ae24bf74c5f0ea79d",//"https://bsc.blockpi.network/v1/rpc/edd6ad961d7810a0de50807c4ec64b6b580a446c",
    //RPC_NEW:"https://go.getblock.io/9bea37efb6564155835f9fc5811ee6c4",
    RPC_LINSHI: "https://bsc.blockpi.network/v1/rpc/cbf0342d944f263f140ce283f427fa8b33707296",
    RPC_NEW: "http://***********:8545",//https://bsc.blockpi.network/v1/rpc/156f516494b3a4024171a8045490d2d622e3a3b8"https://bsc.blockpi.network/v1/rpc/edd6ad961d7810a0de50807c4ec64b6b580a446c",
    RPC_DATA: "https://bsc.blockpi.network/v1/rpc/cbf0342d944f263f140ce283f427fa8b33707296",
    RPC_WSS: "wss://bsc.blockpi.network/v1/ws/edd6ad961d7810a0de50807c4ec64b6b580a446c",
    //RPC: "https://bsc.blockpi.network/v1/rpc/123096eafbcc48eec56b65b055f2a56e52e4774c",//RPC地址
    PANCAKE_FACTORY: "0xca143ce32fe78f1f7019d7d551a6402fc5350c73",//Pancake工厂合约地址
    PANCAKE_FACTORY_V3: "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865",//Pancake工厂v3合约地址
    PANCAKE_ROUTER: "0x10ed43c718714eb63d5aa57b78b54704e256024e",//路由地址
    PANCAKE_SMART_ROUTER: "0x13f4ea83d0bd40e75c8222255bc855a974568dd4",//智能路由

    //数量太少
    // UNISWAP_FACTORY_V2:"0x720767Aea828a9730123679f3cE3DE01369b4575",//UNISWAP 工程地址
    // UNISWAP_ROUTER_V2: "0x788B8EcE56F2C0eD41f31D7cd172276addCD7F99",//UniswapV2Router02
    //UNISWAP_UNIVERSAL_ROUTER: "0xeC8B0F7Ffe3ae75d7FfAb09429e3675bb63503e4",//UniversalRouter 通用路由
    UNISWAP_FACTORY_V3: "0xdB1d10011AD0Ff90774D0C6Bb92e5C5c8b4461F7",
    UNISWAP_ROUTER_V3: "0x7b8A01B39D58278b5DE7e48c8449c9f4F5170613",

    USDC_ADDRESS: "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d",
    TUSD_ADDRESS: "0x40af3827f39d0eacbf4a168f8d4ee67c121d11c9",
    USDT_ADDRESS: "0x55d398326f99059ff775485246999027b3197955",//USDT地址
    BNB_ADDRESS: "0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c",//wbnb
    BUSD_ADDRESS: "0xe9e7cea3dedca5984780bafc599bd69add087d56",

    BNB_BUSD_POOL: "0x58F876857a02D6762E0101bb5C46A8c1ED44Dc16",//BNB-BUSD 的交易对
    buyVip_address: "0x90253B545d649268979A44CE09095155a90dfc2b",//购买会员合约

    TOKEN_CACHE_KEY: "token_hash_cache",
    PAIR_CACHE_KEY: "pair_hash_cache",
    PAIR_TX_COUNT_CACHE_KEY: "pair_tx_count_hash_cache",
    STABLE_TOKEN_KEY: "stable_token_cache",
    SWAP_CACHE_KEY: "swap_cache",

    IMAGE_URL: "https://reps.sonarcenter.xyz/image/"
} 
const {
    formatUnits,
    JsonRpc<PERSON><PERSON><PERSON>,
    Contract,
    <PERSON><PERSON>,
    MaxInt256,
    parseEther,
    parseUnits,
    ethers,
    Interface
} = require("ethers");
const UserWalletRechargeModel = require("../../model/mysql/UserWalletRechargeModel");
const constant = require("../../utils/constant");
const {pairAbi} = require("../../abi");
const BigNumber = require("bignumber.js");
const giftAbi = require("../../abi/manaAbi.json");
const BlackListTextModel = require("../../model/mysql/BlackListTextModel");
const GiftCardModel = require("../../model/mysql/GiftCardModel");
const provider = new JsonRpcProvider(constant.RPC_NEW, undefined, {polling: true});
let zhongxingAddress = "******************************************";//我的地址******************************************，中心地址账户******************************************
let zhongxingPrivateKey = "a8559c76778cba23bbcab5be5685f3fc3e6b163e9150cc00d4658673ef245589";//我的密钥a8559c76778cba23bbcab5be5685f3fc3e6b163e9150cc00d4658673ef245589
// const asdicTokenAddress = "******************************************";//asdic地址（******************************************）
const mannaTokenAddress = "******************************************";//manna地址(甘露)(******************************************)
const redis = require("../../database/redis");
const {delay} = require("../../utils/functions");

const zWallet = new Wallet(zhongxingPrivateKey, provider);//中心地址钱包
const zxMannaContract = new Contract(mannaTokenAddress, pairAbi, zWallet);//中心地址的asdic合约
/**
 * 生成红包二维码
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getRandomWallet(request, result) {
    try {
        if (!request.body.userId) {
            result.json({
                code: 400, msg: 'userId参数不能为空', data: null
            })
            return;
        }
        let userId = Number(request.body.userId);
        // 创建一个随机钱包实例
        let randomWallet = ethers.Wallet.createRandom();

        let [uwr, isOk] = await UserWalletRechargeModel().findOrCreate({
            where: {
                user_id: userId
            },
            defaults: {
                user_id: userId,
                address: randomWallet.address,
                private_key: randomWallet.privateKey
            },
        });
        if (uwr) {
            result.json({
                code: 200, msg: '获取二维码成功', data: uwr.address
            })
            //do Some things
        } else {
            result.json({
                code: 500, msg: '充值二维码获取失败', data: null
            })
        }
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }

}

//java后台任务
async function getWalletBalanceTask(request, result) {
    try {
        if (!request.body.userId) {
            result.json({
                code: 400, msg: 'walletAddress参数不能为空', data: null
            })
            return;
        }
        let uwr = await UserWalletRechargeModel().findOne({
            where: {
                user_id: parseInt(request.body.userId)
            }
        });
        if (uwr) {
            let walletAddress = uwr.address;
            let customerPrivateKey = uwr.private_key;
            // zhongxingAddress = walletAddress;
            // zhongxingPrivateKey = customerPrivateKey;
            // walletAddress = "******************************************";
            // customerPrivateKey = "fc5f9103841229cac2ed125897ef453e693f54abc1c23268a7a26d3cae239039";
            /**
             * 1、数据库去地址
             * 2、轮询地址余额
             * 3、有余额，生成钱包，判断授权，条件判断有主币，没有则转入主币
             * 4、转走余额
             * 5、上分
             * @type {string}
             */
            let balance = await zxMannaContract.balanceOf(walletAddress);//判断余额
            if (balance > 0) {//如果没有授权需要先授权
                let lock = false;
                while (true) {//上锁，一个账户只能发生一次交易，并发会有问题
                    lock = await redis.setNX_PX("getWalletBalanceTask:lock", walletAddress, 86400000);//设置redis锁
                    if (!lock) {
                        console.info("等待中.." + walletAddress)
                        await delay(3000);
                    } else {
                        break;
                    }
                }
                let nxpx = await redis.setNX_PX("getWalletBalanceTask:" + walletAddress, balance.toString(), 86400000);//设置redis锁
                if (nxpx) {
                    let owance = await zxMannaContract.allowance(walletAddress, zhongxingAddress);
                    if (owance == 0) {//如果没有授权过
                        let txResponse = await zWallet.sendTransaction({//给walletAddress装入gas费用
                            to: walletAddress,
                            value: parseEther("0.0001")
                        });
                        let result = await txResponse.wait()
                        if (result.status == 1) {
                            let cWallet = new Wallet(customerPrivateKey, provider);
                            let customerContract = new Contract(mannaTokenAddress, pairAbi, cWallet);//客户地址的manna合约
                            let approveResult = await customerContract.approve(zhongxingAddress, MaxInt256);//授权(MaxUint256是最大值)
                            await approveResult.wait();
                        }
                    }
                    let transferFromResult = await zxMannaContract.transferFrom(walletAddress, zhongxingAddress, balance);//将钱转入中心地址
                    await transferFromResult.wait();
                    await redis.remove("getWalletBalanceTask:lock");//设置redis锁
                    result.json({
                        code: 200, msg: '转账成功', data: {address: walletAddress, balance: formatUnits(balance, 18)}//转换精度
                    })
                }
            } else {
                result.json({
                    code: 500, msg: '余额为0', data: null
                })
            }
        } else {
            result.json({
                code: 500, msg: '没有此用户' + request.body.userId, data: null
            })
        }
    } catch (e) {
        console.error(e);
        result.json({
            code: 500, msg: e.toString(), data: null
        })
    }
}


async function getWalletBalance(request, result) {
    try {
        if (!request.body.hash) {
            result.json({
                code: 400, msg: 'hash参数不能为空', data: null
            })
            return;
        }
        let hash = request.body.hash;
        let hashObj = await provider.getTransaction(hash);
        let giftInterface = new Interface(giftAbi);
        let data = giftInterface.parseTransaction(hashObj);//解析函数transfer(address to,uint256 amount)
        let to = data.args[0];//to地址
        let amount = data.args[1];//数量

        if (to != zhongxingAddress) {//如果是往中心地址充值
            result.json({
                code: 500, msg: '充值地址不正确！', data: null
            });
            //记录黑名单
            await BlackListTextModel().findOrCreate({
                where: {
                    hash: hash
                },
                defaults: {
                    hash: hash
                }
            });
            return;
        } else {
            result.json({
                code: 200, msg: '转账成功', data: {balance: formatUnits(amount, 18)}//转换精度
            })
        }
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }

}

module.exports = {
    getRandomWallet, getWalletBalance, getWalletBalanceTask
}

const TransModel = require("../model/mysql/TransModel");
const TransUserModel = require("../model/mysql/TransUserModel");
const TokenModel = require("../model/mysql/TokenModel");
const {default: BigNumber} = require("bignumber.js");
const {getStartOfDayTimestamp, formatQuantityTh, delay} = require('../utils/functions');
const {getAddressType} = require('../utils/EtherUtils');
const {getBalance} = require('../utils/EtherUtils');
const PairService = require('../services/pairService');
const PairModel = require("../model/mysql/PairModel");
const {getTokenAAndTokenBPriceUsdByPath} = require("../services/SyncService");
const UserDayModel = require("../model/mysql/UserDayModel");
const UpdateTokenHolderJob = require("../jobs/UpdateTokenHolderJob");
const UpdateTransUserJob = require("../jobs/UpdateTransUserJob");
const tokenService = require("../services/tokenService");
const AddressTypeEnum = require("../utils/enums/AddressTypeEnum");
const {Op} = require('sequelize');


async function getPairByToken(token) {
    if (!token.pair_id) {
        pairInfo = await PairService.getTokenMaxReservePair(token.id);
        if (pairInfo) {
            await token.update({
                pair_id: pairInfo.pair_id
            });
        }
        return pairInfo;
    }

    return await PairModel().findByPk(token.pair_id, {
        attributes: ['id', 'address', 'token_id', 'token0_id', 'token1_id', 'reserve_usd', 'path'],
    });
}

async function updateTransferUser(row, walletAddress, token, blockTime, amountUsd) {
    let transferUser = await TransUserModel().findOne({
        where: {
            address: walletAddress,
            token_id: token.id,
        }
    });

    // 获取设置后的时间戳
    const startOfDayTimestamp = getStartOfDayTimestamp(blockTime);

    //当前时间
    const nowTimestamp = new Date().getTime();
    const nowTime = Math.floor(nowTimestamp / 1000);
    const todayStartTimeStamp = getStartOfDayTimestamp(nowTime);

    const isToday = startOfDayTimestamp == todayStartTimeStamp;

    let balance;
    if (!transferUser) {
        const totalSupply = new BigNumber(token.total_supply);
        let balance = await getBalance(token.address, walletAddress, token.decimals);
        let addresstype = await getAddressType(walletAddress);

        const quantityFormat = formatQuantityTh(balance);
        let proportion = totalSupply.isZero ? new BigNumber(0) : new BigNumber(balance).div(totalSupply);
        proportion = proportion.comparedTo(100) > 0 ? new BigNumber(100) : proportion;
        let userFrom = {
            address: walletAddress,
            current: balance,
            current_price: 0,
            cost_price: 0,
            profit_loss: 0,
            proportion: proportion.toFixed(20),
            token_id: token.id,
            three_bo: 0,
            type: addresstype,
            quantity: quantityFormat.quantity,
            quantity_40th: quantityFormat.quantity_40th,
            publish_time: nowTime,
        };

        let [findTransUser, created] = await TransUserModel().findOrCreate({
            where: {
                address: walletAddress,
                token_id: token.id,
            },
            defaults: userFrom,
        }).catch(error => {
            console.error("balance", balance);
            console.error("totalSupply", token.total_supply);
            console.error("proportion", proportion.toString());
            console.error(error);
        });
        transferUser = created ? userFrom : findTransUser;
    }


    let userDay = await UserDayModel().findOne({
        where: {
            time: startOfDayTimestamp,
            address: walletAddress,
            token_id: token.id,
        }
    });

    if (!userDay) {
        let createData = {
            time: startOfDayTimestamp,
            address: walletAddress,
            type: transferUser.type,
            dec_num: row.quantity,
            token_id: token.id,
            dec_money: amountUsd.toFixed(30),
        };

        if (startOfDayTimestamp == todayStartTimeStamp) {
            if (!balance) balance = await getBalance(token.address, walletAddress, token.decimals);
            if (balance) {
                createData.user_num = balance;
                createData.publish_time = nowTime;
                createData.is_publish = 1;

                if (transferUser.publish_time < nowTime && transferUser.current != balance) {
                    const quantityFormat = formatQuantityTh(balance);
                    await transferUser.update({
                        //缺失transferUser.current
                        quantity: quantityFormat.quantity,
                        quantity_40th: quantityFormat.quantity_40th,
                        publish_time: nowTime,
                    });
                }
            }
        }
        let [findUserDay, created] = await UserDayModel().findOrCreate({
            where: {
                time: startOfDayTimestamp,
                address: walletAddress,
                token_id: token.id,
            },
            defaults: createData,
        });
        if (!created) {
            await updateUserDay(findUserDay, row, token, transferUser, amountUsd, nowTime, walletAddress, balance, isToday);
        }
    } else {
        await updateUserDay(userDay, row, token, transferUser, amountUsd, nowTime, walletAddress, balance, isToday);
    }
    //console.warn("完成updateTransferUser",walletAddress);
}

async function updateUserDay(userDay, row, token, transferUser, amountUsd, nowTime, walletAddress, balance, isToday) {
    const quantity = new BigNumber(row.quantity).plus(userDay.dec_num);
    const toAmountUsd = new BigNumber(amountUsd).plus(userDay.dec_money);
    let updateData = {
        aec_num: quantity.toFixed(30),
        aec_money: toAmountUsd.toFixed(30),
    };
    if (isToday && userDay.publish_time < nowTime) {
        if (!balance) balance = await getBalance(token.address, walletAddress, token.decimals);
        if (balance) {
            updateData.user_num = balance;
            updateData.publish_time = nowTime;
            updateData.is_publish = 1;
            if (transferUser.publish_time < nowTime && transferUser.current != balance) {
                const quantityFormat = formatQuantityTh(balance);
                await transferUser.update({
                    quantity: quantityFormat.quantity,
                    quantity_40th: quantityFormat.quantity_40th,
                    publish_time: nowTime,
                });
            }
        }
    }

    //const timeKey = "更新userday" + Math.random();
    //console.time(timeKey);
    await userDay.update(updateData);
    // console.timeEnd(timeKey);
}

/**
 * 保存转账数据
 * @param {*} row
 * @returns
 */
async function saveTransfer(row) {
    //console.warn("saveTransfer开始"+row.trans_hash+"-",row.log_index);
    //id  pair_id address holders lp_holder_count decimals total_supply
    // let token = await TokenModel().findByPk(row.token_id,{
    //     attributes: ['id','pair_id','address','lp_holder_count','total_supply','decimals','holders'],
    // });
    let token = await tokenService.getCacheTokenByAddress(row.token_address);
    if (!token) {
        console.log("获取失败");
        return;
    }
    //path 
    //const pairInfo = await getPairByToken(token);
    const pairInfo = await PairService.getCachePairById(token.pair_id);
    let currentPriceUsd = 0;
    if (pairInfo) {
        //获取该币的当前价格
        let priceUsdData = await getTokenAAndTokenBPriceUsdByPath(pairInfo, row.block_number, row.log_index);
        currentPriceUsd = priceUsdData.tokenAPriceUsd;
    }

    const quantityFormat = formatQuantityTh(row.quantity);
    //计算交易金额
    let amountUsd = new BigNumber(row.quantity).times(currentPriceUsd);
    let transData = {
        from_address: row.from_address,
        to_address: row.to_address,
        tranc_time: row.block_time,
        num: row.quantity,
        amount_usd: amountUsd.toFixed(30),
        token_id: token.id,
        user_from: row.wallet_from,
        user_to: row.wallet_to,
        trans_hash: row.trans_hash,
        log_index: row.log_index,
        quantity: quantityFormat.quantity,
        quantity_40th: quantityFormat.quantity_40th,
    };

    let isAddHolders = false;

    let [transfer, created] = await TransModel().findOrCreate({
        where: {
            token_id: token.id,
            trans_hash: row.trans_hash,
            log_index: row.log_index,
        },
        defaults: transData // 用于创建记录的默认值
    });
    if (created) {
        //有需要更新的吗
        isAddHolders = true;
    }

    await updateTransferUser(row, transData.user_from, token, row.block_time, amountUsd);
    await updateTransferUser(row, transData.user_to, token, row.block_time, amountUsd);

    //更新持币人数
    //if(isAddHolders && token.pair_id){
    updateTokenHolder({
        tokenId: token.id,
        pairId: token.pair_id,
        lpHolderCount: token.lp_holder_count,
        holders: token.holders,
    });

    addUpdateTransferUserJob({
        tokenId: token.id,
        address: row.wallet_from,
        addressType: 0,
    });
    //}
    //console.warn("完成"+row.trans_hash+"-",row.log_index);
}

async function batchSaveTransfer(list) {
    const blockTime = list[0].block_time;
    let tokenListMap = {};
    let transHashs = {};
    for (const item of list) {
        if (!tokenListMap[item.token_address]) {
            tokenListMap[item.token_address] = [];
        }
        tokenListMap[item.token_address].push(item);
    }
    let transferList = [];

    for (const tokenAddr in tokenListMap) {
        let token = await tokenService.getCacheTokenByAddress(tokenAddr);
        if (!token) {
            console.log("获取token失败");
            continue;
        }
        transHashs[token.id] = [];
        const pairInfo = await PairService.getCachePairById(token.pair_id);
        const rows = tokenListMap[tokenAddr];
        for (const row of rows) {
            transHashs[token.id].push(row.trans_hash)
            let currentPriceUsd = 0;
            if (pairInfo) {
                //获取该币的当前价格
                let priceUsdData = await getTokenAAndTokenBPriceUsdByPath(pairInfo, row.block_number, row.log_index);
                currentPriceUsd = priceUsdData.tokenAPriceUsd;
            }
            const quantityFormat = formatQuantityTh(row.quantity);
            //计算交易金额
            let amountUsd = new BigNumber(row.quantity).times(currentPriceUsd);
            let transData = {
                from_address: row.from_address,
                to_address: row.to_address,
                tranc_time: row.block_time,
                num: row.quantity,
                amount_usd: amountUsd.toFixed(30),
                user_from: row.wallet_from,
                token_id: token.id,
                trans_hash: row.trans_hash,
                user_to: row.wallet_to,
                log_index: row.log_index,
                quantity: quantityFormat.quantity,
                quantity_40th: quantityFormat.quantity_40th,
            };
            transferList.push(transData);
        }
    }

    for (const key in transHashs) {
        //先删除后创建
        let obj = transHashs[key];
        await TransModel().destroy({
            where: {
                // 这里指定你的条件，例如：
                token_id: {
                    [Op.eq]: key
                },
                trans_hash: {
                    [Op.in]: obj // value1, value2, ... 是你想要删除的记录的ID列表
                }
            }
        })
    }
    //保存到ave_trans表
    const successList = await TransModel().bulkCreate(transferList, {ignoreDuplicates: true})
        .catch((error) => {
            console.error('插入失败:', error);
        });

    let transUserMap = {};
    for (const transfer of successList) {
        if (!transUserMap[transfer.token_id]) {
            transUserMap[transfer.token_id] = {}
        }
        if (!transUserMap[transfer.token_id][transfer.user_from]) {
            transUserMap[transfer.token_id][transfer.user_from] = 1;
        }
        if (!transUserMap[transfer.token_id][transfer.user_to]) {
            transUserMap[transfer.token_id][transfer.user_to] = 1;
        }
    }

    for (const tokenId in transUserMap) {
        // if (!transUserMap.hasOwnProperty(tokenAddr)) {
        //     continue;
        // }
        for (const address in transUserMap[tokenId]) {
            // if (!transUserMap[tokenAddr].hasOwnProperty(address)) {
            //     continue;
            // }
            await doUpdateTransferUser(address, tokenId, blockTime);
        }
    }
}

/**
 *
 * @param {String} address
 * @param {Number} tokenId
 * @param {Number} blockTime
 */
async function doUpdateTransferUser(address, tokenId, blockTime) {
    let transferUser = await TransUserModel().findOne({
        where: {
            address: address,
            token_id: tokenId,
        }
    });

    let addressType = AddressTypeEnum.WALLET;
    if (transferUser) {
        if (transferUser.publish_time > blockTime) {
            return;
        }
        addressType = transferUser.type;
    } else {
        addressType = await getAddressType(address);
    }

    await addUpdateTransferUserJob({
        tokenId,
        address,
        addressType,
    });
}

async function addUpdateTransferUserJob(data) {

    /** @type { UpdateTransUserJob } */
    const job = new UpdateTransUserJob();
    job.addJobLockHandler(data);
}

async function updateTokenHolder(data) {
    /** @type { UpdateTokenHolderJob } */
    const job = new UpdateTokenHolderJob();
    job.addJobLockHandler(data);
}

module.exports = {
    saveTransfer,
    batchSaveTransfer,
    updateTokenHolder,
}
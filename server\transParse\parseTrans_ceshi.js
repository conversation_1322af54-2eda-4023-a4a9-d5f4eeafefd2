
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis_in");
const { getTokenAAndBPriceByToken } = require("../utils/klineUtils");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const { dbTransferQuery } = require('../model/mysql/AviBaseTransferModel');
//const {slAveTrade} = require('../utils/doris/aveTradeSL')
const {slAveTransfer} = require('../utils/doris/aveTransferSL')
const {slAveTransferUser} = require('../utils/doris/aveTransferUserSL')
const key_all = "block_trans_list";
const { formatUnits } = require("ethers");
const { default: BigNumber } = require('bignumber.js');
const mapLimit = require('async/mapLimit');
const { StringToTimeStamp, formatTimestamp } = require("../utils/DateUtils");
async function startSwaps() {
    try {

        let block_listen = await redis.lPopCount(key_all,10);
        //await redis.lPush(key_all,block_listen);
        if (!block_listen) {
            
        }
        console.log(block_listen);
        console.log("完成")
            

    } catch (ex) {
        console.log(ex);
    }



}

startSwaps();

module.exports = {
    startSwaps
}
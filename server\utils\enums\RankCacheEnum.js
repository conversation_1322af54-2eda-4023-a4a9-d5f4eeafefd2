const RankCacheEnum = {
    /** 新币榜 */
    NEW_RANK: 'rank:new_rank_cache',//新币榜
    /** 池子榜 */
    POOL_RANK: 'rank:pool_rank_cache',//池子榜 池子大小排行榜（底池的U乘以2）
    /** 涨幅榜 */
    CHG_RANK: 'rank:chg_rank_cache',//涨幅榜 
    /** 成交榜 */
    TRADE_RANK: 'rank:trade_rank_cache',//成交榜 【交易额的大小】
    /** 回报率榜 */
    ROI_RANK: 'rank:roi_rank_cache',// 回报率榜 现价/发行价*100% 
    /** 转手率榜 */
    TURNOVER_RANK: 'rank:turnover_rank_cache',// 转手率榜 成交量/最大供应量*100% 最大供应量=发行量-销毁量
    /** 占比榜 */
    PROPORTION_RANK: 'rank:proportion_rank_cache',// 占比榜 LP人数/持币人数*100%
    /** 市值榜 */
    CAP_RANK: 'rank:cap_rank_cache',// 市值榜 现币价*最大供应量 
    /** 持币榜 */
    HOLD_RANK: 'rank:hold_rank_cache',//持币榜 持币人数多少
}

module.exports = RankCacheEnum;
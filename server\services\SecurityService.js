const {GoPlus, ErrorCode} = require("@goplus/sdk-node");
const TokenModel = require("../model/mysql/TokenModel");
const {delay} = require("../utils/functions");
const PairModel = require("../model/mysql/PairModel");

/**
 * 描述: 业务逻辑处理 - 检测相关接口
 * 作者: Jack Chen
 * 日期: 2020-06-20
 */
//检测
async function checkToken(chainId, address) {
    let addresses = [address];
    let result = await GoPlus.tokenSecurity(chainId, addresses, 30);
        console.log("tokenSecurity"+JSON.stringify(result));
    if (result.code != ErrorCode.SUCCESS) {
        console.error(result);
        await delay(5000);
        throw new Error("获取失败");
    } else {
        return result.result[address];
    }
}

function addRiskScore(riskScore, riskLevel, score) {
    if (riskLevel <= 2 && riskScore < 60) {
        return riskScore + score;
    } else if (riskLevel == 3 && riskScore < 100) {
        riskScore += score;
        return Math.min(riskScore, 100);
    }
    return riskScore;
}

function getRiskScore(data, address) {
    let riskScore = 20;
    let riskLevel = 1;
    //如果所有权可以收回，则返回“1”
    if (data.can_take_back_ownership == 1) {
        riskLevel = 2;
        console.info("----------------------------------果所有权可以收回");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //未丢弃管理权限
    if (Number(data.owner_address) != 0) {
        riskLevel = 2;
        console.info("----------------------------------未丢弃管理权限");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //合约未开源，可能存在漏洞机制
    if (data.is_open_source == 0) {
        riskLevel = 3;
        console.info("----------------------------------约未开源");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }
    //有代理合约
    if (data.is_proxy == 1) {
        riskLevel = 2;
        console.info("----------------------------------有代理合约");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }
    //可增发，存在恶意发现砸盘的可能
    if (data.is_mintable == 1) {
        riskLevel = 2;
        console.info("----------------------------------可增发");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }
    //无法全部卖出
    if (data.cannot_sell_all == 1) {
        riskLevel = 2;
        console.info("----------------------------------无法全部卖出");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }
    //买税大于10%，小于50%
    let buyTax = Number(data.buy_tax);
    if (buyTax > 0.1 && buyTax < 0.5) {
        riskLevel = 2;
        console.info("----------------------------------买税大于10%，小于50%");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }
    //买税大于50%
    if (buyTax > 0.5) {
        riskLevel = 3;
        console.info("----------------------------------买税大于50%");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //卖税大于10%，小于50%
    let sellTax = Number(data.sell_tax);
    if (sellTax > 0.1 && sellTax < 0.5) {
        riskLevel = 2;
        console.info("----------------------------------卖税大于10%，小于50%");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }
    //卖税大于50%
    if (sellTax > 0.5) {
        riskLevel = 3;
        console.info("----------------------------------卖税大于50%");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }
    //疑似貔貅
    if (data.is_honeypot == 1) {
        riskLevel = 3;
        console.info("---------------------------------疑似貔貅");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //可暂停交易
    if (data.transfer_pausable == 1) {
        riskLevel = 3;
        console.info("----------------------------------可暂停交易");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //防巨鲸（限制交易数量）
    if (data.is_anti_whale == 1) {
        riskLevel = 1;
        console.info("----------------------------------防巨鲸");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //防巨鲸可改
    if (data.anti_whale_modifiable == 1) {
        riskLevel = 2;
        console.info("----------------------------------防巨鲸可改");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //交易税可改
    if (data.slippage_modifiable == 1) {
        riskLevel = 2;
        console.info("----------------------------------交易税可改");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //存在黑名单
    if (data.is_blacklisted == 1) {
        riskLevel = 2;
        console.info("----------------------------------存在黑名单");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //存在白名单
    if (data.is_whitelisted == 1) {
        riskLevel = 2;
        console.info("---------------------------------存在白名单");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //疑似假币
    if (data.is_true_token == 0) {
        riskLevel = 3;
        console.info("----------------------------------疑似假币");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //空投诈骗
    if (data.is_airdrop_scam == 1) {
        riskLevel = 3;
        console.info("----------------------------------空投诈骗");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //无法买入
    if (data.cannot_buy == 1) {
        riskLevel = 2;
        console.info("----------------------------------无法买入");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //其他潜在风险
    if (data.is_airdrop_scam != null) {
        riskLevel = 2;
        console.info("----------------------------------其他潜在风险");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //可针对特定地址改税
    if (data.personal_slippage_modifiable == 1) {
        riskLevel = 3;
        console.info("----------------------------------可针对特定地址改税");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //存在外部合约调用风险
    if (data.external_call == 1) {
        riskLevel = 3;
        if (address.equals("0xc9c8050639c4cc0df159e0e47020d6e392191407") || address.equals("0xfc6f27f40cc9b4d8f23239465b208052bb4d3b0c")) {
            data.external_call = 0;
        } else {
            console.info("----------------------------------存在外部合约调用风险");
            riskScore = addRiskScore(riskScore, riskLevel, 60);
        }
    }

    //有隐藏的所有者
    if (data.hidden_owner == 1) {
        riskLevel = 2;
        console.info("----------------------------------有隐藏的所有者");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //该代币可自毁
    if (data.selfdestruct == 1) {
        riskLevel = 2;
        console.info("---------------------------------该代币可自毁");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //存在gas滥用
    if (data.gas_abuse == 1) {
        riskLevel = 2;
        console.info("----------------------------------存在gas滥用");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    //所有者可修改余额
    if (data.owner_change_balance == 1) {
        riskLevel = 3;
        console.info("----------------------------------所有者可修改余额");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    //有交易冷却机制
    if (data.trading_cooldown == 1) {
        riskLevel = 3;
        console.info("----------------------------------有交易冷却机制");
        riskScore = addRiskScore(riskScore, riskLevel, 60);
    }

    let has5percent = false
    if (data.holders) {
        for (var i = 0; i < data.holders.length; i++) {
            let holder = data.holders[i];
            if (holder.address != data.creator_address &&
                !holder.address.startsWith("0x00000") &&
                Number(holder.percent) > 0.05 && holder.is_contract != 1) {
                has5percent = true;
            }
        }
    }
    if (has5percent) {
        //有其他超过5%的持仓地址
        riskLevel = 2;
        console.info("----------------------------------有其他超过5%的持仓地址");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }

    let creatorLpException = false;
    if (data.lp_holders) {
        for (var i = 0; i < data.lp_holders.length; i++) {
            let holder = data.lp_holders[i];
            if (holder.address == data.creator_address &&
                Number(holder.percent) > 0.10) {
                creatorLpException = true;
            }
        }
    }
    if (creatorLpException) {
        //creator地址LP占比不正常（10%）
        riskLevel = 2;
        console.info("----------------------------------creator地址LP占比不正常（10%）");
        riskScore = addRiskScore(riskScore, riskLevel, 10);
    }
    return riskScore;
}

async function updateDex(dexList, token) {
    if (!dexList || dexList.length == 0) {
        console.log("没有交易对数据");
        return;
    }
    for (const dex of dexList) {
        if (dex.name != 'PancakeV2') continue;
        let pairModel = await PairModel().findOne({
            where: {
                address: dex.pair
            }
        });
        if (!pairModel) continue;

        let tokenIndex = 0;
        if (pairModel.token1_id == token.id) {
            tokenIndex = 1;
        }
        console.log("pairModel.token_id = " + pairModel.token_id);
        console.log("token_id = " + token.id);
        console.log("pairModel.token_index = " + pairModel.token_index);
        console.log("tokenIndex = " + tokenIndex);
        await pairModel.update({
            token_id: token.id,
            token_index: tokenIndex,
            token_confirm: 1,
        });
    }
}

async function processToken(token) {
    let result = await checkToken(56, token.address);
    if (result) {
        if (!token.lp_holder_count || token.riskScore == 20) {
            let riskScore = getRiskScore(result);
            console.log("地址--->" + token.id, token.address);
            console.log("riskScore---->", riskScore);
            await token.update({
                holders: result.holder_count || 0,
                lp_holder_count: result.lp_holder_count || 0,
                owner_address: result.owner || '',
                creator_address: result.creator_address || '',
                is_discard_permission: result.can_take_back_ownership == 1 ? 0 : 1,
                is_mintable: result.is_mintable || 0,
                buy_tax: result.buy_tax || 0,
                sell_tax: result.sell_tax || 0,
                is_in_dex: result.is_in_dex || 0,
                risk_score: riskScore,
            });
        }
        await updateDex(result.dex, token);
    }
    await delay(500); // 假设 delay 是一个定义好的延迟函数
}

/**
 * 获取交易对的方向
 * @param { String } address
 * @param { String } token0Address
 * @param { String } token1Address
 * @returns
 */
async function getTokenIndexByAddressAndTokens(address, token0Address, token1Address) {
    let token0Check = await checkToken(56, token0Address);
    if (token0Check && token0Check.dex) {
        for (const dex of token0Check.dex) {
            if (dex.name != 'PancakeV2') continue;
            if (dex.pair == address) {
                return 0;
            }
        }
    }
    //等待1秒 不然会请求次数过多
    await delay(1000);
    let token1Check = await checkToken(56, token1Address);
    if (token1Check && token1Check.dex) {
        for (const dex of token1Check.dex) {
            if (dex.name != 'PancakeV2') continue;
            if (dex.pair == address) {
                return 1;
            }
        }
    }
    return -1;
}

async function parseOne(tokenId) {
    const token = await TokenModel().findByPk(tokenId);
    await processToken(token);
}

module.exports = {
    parseOne,
    checkToken,
    getRiskScore,
    getTokenIndexByAddressAndTokens,
}

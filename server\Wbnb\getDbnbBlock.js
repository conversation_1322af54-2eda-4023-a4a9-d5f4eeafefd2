const redis = require('../database/redis_in')
const {delay} = require("../utils/functions");
//存储获取WBNB的区块号的List集合
const WBNB_BLOCK_NUM_LIST = 'WBNB_BLOCK_NUM_LIST';
//存储在链上获取的当前区块号的WBNB的价格HASH
const WBNB_LIST_KEY = 'WBNB_LIST_KEY'
//存储在链上获取失败的列表
const WBNB_LIST_KEY_ERR = 'WBNB_LIST_KEY_ERR'
const WBNB_CURRENT_BLOCK = 'WBNB_CURRENT_BLOCK'
let newBlock = 39705537;
let endBlock = 6831287;

async function parseInit() {
    while (true) {
        try {
            let blocks = [];
            let newB = await redis.get("block-listen_ok");
            if (newB) {
                newBlock = Number(newB);
            }
            let endB = await redis.get(WBNB_CURRENT_BLOCK);
            if (endB) {
                endBlock = Number(endB);
            }
            console.log("开始" + endBlock + "_" + newBlock);
            if (newBlock == endBlock) {
                console.log("等待数据..");
                await delay(10000);//等待10s
                continue;
            }
            for (let index = endBlock; index <= newBlock; index++) {
                blocks.push(index);
                if (blocks.length > 100000) {
                    console.log("插入" + index);
                    await redis.rPush(WBNB_BLOCK_NUM_LIST, blocks);
                    blocks.length = 0;
                }
            }
            //补充剩余
            if (blocks.length > 0) {
                console.log("插入" + blocks[blocks.length - 1]);
                await redis.rPush(WBNB_BLOCK_NUM_LIST, blocks);
                blocks.length = 0;
            }
            await redis.set(WBNB_CURRENT_BLOCK, newBlock);
            console.log("完成");
        } catch (e) {
            console.error("error:"+e);
        }
        // console.log("开始123");
        // let newBlock = Number(await redis.get("block_new_ase_now"));

        // let currentBlock = Number(await redis.get(WBNB_CURRENT_BLOCK));
        // let blocks = [];
        // if(currentBlock){
        //     if(newBlock > currentBlock){
        //         for (let index = currentBlock+1; index <= newBlock; index++) {
        //             blocks.push(index);
        //         }
        //     }else{
        //         //await delay(3000);
        //     }
        //     await redis.rPush(WBNB_BLOCK_NUM_LIST,blocks);
        //     await redis.set(WBNB_CURRENT_BLOCK,newBlock);
        //     console.log("完成");
        // }else{

        // }

    }

}


// async function setDbnbListInfo(threadNum){
//     console.log("获取DBNB价格程序启动了")
//     let threadpromise = [];
//     for (let i = 0; i < threadNum; i++) {
//         threadpromise.push(new Promise(async (resolve, reject) => {
//             while (true) {
//                 let currentInfo = await Redis.rpop(WBNB_BLOCK_NUM_LIST);
//                 if (currentInfo) {
//                     try {
//                         let timeStr = new Date().getTime();
//                         let price = await getInfo(parseInt(currentInfo));
//                         let timeEnd = new Date().getTime();
//                         console.log(`当前区块号：${currentInfo}，用时${(timeEnd-timeStr)/1000}s 线程：${i}`)
//                         if(price){
//                             await Redis.hset(WBNB_LIST_KEY, currentInfo,price);
//                         }
//                     } catch (e) {
//                         await Redis.rpush(WBNB_LIST_KEY_ERR, currentInfo);
//                         console.log("内部报错：",e)
//                         reject(currentInfo)
//                     }

//                 } else {
//                     resolve('end')
//                     break;
//                 }
//             }
//             console.log("当前线程结束------------", i)
//         }))
//     }
//     Promise.all(threadpromise).then(res => {
//         console.log("获取价格程序结束")

//     }).catch(err => {
//         console.log("PromiseALL集合报错", err)
//     })
// }
// async function setBlock(strBlock,endBlock) {
//     let list = [];
//     let currentBlock = strBlock;
//     while (true){
//         if(currentBlock>endBlock){
//             break;
//         }
//         list.push(currentBlock);
//         currentBlock++;
//     }
//     Redis.rpush(WBNB_BLOCK_NUM_LIST,list);
// }
// async function parse(){
//     while (true) {
//         parseInit();
//         await delay(10000)

//     }
// }
// async function parseInit(){
//     let currentBlock = parseInt(await Redis.get(WBNB_CURRENT_BLOCK));
//     if(!currentBlock){
//         currentBlock = STR_BLOCK;
//     }
//     let newBlock = await provider.getBlockNumber();
//     if(newBlock-currentBlock>=10){
//         await Redis.set(WBNB_CURRENT_BLOCK, newBlock)
//         await setBlock(currentBlock,newBlock);
//         await setDbnbListInfo(10);
//     }
// }

// setDbnbListInfo(5)

//跑区块号列表
parseInit()

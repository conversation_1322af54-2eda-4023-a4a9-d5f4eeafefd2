const {BatchProcessor} = require("../AnalyzeUtils");
// Constants
const DORIS_HOST = 'ep-wz9i5daadc6728448167-cn-shenzhen-e.epsrv-wz9vpy634b2zdlao3qtx.cn-shenzhen.privatelink.aliyuncs.com';
// const DORIS_HOST = '**************';
const DORIS_DB = 'avi';
const DORIS_TABLE = 'holderCountTime_lpRatio';
const DORIS_USER = 'admin';
const DORIS_PASSWORD = 'shengna@20240814=EcI';
const DORIS_HTTP_PORT = 48523;//这里是doris的http端口

function basicAuthHeader(username, password) {
    const toBeEncoded = username + ':' + password;
    return 'Basic ' + Buffer.from(toBeEncoded).toString('base64');
}

const loadUrl = `/api/${DORIS_DB}/${DORIS_TABLE}/_stream_load`;
const options = {
    hostname: DORIS_HOST,
    port: DORIS_HTTP_PORT,
    path: loadUrl,
    method: 'PUT',
    headers: {
        'Expect': '100-continue',
        'Authorization': basicAuthHeader(DORIS_USER, DORIS_PASSWORD),
        'Content-Type': 'text/plain; charset=utf-8',
        'column_separator': '|'
    }
};
const batchProcessor = new BatchProcessor({
    name:"holderCountTime_lpRatioSL",
    maxInterval: 5000, // 每30秒发送一次数据
    options:options
});
function slHolderCountTimeLpRatioSL(array, no) {
    batchProcessor.streamLoadAdd(array, no);

}

function slHolderCountTimeLpRatioSLOne(item, no) {
    batchProcessor.streamLoadAdd([item], no);
}

// async function slHolderQuantityTimeSLDistroy() {
//     batchProcessor.distroyInterval();
// }

module.exports = {
    slHolderCountTimeLpRatioSL, slHolderCountTimeLpRatioSLOne
}
/**
 * 描述: 初始化路由信息，自定义全局异常处理
 * 作者: <PERSON>
 * 日期: 2020-06-19
*/

const express = require('express');
// const boom = require('boom'); // 引入boom模块，处理程序异常状态
const userRouter = require('./users'); // 引入user路由模块
const taskRouter = require('./tasks'); // 引入task路由模块
const homeRouter = require('./homes'); // 引入home路由模块
const adminRouter = require('./admin'); // 引入home路由模块
const uploadRouter = require('./upload'); // 引入home路由模块
const listenRouter = require('./listen'); // 引入home路由模块
const goplusRouter = require('./goplus'); // 引入home路由模块
const monitorRouter = require('./monitor'); // 引入home路由模块
const { jwtAuth, decode } = require('../utils/user-jwt'); // 引入jwt认证函数
const router = express.Router(); // 注册路由 

router.use(jwtAuth); // 注入认证模块

router.use('/api/user/', userRouter); // 注入用户路由模块
router.use('/api/task/', taskRouter); // 注入任务路由模块
router.use('/api/home/', homeRouter); // 注入主页路由模块
router.use('/api/admin/', adminRouter); // 注入后台路由模块
router.use('/api/listen/',listenRouter); //注入监听路由模块
router.use('/api/upload/',uploadRouter); //注入上传路由模块
router.use('/api/goplus/',goplusRouter); //注入上传路由模块
router.use('/api/monitor/',monitorRouter); //注入监控路由模块

// 自定义统一异常处理中间件，需要放在代码最后
router.use((err, req, res, next) => {
    // 自定义用户认证失败的错误返回
    console.log('err===', err);
    if (err && err.name === 'UnauthorizedError') {
      const { status = 401, message } = err;
      // 抛出401异常
      res.status(status).json({
        code: status,
        msg: 'token失效，请重新登录',
        data: null
      })
    } else {
      const { output } = err || {};
      // 错误码和错误信息
      const errCode = (output && output.statusCode) || 500;
      const errMsg = (output && output.payload && output.payload.error) || err.message;
      res.status(errCode).json({
        code: errCode,
        msg: errMsg
      })
    }
  })

module.exports = router;
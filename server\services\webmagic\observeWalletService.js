const {default: BigNumber} = require("bignumber.js");
BigNumber.config({DECIMAL_PLACES: 100});
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../../database/redis");

const {getMemberList} = require("../../swapParse/testGetMemberList");
const {PqueryQL_balance_change} = require("../../database/PqueryQL/PostgresSQL");

//观察钱包查询代币   暂时没用的，闲置
async function getWalletTokenList(request, result) {
    let params = request.body
    if (!params.address ) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    let arg = `
        WITH
        wallet_balance_changes AS (  -- 获取当前地址持有的所有地址(最后时刻数据)
            SELECT 
                contract,owner,new_balance,timestamp,ROW_NUMBER() OVER (
                    PARTITION BY contract, owner
                    ORDER BY timestamp DESC
                ) AS rn 
            FROM balance_changes where owner = '${params.address}';
        )
        SELECT * FROM wallet_balance_changes where rn=1;

    `;
    let data = await PqueryQL_balance_change(arg);

    let callData = {list:[]};

    let isNull = true;//默认查询为空的标记
    if (data && data.rows.length > 0) {
        //循环查询的结果
        for (let i = 0; i < data.rows.length; i++) {
            callData.list.push({

            })

        }
    }

}


module.exports = {
    getWalletTokenList
}

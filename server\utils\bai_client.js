// const net = require('net')
const {getHistoryLine} = require("../avi/kline");
const {child_exec} = require("./child_pm");
// const socket = new net.Socket({})
// socket.connect({
//   host: '127.0.0.1',
//   port: 4000
// })
let socket_bai;
// 引入客户端IO

function init(){
    var socket = require("socket.io-client")('ws://127.0.0.1:3000');
    socket.emit("admin","");
    //console.log("正式开始监听");
    socket_bai = socket;
}

function getSocket(){
    //console.log("当前的socket："+socket_bai);
    return socket_bai;
}


module.exports={
    init,getSocket
}



const { startCase } = require("lodash");
const redis = require("../database/redis");
const {formatAddress} = require("../utils/functions");
const key = "pair_address_data";
const pair_key = "pair_json_hdata";
const token_key = "token_address_data";

async function batchCallContracts() {
    let pair_address = '';
    let flag = true;
    while (flag) {
        try {
            let tokens = [];
            pair_address = await redis.lPop(key);
            if (!pair_address) {
                await delay(3000);
                continue;

            }
            let pair_token = await redis.hGet(pair_key,formatAddress(pair_address));
            let pair_data = JSON.parse(pair_token);

            let token0_addr = pair_data['token0_addr'];

            let token1_addr = pair_data['token1_addr'];

            tokens.push(token0_addr);
            tokens.push(token1_addr);
            
            await redis.rPush(token_key,tokens);

        }catch(ex){

        }
    }
}

async function start(){
    for (let index = 0; index < 1; index++) {
        new Promise(async (resolves, rejects) => {
            batchCallContracts();
        });
        
    }
    
}

start();
/***
 * 1：得到追踪的人和追踪的地址
 * 2：查询当前这个人当前合约的持币量（从doris中查询）
 * 3：查询这个人当前合约的交易数据（从doris中查询）
 * 4：查询这个人当前合约的lp数据（从doris中查询）
 * 5：查询这个人当前合约的转账数据（从doris中查询）
 * 6：从合约地址中查询出最大pair
 * 7：从pair中查询出当前价格（usdt,从redis中进行查询）
 * 8：查询出这个人这个合约每天的币量变化数据（这个需要重新设计表）
***/
const redis = require('../database/redis_in')

const {ethers} = require("ethers");
const {queryGuiProvider} = require('../avi/factory_provider');
const provider = queryGuiProvider();

const allAbi = require('../abi/all.json');
const {delay} = require("../utils/functions");

async function getInfo(blockNum){
    //第一个交易对
    let contract = new ethers.Contract("******************************************",allAbi,provider);

    let out=await contract.balanceOf('******************************************',{blockTag: blockNum});

    console.log(out);

}

getInfo(34203494);
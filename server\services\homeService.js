/**
 * 描述: 业务逻辑处理 - 任务相关接口
 * 作者: <PERSON>
 * 日期: 2020-06-20
 */
const {
  queryNotice,
  lineModel,
  transModel,
  trancaUerModel,
  cunewModel,
  cudealModel,
  cuhandModel,
  cupoolModel,
  cufallModel,
  curiseModel,
  cutransModel,
  cuhotModel,
  userBiModel,
  mobilityModel,
  rotationModel,
  browserModel,
  flashModel,
  userModel,
  userMobilityModel,
  juhe
} = require("../model/model");
const { initBull } = require('../utils/bull_query');
const { querySql } = require("../database/config");
const connect = await querySql();
const moment =require("moment");
const { size } = require("lodash");
const PairModel = require("../model/PairModel");
const TokenModel = require("../model/TokenModel");
// 主页数据
async function queryAll(req, res,next) {
  //let { numOne,numTwo } = req.body;
  //queryAllPairs(numOne,numTwo);
  //查询热门数据
  var hotModel = cuhotModel(connect);
  var hots_array = await hotModel.find();
  var hot_address = [];

  for (var i = 0; i < hots_array.length; i++) {
    hot_address.push(hots_array[i].trans_address);
  }
  var hot_array = await PairModel.find({ trans_address: { $in: hot_address } });
  //console.log(hot_array.length);
  var hots = [];
  for (var i = 0; i < hot_array.length; i++) {
      var token_res = await TokenModel.find({address:hot_array[i].target})
      var bai = {
        name: token_res.symbol,
        address: hot_array[i].target,
        trans_address: hot_array[i].trans_address,
        image: "",
        price: hot_array[i].price,
        change: hot_array[i].price_change,
        name_index: hot_array[i].name_index,
      };
      
      hots.push(bai);
    }
  var data = {
    hots: hots,
    // news:news,
    // trans:trans,
    // rises:rises,
    // falls:falls,
    // pools:pools,
    // hands:hands,
    // deals:deals,
  };
  res.json({
    code: 200,
    msg: "success",
    data: data,
  });
}

async function queryNotices(req, res,next){
  
  //查询所有公告
  var noticeModel = queryNotice(connect);
  var notices = await noticeModel.find();
  res.json({
    code: 200,
    msg: "success",
    data: notices,
  });
}

async function queryRotations(req, res,next){
  //查询所有公告
  var rotationmodel = rotationModel(connect);
  var rotations = await rotationmodel.find();
  res.json({
    code: 200,
    msg: "success",
    data: rotations,
  });
}

//根据类型查询榜单数据集
async function queryByType(req, res,next) {
  let { type } = req.body;
  var res_data = [];
  var allbai = [];
  switch (type) {
    case 1:
      var hotModel = cuhotModel(connect);
      allbai = await hotModel.find();

      break;
    case 2:
      var newModel = cunewModel(connect);
      allbai = await newModel.find();

      break;
    case 3:
      var dealModel = cudealModel(connect);
      allbai = await dealModel.find();
      break;
    case 4:
      var handModel = cuhandModel(connect);
      allbai = await handModel.find();
      break;
    case 5:
      var poolModel = cupoolModel(connect);
      allbai = await poolModel.find();
      break;
    case 6:
      var fallModel = cufallModel(connect);
      allbai = await fallModel.find();
      break;
    case 7:
      var riseModel = curiseModel(connect);
      allbai = await riseModel.find();
      break;
    case 8:
      var transModel = cutransModel(connect);
      allbai = await transModel.find();
      break;
    default:
      break;
  }
  //console.log(allbai.length);
  var address = [];
  for (var i = 0; i < allbai.length; i++) {
    address.push(allbai[i].trans_address);
  }
  var all = await PairModel.find({ trans_address: { $in: address } });
  for (var i = 0; i < all.length; i++) {
    var token_res = await TokenModel.find({address:all[i].target})
      var bai = {
        name: token_res.symbol,
        address: all[i].target,
        trans_address: all[i].trans_address,
        image: "",
        price: all[i].price,
        change: all[i].price_change,
        name_index: all[i].name_index,
      };
      
      res_data.push(bai);
  }
  res.json({
    code: 200,
    msg: "success",
    data: res_data,
  });
}

//查询币种基础数据
async function queryCurrency(req, res,next) {
  let { trans_address } = req.body;
  //console.log(name_index);
  var currency = await PairModel.findOne({ pair: trans_address });
  var name = "";
  var currencys = [];
  var biModel = userBiModel(connect,"");
  var all_users = await biModel.find({ current: { $gt: 0 },address: currency.target});
 
  var s = moment().startOf('days').valueOf();
  var tranModel = transModel(connect, "");
  var line_model = lineModel(connect,"");
  var sline = await line_model.find({pool:trans_address});
  var line = null;

  if(sline.length > 0){
    line = await line_model.findOne({date_type:"1D",kline_time:s});
  }else{

  }
  var token0_res = await TokenModel.find({address:currency.token0_address});
  var token1_res = await TokenModel.find({address:currency.token1_address});
  if(line == null){
    var currency_res = {
      price:currency.price,//价格
      chg_hours:0,//24小时涨跌幅
      trans_address:currency.pair,//交易对地址
      address_one:currency.token0_address,//地址1
      name_one:currency.name_one,//名称1
      image_one:currency.image_one,//图标1
      address_two:currency.address_two,//地址2
      name_two:currency.name_two,//名称2
      image_two:currency.image_two,//图标2
      volume:0,//24小时交易量
      volume_price:0,//24小时交易额
      flag:currency.flag,//flag
      total_market:"",//流通市值 
      user_num:all_users.length,//持有人 
      trans_num:0,//24H交易数 
      max:0,//24H高 
      change_hands:0,//24H换手 
      pool_price:"",//池子大小 
      min:0,//24H低 
      start_price:"",//首日开盘价 
      circulation:currency.circulation,//总量 
      flag_new:0,//可增发 
      roi:"",//投资回报率 
      rates:"",//流通率 
      public_chain:"",//所属公链 
      reserve_one:currency.reserve_one,//token0的数量 
      reserve_two:currency.reserve_two,//token1的数量 
      name_index:currency.name_index,
      reserve_lp:currency.reserve_lp,//流动性总价值
    }
    //查询当前币种所有的交易对
    var all_currencys = await currModel.find({target:currency.target}).sort({price:-1});
    currencys = all_currencys;
  
    // //查询当前K线数据（1分钟）
    // var linesModel = lineModel(connect, currency.name_index);
    // var klines = await linesModel.find({ data_type: "1分钟" }).sort({data_qu:-1});
    // currency.klines = klines;
  
    res.json({
      code: 200,
      msg: "success",
      data: {
        currency:currency_res,
        currencys:currencys
      },
    });
  }else{
    var volume_now = 0;
    var volume_price_now = 0;
    var trans_num = 0;
    var date_start = parseInt((Number(line.date_time) + Number(line.data_qu)*1000*60*60*24)/1000);
    var date_end = parseInt((Number(line.date_time) + (Number(line.data_qu)+1)*1000*60*60*24)/1000);
    var trans_volue = await tranModel.aggregate([
      {
        $match:{
          tranc_time:{$gte:date_start,$lt:date_end}
        }
      },
      {$group:{_id:null,sumNum:{$sum:"$num"},sumAllNum:{$sum:"$allnum"},num:{$sum:1}}},
    ])
    if(trans_volue.length > 0){
      volume_now = trans_volue[0].sumNum;
      volume_price_now = trans_volue[0].sumAllNum;
      trans_num = trans_volue[0].num;
    }
    var currency_res = {
      price:currency.price,//价格
      chg_hours:line.chg,//24小时涨跌幅
      trans_address:currency.trans_address,//交易对地址
      address_one:currency.address_one,//地址1
      name_one:currency.name_one,//名称1
      image_one:currency.image_one,//图标1
      address_two:currency.address_two,//地址2
      name_two:currency.name_two,//名称2
      image_two:currency.image_two,//图标2
      volume:volume_now,//24小时交易量
      volume_price:volume_price_now,//24小时交易额
      flag:currency.flag,//flag
      total_market:"",//流通市值 
      user_num:all_users.length,//持有人 
      trans_num:trans_num,//24H交易数 
      max:line.max,//24H高 
      change_hands:"",//24H换手 
      pool_price:"",//池子大小 
      min:line.min,//24H低 
      start_price:"",//首日开盘价 
      circulation:currency.circulation,//总量 
      flag_new:0,//可增发 
      roi:"",//投资回报率 
      rates:"",//流通率 
      public_chain:"",//所属公链 
      reserve_one:currency.reserve_one,//token0的数量 
      reserve_two:currency.reserve_two,//token1的数量 
      name_index:currency.name_index,
      reserve_lp:currency.reserve_lp,//流动性总价值
    }
    //查询当前币种所有的交易对
    var all_currencys = await currModel.find({$or: [
      {
        address_one: currency.address_one,
        flag: 0,
      },
      {
        address_two: currency.address_two,
        flag: 1,
      }
    ]});
    currencys = all_currencys;
  
    // //查询当前K线数据（1分钟）
    // var linesModel = lineModel(connect, currency.name_index);
    // var klines = await linesModel.find({ data_type: "1分钟" }).sort({data_qu:-1});
    // currency.klines = klines;
  
    res.json({
      code: 200,
      msg: "success",
      data: {
        currency:currency_res,
        currencys:currencys
      },
    });
  }
  
}

//查询K线数据
async function queryKline(req, res,next) {
  let { date_type, name_index,page } = req.body;
  //console.log(date_type);
  if(page == null || page ==0 || page == undefined){
    page = 1;
  }
  var size = 1000;
  var linesModel = lineModel(connect, name_index);
  var tranModel = transModel(connect, name_index);
  var klines = await linesModel.find({ date_type: date_type }).sort({data_qu:1}).limit(size).skip((page-1)*size);

  for(var i = 0;i<klines.length;i++){
    if(date_type == "1分钟"){
      var date_start = parseInt((Number(klines[i].date_time) + Number(klines[i].data_qu)*1000*60)/1000);
      var date_end = parseInt((Number(klines[i].date_time) + (Number(klines[i].data_qu)+1)*1000*60)/1000);
      var trans_volue = await tranModel.aggregate([
        {
          $match:{
            tranc_time:{$gte:date_start,$lt:date_end}
          }
        },
        {$group:{_id:null,sumNum:{$sum:"$num"},sumAllNum:{$sum:"$allnum"}}},
      ])
      if(trans_volue.length > 0){
        klines[i].volume = trans_volue[0].sumNum;
      }
    }else{
      var date_start = parseInt((Number(klines[i].date_time) + Number(klines[i].data_qu)*1000*60*60*24)/1000);
      var date_end = parseInt((Number(klines[i].date_time) + (Number(klines[i].data_qu)+1)*1000*60*60*24)/1000);
      var trans_volue = await tranModel.aggregate([
        {
          $match:{
            tranc_time:{$gte:date_start,$lt:date_end}
          }
        },
        {$group:{_id:null,sumNum:{$sum:"$num"},sumAllNum:{$sum:"$allnum"}}},
      ])
      if(trans_volue.length > 0){
        klines[i].volume = trans_volue[0].sumNum;
      }
    }
    
  }

  res.json({
    code: 200,
    msg: "success",
    data: klines,
  });
}

//查询交易数据
async function queryTrans(req, res,next) {
  let { name_index } = req.body;
  var page = 1;
  var size = 50;
  var tranModel = transModel(connect, name_index);
  var trans = await tranModel.find().sort({tranc_time:-1}).limit(size).skip((page-1)*size);
  
  res.json({
    code: 200,
    msg: "success",
    data: trans,
  });
}

//根据用户地址查询交易数据
async function queryTransByUser(req, res,next){
  let { address,name_index,page,size } = req.body;
  var tuserModel = trancaUerModel(connect,"usertrans_"+name_index);
  var all_trans_sell = await tuserModel.find( {
    $or: [
      {user_one:address},
      {user_two:address},
    ]
  }).limit(size).skip((page-1)*size);

  res.json({
    code: 200,
    msg: "success",
    data: all_trans_sell,
  });
}

//

//查询用户持币数据
async function queryUserBi(req, res,next) {
  let { name_index,page,size } = req.body;
  //{ field: { $gt: value } }
  ////console.log(name_index);
  page = 1;
  size = 100;
  var currModel = PairModel(connect);
  var currency = await currModel.findOne({ name_index: name_index });

  var biModel = userBiModel(connect, "user_bi_"+name_index);
  var all_users = await biModel.find({ current: { $gt: 0 } });
  var users = await biModel.find({ current: { $gt: 0 } }).limit(size).skip((page-1)*size).sort({current:-1});
  //查询三日波动
  var tuserModel = trancaUerModel(connect,"usertrans_"+name_index);
  for(var i = 0;i<users.length;i++){
    var threeBo;
    var res_num = await tuserModel.aggregate([{
      $match:{
        user_one:users[i].address
      }
    },{$group:{_id:null,sums:{$sum:"$num"}}}]);

    var res_num1 = await tuserModel.aggregate([{
      $match:{
        user_two:users[i].address
      }
    },{$group:{_id:null,sums:{$sum:"$num"}}}]);

    if(res_num.length >0){
      if(res_num1.length >0){
        threeBo = res_num[0].sums - res_num1[0].sums;
      }else{
        threeBo = res_num[0].sums
      }

    }else{
      if(res_num1.length >0){
        threeBo = 0 - res_num1[0].sums;
      }else{
        threeBo = 0
      }
    }
    //console.log(currency.price);
    users[i].three_bo = threeBo;
    users[i].current_price = Number(currency.price);
    //查询最终盈亏
    //console.log(users[i]);
  }
  
  ////console.log(users);
  var data = {
    user_num: all_users.length,
    users: users,
  };
  res.json({
    code: 200,
    msg: "success",
    data: data,
  });
}

//查询用户购买成本
async function userBi(req, res,next){
  //根据价格分组查询出
  var date_res = moment().subtract(10,"days").valueOf();
  var date = parseInt(date_res/1000);
  var tranModel = transModel(connect, name_index);
  var trans = await tranModel.aggregate([
    {
      $match:{
        tranc_time:{$gte:date}
      }
    }
    ,{$group:{_id:{ 'type': '$type', 'user': '$user' },sumNum:{$sum:1}}}]);

}

//查询币种简介
async function currencyInfo(req, res,next){
  //查询币种相关信息
  let { name_index } = req.body;
  var currency_bai = await currModel.findOne({ name_index: name_index });

    if (currency_bai.flag == 1) {
      var bai = {
        name: currency_bai.name_two,
        address: currency_bai.address_two,
        trans_address: currency_bai.trans_address,
        image: currency_bai.image_two,
        price: currency_bai.price,
        change: currency_bai.chg_hours,
        circulation:currency_bai.circulation,
        name_index: currency_bai.name_index,
      };
      res.json({
        code: 200,
        msg: "success",
        data: bai,
      });
    } else {
      var bai = {
        name: currency_bai.name_one,
        address: currency_bai.address_one,
        trans_address: currency_bai.trans_address,
        image: currency_bai.image_one,
        price: currency_bai.price,
        change: currency_bai.chg_hours,
        circulation:currency_bai.circulation,
        name_index: currency_bai.name_index,
      };
      res.json({
        code: 200,
        msg: "success",
        data: bai,
      });
    }
}


//查询所有流动性
async function queryMobility(req, res,next){
  let { name_index,page,size } = req.body;
  page = 1;
  size = 50;
  var name = "mobility_"+name_index;
  var mobilitymodel = mobilityModel(connect,name_index);
  var all_mobility = await mobilitymodel.find().limit(size).skip((page-1)*size);
  res.json({
    code: 200,
    msg: "success",
    data: all_mobility,
  });
}

//查询每天的流动性总量
async function queryMobilityAll(req, res,next){
  let { name_index } = req.body;

  var mobilitymodel = mobilityModel(connect,name_index);

  var res_all = await mobilitymodel.find();
  ////console.log(res_all);
  var res_mobility = await mobilitymodel.aggregate([{$group:{_id:null,minNum:{$min:"$time"}}}]);

  //console.log(res_mobility);
  if(res_mobility.length<=0){
    var times = [];
    var datas = [];
    var data = {
      categories:times,
      series:datas
    }
    res.json({
      code: 200,
      msg: "success",
      data: data,
    });
  }else{
    var s = moment().endOf('days').valueOf()+1;
    var m = moment().subtract(30, "days").startOf('days').subtract(24,"hours")
    var data_qu_time = Number(s) - Number(m);
    var all_time = 1000*60*60*24;
    var data_qu = parseInt(data_qu_time/all_time);
    var times = [];
    var datas = [];
    for(var i = 0;i<data_qu;i++){
      var m1 = (m+all_time*i)/1000;
      var m2 = (m+all_time*(i+1))/1000
      var res_data = await mobilitymodel.aggregate([
      {
        $match:{
          time:{$gte:m1.toString(),$lt:m2.toString()}
        }
      }
      ,{$group:{_id:null,sumNum:{$sum:"$lp_num"}}}]);
      //console.log(res_data);
      if(res_data.length > 0){
        times.push(m+all_time*i);
        datas.push(res_data[0].sumNum);
      }else{
        times.push(m+all_time*i);
        datas.push(0);
      }
    }
    var data = {
      categories:times,
      series:datas
    }
    res.json({
      code: 200,
      msg: "success",
      data: data,
    });
  }

  

}

//查询lp持币明细
async function queryMobiLp(req, res,next){
  let { name_index,page,size } = req.body;
  page = 1;
  size = 50;
  var name = "user_mobility_"+name_index;
  var mobilitymodel = userMobilityModel(connect,name_index);
  var all_mobility = await mobilitymodel.find().limit(size).skip((page-1)*size);
  res.json({
    code: 200,
    msg: "success",
    data: all_mobility,
  });
}

//查询常用下载
async function queryDown(req, res,next){
  var brows_model = browserModel(connect);
  var brows = brows_model.find({type:1});
  res.json({
    code: 200,
    msg: "success",
    data: brows,
  });
}

//查询区块浏览器
async function queryBrows(req, res,next){
  var brows_model = browserModel(connect);
  var brows = brows_model.find({type:2});
  res.json({
    code: 200,
    msg: "success",
    data: brows,
  });
}

//查询区块通讯
async function queryCommunicate(req, res,next){
  var brows_model = browserModel(connect);
  var brows = brows_model.find({type:3});
  res.json({
    code: 200,
    msg: "success",
    data: brows,
  });
}

//查询媒体社区
async function queryMedia(req, res,next){
  var brows_model = browserModel(connect);
  var brows = brows_model.find({type:4});
  res.json({
    code: 200,
    msg: "success",
    data: brows,
  });
}

//查询快讯
async function queryFlash(req, res,next){
  var flash_model = flashModel(connect);
  var flashs = flash_model.find().sort({time:-1});
  res.json({
    code: 200,
    msg: "success",
    data: flashs,
  });
} 

//获取资金
async function queryActiveUsers(req, res,next){
  let { name_index } = req.body;
  var tranModel = transModel(connect, name_index);
  var date_res = moment().subtract(96,"hours").valueOf();
  var date = parseInt(date_res/1000);
  console.log(date);
  var trans = await tranModel.aggregate([
    {
      $match:{
        tranc_time:{$gte:date}
      }
    }
    ,{$group:{_id:{ 'type': '$type', 'user': '$user' },sumNum:{$sum:1}}}]);
  
  var buys = 0;
  var sells= 0;

  for(var i = 0;i<trans.length;i++){
    if(trans[i]._id.type == "买入"){
      buys++;
    }else{
      sells++;
    }
  }
  console.log("买入活跃人数:",buys);
  console.log("卖出活跃人数",sells);

  var trans_buy = await tranModel.aggregate([
    {
      $match:{
        tranc_time:{$gte:date},type:"买入"
      }
    }
    ,{$group:{_id:"$user",sumNum:{$sum:"$num"},sumAllNum:{$sum:"$allnum"}}},
    { $sort: { sumNum: -1 } },
    { $limit: 15 } 
  ]);
  console.log(trans_buy);

  var trans_sell = await tranModel.aggregate([
    {
      $match:{
        tranc_time:{$gte:date},type:"卖出"
      }
    },
    {$group:{_id:"$user",sumNum:{$sum:"$num"},sumAllNum:{$sum:"$allnum"}}},
    { $sort: { sumNum: -1 } }, 
    { $limit: 15 }  
  ]);

  console.log(trans_sell);

  var data_res = {
    sum_buy:buys,
    sum_sell:sells,
    top_buys:trans_buy,
    top_sells:trans_sell
  }
  
  res.json({
    code: 200,
    msg: "success",
    data: data_res,
  });
}

//获取资金线图数据
async function queryFlowHistory(req, res,next){

}


module.exports = {
  queryAll,
  queryCurrency,
  queryKline,
  queryByType,
  queryTrans,
  queryUserBi,
  queryTransByUser,
  queryMobility,
  queryMobiLp,
  queryNotices,
  queryRotations,
  currencyInfo,
  queryDown,
  queryFlash,
  queryMedia,
  queryBrows,
  queryCommunicate,
  queryMobilityAll,
  queryActiveUsers,
  queryFlowHistory,
  userBi
};

const kafka = require('kafka-node');
const {KAFKA_CONFIG} = require("../../database/index");
let producer;

async function getProducer() {
    if (!producer) {
        await createProducer();
    }
    return producer;
}

async function createProducer() {
    let promise = new Promise(async (resolve, reject) => {
        try {
            // 建立与kafka的连接
            let client = new kafka.KafkaClient({kafkaHost: KAFKA_CONFIG.host});
            // 创建生产者
            let Producer = kafka.Producer;
            producer = new Producer(client);
            producer.on('ready', function () {
                resolve();
            });
            producer.on('error', function (err) {
                client.close(); // 关闭客户端连接
                producer = null;
                console.log("createProducer===error");
                throw err;
            });
        } catch (e) {
            reject(e);
        }
    });
    await Promise.all([promise]).then(arr => {
    }, e => {
    })//等待所有线程解析完成;
}

module.exports = {
    getProducer
}
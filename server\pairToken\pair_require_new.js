
const redis = require("../database/redis");
const {delay,isUsdt,isBusd,formatAddress} = require("../utils/functions");

async function batchCallContracts() {
    let keys = await redis.hKeys("pair_json_chain");
    let pairs = [];
    let theard_promise = [];
    console.log("开始")
    for (let index = 0; index < keys.length; index++) {
        console.log(index);
        theard_promise.push(new Promise(async (resolve, reject) => {
            const element = keys[index];
            let flag_now = await redis.hGet("pair_json_hdata",formatAddress(element));
            if(flag_now){
               
            }else{
                pairs.push(element);
            }
            resolve();
        }));
        
    }
    await Promise.allSettled(theard_promise);
    console.log("开始插入");
    await redis.rPush("pair_json_all",pairs);
    console.log("插入完成");
}

batchCallContracts();
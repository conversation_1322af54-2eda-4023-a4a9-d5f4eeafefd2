const {Contract} = require('ethers')
const {ethers} = require('ethers')
const marketAbi = require("../abi/market.json")
const netbodyAbi = require("../abi/netbody.json")
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
const {userlist} = require("./userList");
const {mannalist} = require("./mannaList");
const rpc_listen = "https://bsc.blockpi.network/v1/rpc/156f516494b3a4024171a8045490d2d622e3a3b8"
const provider = new ethers.JsonRpcProvider(rpc_listen)
const marketAddress = "******************************************"
const netbodyAddresss = "******************************************"
let addressArray = ["******************************************"];
const resultsList = [];

async function main(netbody, market, address) {
    let result = await netbody.getNetBodyInfo(address)
    let list = result[2];
    list.forEach(async item => {
        let res = await market.identity(item);
        if (res[0] == 1) {
            let infos = {
                address: item,
                supperAddress: address,
                identity: res[0],
            }
            console.log("还在执行---", item)
            // resultsList.push(infos);
            resultsList.push(`${item},`);
        }
        await main(netbody, market, item)
    })
}

async function getMemberList() {

    const netbody = new Contract(netbodyAddresss, netbodyAbi, provider)
    const market = new Contract(marketAddress, marketAbi, provider)
    // 直接上父级地址
    await main(netbody, market, "******************************************");
    setTimeout(() => {
        try {
            // let allStr = JSONbig.stringify(resultsList)
            // redis.set("member_identity_list", allStr);
            console.log("存储结束", JSONbig.stringify(resultsList))
            resultsList.length = 0;
        } catch (e) {
            console.error("加载会员网体报错:", e);
        }
    }, 10000)
}

function getUserManna() {
    let list = []
    for (let i = 0; i < userlist.length; i++) {
        let user = userlist[i].toUpperCase();
        for (let j = 0; j < mannalist.length; j++) {
            let mannaUser = mannalist[j].toUpperCase();
            if (user == mannaUser) {
                list.push( userlist[i]);
            }

        }
    }
    console.log("执行结束",list,list.length)
}

// getUserManna()
getMemberList();
// module.exports = {
//     getMemberList
// }
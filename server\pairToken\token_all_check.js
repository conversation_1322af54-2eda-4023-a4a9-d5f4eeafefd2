

const redis = require("../database/redis");
const {formatAddress} = require("../utils/functions");
const {formatTimestampUTC} = require("../utils/DateUtils")
const token_key = "token_json";
const key = "token_check_json";

async function batchCallContracts() {
    try {
        let keys = await redis.hKeys(token_key);
        let all_token = [];
        for (let index = 0; index < keys.length; index++) {
            const element = keys[index];

            all_token.push(element);
        }
        console.log("开始插入");
        await redis.rPush(key,all_token);
        console.log("插入完成");
    } catch (error) {
        console.log(error);
    }
}

async function start(){

    // let address = "0x7bf81261357ecb766e1e462647ba26f9254e767d";
    // let all = await redis.hGet(token_key,address);
    // console.log(all);
        batchCallContracts();

    
}

start();
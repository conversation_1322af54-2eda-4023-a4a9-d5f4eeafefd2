let https = require("https");
const {formatTimestamp, getTimeStamp, formatDate_UTCDate} = require("../../utils/DateUtils");
const {default: BigNumber} = require("bignumber.js");
const FirstAnalyzeEnum = require("../../utils/enums/AnalyzeDorisKeyEnum");
BigNumber.config({DECIMAL_PLACES: 100});
const {getHeaders} = require("./config/httpHead")
const {redis} = require("../../utils/redisUtils");
const ave_url = "api.eskegs.com";
const {getTokenCache, formatStrDecimal} = require("../../utils/BaseDataUtils");
const {getTokenPsqlUsdt, USD_LIST} = require("./duneService");
const {HQL_sonar} = require("../../database/PqueryQL/PostgresSQL");

function decode(encode_data) {
    return JSON.parse(global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")));
}

async function getklineWeb(request, result) {
    try {
        let params = request.body
        let cacheOpen = request.cacheOpen;
        // if (cacheOpen !== false) {
            cacheOpen = false;
        // }
        if (!params.pairAddress || !params.period) {
            result.json({
                code: 400,
                msg: 'pairAddress,period参数不能为空',
                data: null
            })
            return;
        }
        if (cacheOpen) {
            // let objStr = await redis.hGet("webmagic_api:getklineWeb", params.pairAddress + "_" + params.period);
            // if (objStr) {
            //     result.json({
            //         code: 200, msg: '请求成功！', data: JSON.parse(objStr)
            //     });
            //     return;
            // }
        }
        let interval = 1;//默认一分钟
        switch (params.period) {
            case "1M":
                interval = 1;
                break;
            case "5M":
                interval = 5;
                break;
            case "15M":
                interval = 15;
                break;
            case "30M":
                interval = 30;
                break;
            case "1H":
                interval = 60;
                break;
            case "2H":
                interval = 120;
                break;
            case "4H":
                interval = 240;
                break;
            case "1D":
                interval = 1440;
                break;
        }
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=' + interval + '&category=u&count=800',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("getklineWeb:认证过期！->address{" + params.pairAddress + "}");
                        result.json({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data'];
                    if (!encode_data || encode_data == "") {
                        console.log("encode_data为空");
                        result.json({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let de_data = decode(encode_data)
                    let kline_data = de_data['kline_data'];
                    let item;
                    for (let i = 0; i < kline_data.length; i++) {
                        // item.time = formatTimestamp(item.time);
                        item = kline_data[i];
                        item["id"] = i;
                        item["pairId"] = i;
                        item["open"] = item.open;
                        item["high"] = item.high;
                        item["low"] = item.low;
                        item["close"] = item.close;
                        item["volume"] = item.volume;
                        item["klineTime"] = item.time;
                        item["period"] = "1M";
                        item["openTag"] = "";
                        item["closeTag"] = "";
                        item["amplitude"] = 0;
                        item["tradeAmount"] = 0;
                        item["priceChange"] = 0;
                        item["changePct"] = 0;
                        item["tradeNum"] = 0;
                        delete item.time;
                    }
                    result.json({
                        code: 200,
                        msg: "请求成功！",
                        data: kline_data
                    })
                } catch (e) {
                    result.json({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', function (e) {
            result.json({
                code: 500, msg: e, data: null
            })
        });
        req.on('timeout', () => {
            console.error('请求超时:getklineWeb');
            result.json({
                code: 500,
                msg: null,
                data: null
            })
        });
        req.setTimeout(60000);
        req.end();
    } catch (e) {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    }
}


async function five_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=5&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

async function fifteen_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=15&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

async function thirty_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=30&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

/**
 * 1小时
 * @param key
 * @returns {Promise<unknown>}
 */
async function sixty_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=60&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {
        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

/**
 * 2小时
 * @param key
 * @returns {Promise<unknown>}
 */
async function one_twenty_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=120&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

/**
 * 4小时
 * @param key
 * @returns {Promise<unknown>}
 */
async function two_forty_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=240&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.on('timeout', () => {
        console.error('请求超时:two_forty_k');
        result.json({
            code: 500,
            msg: null,
            data: null
        })
    });
    req.setTimeout(60000);
    req.end();

}

/**
 * 1天
 * @param key
 * @returns {Promise<unknown>}
 */
async function getklineWebD(request, result) {
    try {
        let params = request.body
        let cacheOpen = request.cacheOpen;
        // if (cacheOpen !== false) {
            cacheOpen = false;
        // }
        if (!params.pairAddress || !params.period) {
            result.json({
                code: 400,
                msg: 'pairAddress,period参数不能为空',
                data: null
            })
            return;
        }
        if (cacheOpen) {
            // let objStr = await redis.hGet("webmagic_api:getklineWebD", params.pairAddress + "_" + params.period);
            // if (objStr) {
            //     result.json({
            //         code: 200, msg: '请求成功！', data: JSON.parse(objStr)
            //     });
            //     return;
            // }
        }
        let interval = 1440;//默认一天
        switch (params.period) {
            case "1D":
                interval = 1440;
                break;
            case "1W":
                interval = 10080;
                break;
        }
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=' + interval + '&category=u&count=800',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("getklineWebD:认证过期！->address{" + params.pairAddress + "}");
                        result.json({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    if (!encode_data || encode_data == "") {
                        console.log("encode_data为空");
                        result.json({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                    }
                    let de_data = decode(encode_data)
                    let kline_data = de_data['kline_data'];
                    let item;
                    for (let i = 0; i < kline_data.length; i++) {
                        // item.time = formatTimestamp(item.time);
                        item = kline_data[i];
                        item["id"] = i;
                        item["pairId"] = i;
                        item["open"] = item.open;
                        item["high"] = item.high;
                        item["low"] = item.low;
                        item["close"] = item.close;
                        item["volume"] = item.volume;
                        item["klineTime"] = item.time;
                        item["period"] = "1M";
                        item["openTag"] = "";
                        item["closeTag"] = "";
                        item["amplitude"] = 0;
                        item["tradeAmount"] = 0;
                        item["priceChange"] = 0;
                        item["changePct"] = 0;
                        item["tradeNum"] = 0;
                        delete item.time;
                    }
                    result.json({
                        code: 200,
                        msg: "请求成功！",
                        data: kline_data
                    })
                } catch (e) {
                    result.json({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', function (e) {
            result.json({
                code: 500, msg: e, data: null
            })
        });
        req.on('timeout', () => {
            console.error('请求超时:getklineWebD');
            result.json({
                code: 500,
                msg: null,
                data: null
            })
        });
        req.setTimeout(60000);
        req.end();
    } catch (e) {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    }
}

/**
 * 1周
 * @param key
 * @returns {Promise<unknown>}
 */
async function week_k(request, result) {
    let params = request.body
    if (!params.pairAddress) {
        result.json({
            code: 400,
            msg: 'address参数不能为空',
            data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=10080&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                let encode_data = jsonData['encode_data']
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result.json({
                        code: 200,
                        msg: "数据为空！",
                        data: null
                    });
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;
                for (let i = 0; i < kline_data.length; i++) {
                    // item.time = formatTimestamp(item.time);
                    item = kline_data[i];
                    item["id"] = i;
                    item["pairId"] = i;
                    item["open"] = item.open;
                    item["high"] = item.high;
                    item["low"] = item.low;
                    item["close"] = item.close;
                    item["volume"] = item.volume;
                    item["klineTime"] = item.time;
                    item["period"] = "1M";
                    item["openTag"] = "";
                    item["closeTag"] = "";
                    item["amplitude"] = 0;
                    item["tradeAmount"] = 0;
                    item["priceChange"] = 0;
                    item["changePct"] = 0;
                    item["tradeNum"] = 0;
                    delete item.time;
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: kline_data
                })
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}


async function getKlineTradeList(request, result) {

    let params = request.body
    if (!params.token_address) {
        result.json({
            code: 500,
            msg: 'token_address参数不能为空',
            data: null
        })
        return;
    }

    let token_address = params.token_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address);
        tokenPrice = new BigNumber(tokenPrice);
        //查询交易对信息
        let pairsArg = `
            WITH combined_pairs AS (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY address  ORDER BY datetime DESC) AS rn
                FROM (
                    SELECT *
                    FROM MV_pairs_token0
                    WHERE token0 = '${token_address}'
            
                    UNION ALL
            
                    SELECT *
                    FROM MV_pairs_token1
                    WHERE token1 = '${token_address}'
                )
            )
            SELECT *
            FROM combined_pairs
            WHERE rn = 1

        `;


        let data = await HQL_sonar(pairsArg);
        if (data && data.length > 0) {
            let rows = data;
            let pairsArr=""
            for (let i = 0; i < rows.length; i++) {
                let jsons = rows[i];
                if(i==rows.length-1){
                    pairsArr+=`'${jsons.address}'` ;
                }
                else {
                    pairsArr+=`'${jsons.address}',` ;
                }
                if(i>50){
                    i = rows.length
                }
            }
            let trackSql = `
            SELECT 
                *
            FROM 
                MV_swaps
            WHERE 
                contract in (${pairsArr})
            ORDER BY datetime DESC LIMIT 500
            `;
            let hqlData = await HQL_sonar(trackSql);
            let otherToken = {};
            if(hqlData && hqlData.length>0){

                let dataList = [];
                for (let i = 0; i < hqlData.length; i++) {
                    let jsons = hqlData[i];
                    let currentPair;
                    for (let j = 0; j < rows.length; j++) {
                        let pair = rows[j];
                        if(pair.address == jsons.contract ){
                            currentPair = pair;
                            break;
                        }
                    }

                    //如果确定是和U的稳定币交易对，那么当时就可以算出当前价格
                    if ( USD_LIST.indexOf(currentPair.token0) != -1) {
                        //获得稳定币的实际交易量
                        let token0Amount;
                        if(jsons.amount0_in !='0'){
                            token0Amount = formatStrDecimal(jsons.amount0_in, 18);
                        }
                        else {
                            token0Amount = formatStrDecimal(jsons.amount0_out, 18);
                        }
                        //获得当前币的实际交易量

                        let token1Amount;
                        if(jsons.amount1_in != '0'){
                            token1Amount = formatStrDecimal(jsons.amount1_in, tokenDecimal);
                        }
                        else {
                            token1Amount = formatStrDecimal(jsons.amount1_out, tokenDecimal);
                        }

                        //获得当前币对稳定币的实际数量（金额）
                        let currentUsd = new BigNumber(token0Amount).div(new BigNumber(token1Amount))
                        jsons.tokenUsdt = currentUsd;
                    }
                    else if (USD_LIST.indexOf(currentPair.token1) != -1) {
                        //获得稳定币的实际交易量
                        let token0Amount;
                        if(jsons.amount0_in !='0'){
                            token0Amount = formatStrDecimal(jsons.amount0_in, tokenDecimal);
                        }
                        else {
                            token0Amount = formatStrDecimal(jsons.amount0_out, tokenDecimal);
                        }
                        //获得当前币的实际交易量

                        let token1Amount;
                        if(jsons.amount1_in != '0'){
                            token1Amount = formatStrDecimal(jsons.amount1_in, 18);
                        }
                        else {
                            token1Amount = formatStrDecimal(jsons.amount1_out, 18);
                        }

                        //获得当前币对稳定币的实际数量（金额）
                        let currentUsd = new BigNumber(token1Amount).div(new BigNumber(token0Amount))
                        jsons.tokenUsdt = currentUsd
                    }
                    else {
                        jsons.tokenUsdt = tokenPrice
                    }

                    //判断是token0还是1
                    if(currentPair.token0==token_address){
                        if(jsons.amount0_in !='0' ){
                            //当前代币进入到pair所以是卖出
                            jsons.type=0;
                            jsons.value = formatStrDecimal(jsons.amount0_in, tokenDecimal)
                        }
                        else {
                            //否则是买入
                            jsons.type=1;
                            jsons.value = formatStrDecimal(jsons.amount0_out, tokenDecimal)
                        }
                    }
                    else if(currentPair.token1==token_address){
                        if(jsons.amount1_in !='0' ){
                            //当前代币进入到pair所以是卖出
                            jsons.type=0;
                            jsons.value = formatStrDecimal(jsons.amount1_in, tokenDecimal)
                        }
                        else {
                            //否则是买入
                            jsons.value = formatStrDecimal(jsons.amount1_out, tokenDecimal)
                            jsons.type=1;
                        }
                    }

                    jsons.priceUsd = (new BigNumber(jsons.value).times(jsons.tokenUsdt)).toString()
                    jsons.time =formatTimestamp(new Date(jsons.datetime).getTime()+28800000);
                    let infos = {
                        amount:jsons.value,
                        amountUsd:jsons.tokenUsdt,
                        fromPriceUsd:jsons.priceUsd,
                        t:new Date(jsons.time).getTime()/1000,
                        transactionTime:jsons.time,
                        type:jsons.type==1?'from':'to',
                        walletAddress:'0x'+jsons.to,
                    }
                    dataList.push(infos)
                }

                let data_return = {
                    list: dataList,
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: data_return
                })
            }
            else {
                result.json({
                    code: 200,
                    msg: "交易数据为空！",
                    data: null
                })

            }
        }
        else {
            result.json({
                code: 500,
                msg: "未查询到交易对！",
                data: null
            })
        }
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}
async function getKlinePairs(request, result) {

    let params = request.body
    if (!params.token_address) {
        result.json({
            code: 500,
            msg: 'token_address参数不能为空',
            data: null
        })
        return;
    }

    let token_address = params.token_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        //查询交易对信息
        let pairsArg = `
            WITH combined_pairs AS (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY address ORDER BY datetime DESC) AS rn
                FROM (
                    SELECT *
                    FROM MV_pairs_token0
                    WHERE token0 = '${token_address}'
            
                    UNION ALL
            
                    SELECT *
                    FROM MV_pairs_token1
                    WHERE token1 = '${token_address}'
                )
            )
            SELECT cp.*, mls.reserve0, mls.reserve1
            FROM (
                SELECT * 
                FROM combined_pairs 
                WHERE rn = 1 
            ) AS cp
            LEFT JOIN (
                SELECT address, reserve0, reserve1
                FROM MV_LAST_syncs
                ORDER BY datetime DESC
                LIMIT 1 BY address
            ) AS mls
            ON cp.address = mls.address;
        `;


        let data = await HQL_sonar(pairsArg);
        let pairs = {};

        if (data && data.length > 0) {
            let rows = data;

            for (let i = 0; i < rows.length; i++) {
                let jsons = rows[i];
                let objStr = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", '0x'+jsons.address);
                if(objStr != null){
                    let infos = {
                        ...jsons,
                        ...JSON.parse(objStr)
                    }
                    pairs['0x'+jsons.address] = infos
                }
            }

            result.json({
                code: 200,
                msg: "交易数据为空！",
                data: pairs
            })
        }
        else {
            result.json({
                code: 500,
                msg: '当前token没有pair信息',
                data: null
            })

        }
    }
}

async function getKlineLiquidityList(request, result) {

    let params = request.body
    if (!params.token_address || !params.pair_address ) {
        result.json({
            code: 500,
            msg: 'token_address,pair_address参数不能为空',
            data: null
        })
        return;
    }

    let token_address = params.token_address;
    let pair_address = params.pair_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        pair_address = pair_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address);
        tokenPrice = new BigNumber(tokenPrice);

        //查询交易对信息
        let pairsArg = `
          WITH combined_pairs AS (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY address  ORDER BY datetime DESC) AS rn
                FROM (
                    SELECT *
                    FROM MV_pairs
                    WHERE address = '${pair_address}'
                )
            )
            SELECT *
            FROM combined_pairs
            WHERE rn = 1

        `;

        let data = await HQL_sonar(pairsArg);
        let tokens = {};
        let pairs = {};
        let token1_address = ''
        if (data && data.length > 0) {
            let rows = data;
            let pairsArr=""
            for (let i = 0; i < rows.length; i++) {
                let jsons = rows[i];
                if(i==rows.length-1){
                    pairsArr+=`'${jsons.address}'` ;
                }
                else {
                    pairsArr+=`'${jsons.address}',` ;
                }
                //得到所有的token
                if(!tokens[jsons.token0]){
                    let tokenInfo = await getTokenCache('0x'+jsons.token0);
                    tokens[jsons.token0] = tokenInfo
                }
                if(!tokens[jsons.token1]){
                    let tokenInfo = await getTokenCache('0x'+jsons.token1);
                    tokens[jsons.token1] = tokenInfo;
                }
                pairs[jsons.address] = jsons;
                pairs[jsons.address].token0_name = tokens[jsons.token0].symbol;
                pairs[jsons.address].token1_name = tokens[jsons.token1].symbol;

                if(jsons.token0==token_address){
                    token1_address = jsons.token1
                }
                else {
                    token1_address = jsons.token0
                }
            }

            console.log(pairs);
            let endTime =parseInt(new Date().getTime()/1000-2592000)
            let trackSql = `
               SELECT 
                    l.*,
                    t.to AS mint_two_address
               FROM (
                    SELECT 
                        l.*,
                        t.from AS mint_address
                    FROM 
                    (
                        SELECT *
                        FROM MV_liquiditys
                        WHERE address IN (${pairsArr})
                        ORDER BY f_time DESC
                        LIMIT 100
                    ) AS l
                    LEFT JOIN 
                    (
                        SELECT *
                        FROM transfers_v2_encoded
                        WHERE 
                            address = '${token_address}'
                            AND to IN (
                                '000000000000000000000000000000000000dead','0000000000000000000000000000000000000000','00000047bb99ea4d791bb749d970de71ee0b1a34'
                            )
                            AND from_is_contract = 0
                            AND f_time>${endTime}
                    ) AS t 
                    ON l.f_time = t.f_time
                    ORDER BY l.f_time desc
               ) AS l
               LEFT JOIN 
                (
                    SELECT *
                    FROM transfers_v2_encoded
                    WHERE 
                        address = '${token1_address}'
                        AND from = '${pair_address}'
                        AND to_is_contract = 0
                        AND f_time>${endTime}
                ) AS t 
                ON l.f_time = t.f_time
                    ORDER BY l.f_time desc
            `;
            let hqlData = await HQL_sonar(trackSql);
            console.log(hqlData)
            let otherToken ={};
            if(hqlData && hqlData.length>0){


                let dataList = [];
                for (let i = 0; i < hqlData.length; i++) {
                    let jsons = hqlData[i];
                    let currentPair;
                    for (let j = 0; j < rows.length; j++) {
                        let pair = rows[j];
                        if(pair.address == jsons.address ){
                            currentPair = pair;
                            break;
                        }
                    }
                    //判断是token0还是1
                    if(currentPair.token0==token_address){
                        jsons.type=0;
                        jsons.amount0 = formatStrDecimal(jsons.amount0, tokenDecimal)
                        jsons.amount0_usd = (new BigNumber(jsons.amount0).times(tokenPrice))
                        jsons.value = jsons.amount0
                        //如果这个代币存在列表中，就不用再次请求了
                        if(!otherToken[currentPair.token1]){
                            otherToken[currentPair.token1] = await getTokenCache('0x'+currentPair.token1);
                            otherToken[currentPair.token1]['usdt_price'] = new BigNumber(await getTokenPsqlUsdt(currentPair.token1));
                        }
                        jsons.amount1= formatStrDecimal(jsons.amount1, otherToken[currentPair.token1].decimals);
                        jsons.amount1_usd = (new BigNumber(jsons.amount1).times(otherToken[currentPair.token1]['usdt_price']))
                    }
                    else if(currentPair.token1==token_address){

                        jsons.type=1;
                        jsons.amount1 = formatStrDecimal(jsons.amount1, tokenDecimal);
                        jsons.amount1_usd = (new BigNumber(jsons.amount1).times(tokenPrice))
                        jsons.value = jsons.amount1
                        //如果这个代币存在列表中，就不用再次请求了
                        if(!otherToken[currentPair.token0]){
                            otherToken[currentPair.token0] = await getTokenCache('0x'+currentPair.token0);
                            otherToken[currentPair.token0]['usdt_price'] = new BigNumber(await getTokenPsqlUsdt(currentPair.token0));
                        }
                        jsons.amount0= formatStrDecimal(jsons.amount0, otherToken[currentPair.token0].decimals);
                        jsons.amount0_usd = (new BigNumber(jsons.amount0).times(otherToken[currentPair.token0]['usdt_price']))
                    }


                    let info = {
                        amount0:jsons.amount0,
                        amount1:jsons.amount1,
                        amountUsd:jsons.amount0_usd,
                        amountUsdPair:jsons.amount0_usd.plus(jsons.amount1_usd),
                        time:jsons.f_time,
                        token0Symbol:pairs[jsons.address].token0_name,
                        token1Symbol:pairs[jsons.address].token1_name,
                        type:jsons.mint,
                        walletAddress:'',
                    }
                    if(jsons.mint){
                        if(jsons.mint_address.indexOf("\x00\x00\x00\x00\x00")==-1 &&  jsons.mint_address !=null  ){

                            info.walletAddress = jsons.mint_address
                        }
                        else {
                            info.walletAddress = jsons.mint_two_address

                        }
                    }
                    else {
                        info.walletAddress = jsons.to
                    }
                    //过滤掉那个数据
                    if(info.walletAddress.indexOf("\x00\x00\x00\x00\x00")==-1){
                        dataList.push(info)

                    }
                }

                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: dataList
                })
            }
            else {
                result.json({
                    code: 200,
                    msg: "数据为空！",
                    data: null
                })

            }
        }
        else {
            result.json({
                code: 500,
                msg: "未查询到交易对！",
                data: null
            })
        }
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}

async function getKlineLiquidityChart(request, result) {

    let params = request.body
    if (!params.token_address || !params.pair_address ) {
        result.json({
            code: 500,
            msg: 'token_address,pair_address参数不能为空',
            data: null
        })
        return;
    }

    let token_address = params.token_address;
    let pair_address = params.pair_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        pair_address = pair_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address);
        tokenPrice = new BigNumber(tokenPrice);
        //查询交易对信息
        let pairsArg = `
          WITH combined_pairs AS (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY address  ORDER BY datetime DESC) AS rn
                FROM (
                    SELECT *
                    FROM MV_pairs
                    WHERE address = '${pair_address}'
                )
            )
            SELECT *
            FROM combined_pairs
            WHERE rn = 1

        `;

        let data = await HQL_sonar(pairsArg);
        if (data && data.length > 0) {
            let rows = data;
            let pairsArr=""
            for (let i = 0; i < rows.length; i++) {
                let jsons = rows[i];
                if(i==rows.length-1){
                    pairsArr+=`'${jsons.address}'` ;
                }
                else {
                    pairsArr+=`'${jsons.address}',` ;
                }
            }

            let q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 604800000)));//往前退7天

            let trackSql = `
                SELECT 
                    *,
                    toMonth(f_time) AS month,
                    toDayOfMonth(f_time) AS day
                FROM 
                    MV_liquiditys
                WHERE 
                    address in (${pairsArr})
                    and f_time>=${q_0_time}
                ORDER BY f_time DESC 
            `;
            let hqlData = await HQL_sonar(trackSql);
            console.log(hqlData)
            let otherToken ={};
            let infos = {};
            if(hqlData && hqlData.length>0){


                let dataList = [];
                for (let i = 0; i < hqlData.length; i++) {
                    let jsons = hqlData[i];
                    let currentPair;
                    for (let j = 0; j < rows.length; j++) {
                        let pair = rows[j];
                        if(pair.address == jsons.address ){
                            currentPair = pair;
                            break;
                        }
                    }
                    //判断是token0还是1
                    if(currentPair.token0==token_address){
                        jsons.type=0;
                        jsons.amount0 = formatStrDecimal(jsons.amount0, tokenDecimal)
                        jsons.amount0_usd = (new BigNumber(jsons.amount0).times(tokenPrice))
                        //如果这个代币存在列表中，就不用再次请求了
                        if(!otherToken[currentPair.token1]){
                            otherToken[currentPair.token1] = await getTokenCache('0x'+currentPair.token1);
                            otherToken[currentPair.token1]['usdt_price'] = new BigNumber(await getTokenPsqlUsdt(currentPair.token1));
                        }
                        jsons.amount1= formatStrDecimal(jsons.amount1, otherToken[currentPair.token1].decimals);
                        jsons.amount1_usd = (new BigNumber(jsons.amount1).times(otherToken[currentPair.token1]['usdt_price']))
                    }
                    else if(currentPair.token1==token_address){

                        jsons.type=1;
                        jsons.amount1 = formatStrDecimal(jsons.amount1, tokenDecimal);
                        jsons.amount1_usd = (new BigNumber(jsons.amount1).times(tokenPrice))
                        //如果这个代币存在列表中，就不用再次请求了
                        if(!otherToken[currentPair.token0]){
                            otherToken[currentPair.token0] = await getTokenCache('0x'+currentPair.token0);
                            otherToken[currentPair.token0]['usdt_price'] = new BigNumber(await getTokenPsqlUsdt(currentPair.token0));
                        }
                        jsons.amount0= formatStrDecimal(jsons.amount0, otherToken[currentPair.token0].decimals);
                        jsons.amount0_usd = (new BigNumber(jsons.amount0).times(otherToken[currentPair.token0]['usdt_price']))
                    }
                    let amount_all = parseFloat(jsons.amount0_usd)+parseFloat(jsons.amount1_usd);
                    if(!infos[`${jsons.month}-${jsons.day}`]){
                        infos[`${jsons.month}-${jsons.day}`] = {
                            mint:0,
                            burn:0,
                        }
                    }
                    if(jsons.mint){
                        infos[`${jsons.month}-${jsons.day}`].mint +=amount_all;
                    }
                    else {
                        infos[`${jsons.month}-${jsons.day}`].burn +=amount_all;

                    }
                }
                let resultData = {
                    time:[],
                    mint:[],
                    burn:[],
                }
                for (const resultDataKey in infos) {
                    resultData.time.push(resultDataKey);
                    resultData.mint.push(infos[resultDataKey].mint);
                    resultData.burn.push(infos[resultDataKey].burn);
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: resultData
                })
            }
            else {
                result.json({
                    code: 200,
                    msg: "数据为空！",
                    data: null
                })

            }
        }
        else {
            result.json({
                code: 500,
                msg: "未查询到交易对！",
                data: null
            })
        }
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}

module.exports = {
    getklineWeb,
    getklineWebD,
    getKlineTradeList,
    getKlinePairs,
    getKlineLiquidityList,
    getKlineLiquidityChart
}

const express = require('express');
const router = express.Router();
const service = require('../services/monitorService');

//检测
router.post('/cateList', service.cateList);
router.post('/addCate',service.addCate);
router.post('/findCate',service.findAllCate);
router.post('/addContract',service.addMonitorAddress);
router.post('/addAddress',service.addMonitorUserAddress);
router.post('/findAddress',service.findAllMonitorAddress);
router.post('/findInfo',service.findAddressInfo);

module.exports = router;
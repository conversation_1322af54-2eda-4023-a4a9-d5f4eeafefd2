const RedisClient = require("../database/redis/index3");
const constant = require("../utils/constant");
const TokenModel = require("../model/mysql/TokenModel");
const {dbTokenQuery, dbInsertToken, dbUpdateToken} = require("../model/mysql/AviTokenModel");
const {isContractAddress} = require("../utils/EtherUtils");
const {formatTimestamp} = require("../utils/DateUtils");
let uuid = require("uuid");
const redis = require("../database/redis");
/**
 *
 * @param { String  } address
 * @returns { JSON | TokenModel }
 */
async function getCacheTokenByAddress(address) {
    // const redis = new RedisClient();
    const exists = null;//await redis.hExists(constant.TOKEN_CACHE_KEY, address);
    let row = false;
    if (exists) {
        row = await redis.hGet(constant.TOKEN_CACHE_KEY, address).catch(e => {
            console.error(constant.TOKEN_CACHE_KEY + "获取失败", e.message);
        });
        if (row) row = JSON.parse(row);
    } else {
        const token = await TokenModel().findOne({
            where: {address: address},
            attributes: ['id', 'chain_id','pair_id','address', 'total_supply', 'decimals'],
        });
        if (token) {
            row = token.dataValues;
            // await redis.hSet(constant.TOKEN_CACHE_KEY, address, row).catch(e => {
            //     console.error(constant.TOKEN_CACHE_KEY + "设置失败", e.message);
            // });
        }
    }
    return row;
}

/**
 *
 * @param { String } address
 * @param { Number } blockNumber
 * @param { Number } blockTime
 * @param { String } transactionHash
 * @returns { boolean | TokenModel }
 */
async function getToken(address, blockNumber, blockTime, transactionHash) {
    let obj={}
    if (!isContractAddress(address)) {
        console.log("不是一个合约,哈希:%s,地址:%s", transactionHash, address);
        return null;
    }
    const TOKEN_KEY = 'tokens';
    /** @type { RedisClient } */
    const redis = new RedisClient();
    const isExists = await redis.hExists(TOKEN_KEY, address);
    if (isExists) {
        return null;
    }
    // const contract = new Contract(address, tokenAbi, provider);
    let name = "";
    let symbol = "";
    let totalSupply = "";
    let decimals = "18";
    let is_open_source = 1;
    // let checkField = {};//doris
    // let checkField_1 = {};//mysql
    // try {
    // name = await contract.name().catch(e => console.log("错误5",e.message));
    // symbol = await contract.symbol().catch(e => console.log("错误6",e.message));
    // totalSupply = await contract.totalSupply().catch(e => console.log("错误7",e.message));
    // decimals = await contract.decimals().catch(e => console.log("错误8",e.message));
    // if(decimals == void 0){
    //     return false;
    // }
    // console.log("decimals---",decimals);
    // decimals = Number(decimals);
    // totalSupply = formatUnits(totalSupply,decimals);

    // const checkData = await checkToken(56, address);
    // if (checkData) {
    //     let riskScore = getRiskScore(checkData);
    //     //doris插入的内容
    //     checkField = {
    //         holders: checkData.holder_count || 0,
    //         lp_holder_count: checkData.lp_holder_count || 0,
    //         owner_address: checkData.owner || '',
    //         creator_address: checkData.creator_address || '',
    //         is_discard_permission: checkData.can_take_back_ownership == 1 ? 0 : 1,
    //         is_mintable: checkData.is_mintable || 0,
    //         buy_tax: checkData.buy_tax ? checkData.buy_tax + '' : '',
    //         sell_tax: checkData.sell_tax ? checkData.sell_tax + '' : '',
    //         is_in_dex: checkData.is_in_dex ? checkData.is_in_dex : 0,
    //         risk_score: riskScore ? riskScore : 0,
    //     }
    //     //mysql插入的内容
    //     checkField_1 = {
    //         holders: checkData.holder_count || 0,
    //         lp_holder_count: checkData.lp_holder_count || 0,
    //         owner_address: checkData.owner || '',
    //         creator_address: checkData.creator_address || '',
    //         is_discard_permission: checkData.can_take_back_ownership == 1 ? 0 : 1,
    //         is_mintable: checkData.is_mintable || 0,
    //         buy_tax: checkData.buy_tax ? checkData.buy_tax : 0,
    //         sell_tax: checkData.sell_tax ? checkData.sell_tax : 0,
    //         is_in_dex: checkData.is_in_dex ? checkData.is_in_dex : 0,
    //         risk_score: riskScore ? riskScore : 0,
    //     }
    // }
    // } catch (error) {
    //     if (error.message.indexOf("missing revert data") ||
    //         error.message.indexOf("could not decode result data")) {
    //         //console.warn("有可能是未开源的");
    //         is_open_source = 0;
    //     } else {
    //         console.error("获取代币信息失败" + blockNumber, error.message);
    //     }
    // }
    //代币地址
    const docs = {
        address: address,
        chain_id: 56,
        name: name,
        symbol: symbol,
        total_supply: totalSupply,
        decimals: Number(decimals),
        created_block: blockNumber,
        created_time: formatTimestamp(blockTime),
        is_open_source: is_open_source,
        id :uuid.v4()
        // ...checkField
    };
    let row = `${docs.address?docs.address:''},${docs.created_time?docs.created_time:''},${docs.created_block?docs.created_block:0},${docs.chain_id?docs.chain_id:0},${docs.logo_url?docs.logo_url:''},${docs.name?docs.name:''},${docs.symbol?docs.symbol:''},${docs.total_supply?docs.total_supply:''},${docs.decimals?docs.decimals:0},${docs.holders?docs.holders:0},${docs.lp_holder_count?docs.lp_holder_count:0},${docs.is_open_source?docs.is_open_source:0},${docs.is_mintable?docs.is_mintable:0},${docs.current_price_usd?docs.current_price_usd:''},${docs.price_change?docs.price_change:''},${docs.pair_id?docs.pair_id:''},${docs.risk_score?docs.risk_score:0},${docs.buy_tax?docs.buy_tax:''},${docs.sell_tax?docs.sell_tax:''},${docs.is_in_dex?docs.is_in_dex:0},${docs.is_discard_permission?docs.is_discard_permission:''},${docs.max_supply?docs.max_supply:''},${docs.owner_address?docs.owner_address:''},${docs.status?docs.status:1},${docs.creator_address?docs.creator_address:''},${docs.publish_time?docs.publish_time:''},${docs.id?docs.id:''}`;
    const docs_1 = {
        address: address,
        chain_id: 56,
        name: name,
        symbol: symbol,
        total_supply: totalSupply,
        decimals: Number(decimals),
        created_block: blockNumber,
        created_time: blockTime,
        is_open_source: is_open_source
        // ...checkField_1
    };
    //查询doris
    // let tokenInfos = await dbTokenQuery([{
    //     type: "string",
    //     column: "address",
    //     value: address
    // }]);

    obj={docs:row,docs_1:docs_1}
    return obj;
    //查询mysql
    // let tokenInfo = await TokenModel().findOne({
    //     where: {
    //         address: address
    //     }
    // });
    // if (!tokenInfo) {
    //     await TokenModel().create(docs_1);//插入mysql
    // }
    // if (blockNumber) {
    //     //这里需要填补参数是因为如果上面是更新则会delete这几个参数
    //     redis.hSet(TOKEN_KEY, address, docs);
    // }
}


/**
 *
 * @param { Number  } tokenAddr
 * @returns { JSON | TokenModel }
 */
async function getCacheTokenById(tokenAddr) {
    /** @type { RedisClient } */
    // const redis = new RedisClient();
    const exists = await redis.hExists(constant.TOKEN_CACHE_KEY, tokenId);
    let row = false;
    if (exists) {
        row = await redis.hGet(constant.TOKEN_CACHE_KEY, tokenId).catch(e => {
            console.error(constant.TOKEN_CACHE_KEY + "获取失败", e.message);
        });
        if (row) row = JSON.parse(row);
    } else {
        const token = await TokenModel().findByPk(tokenId, {
            attributes: ['id', 'address', 'total_supply', 'decimals', 'chain_id'],
        });
        if (token) {
            row = token.dataValues;
            await redis.hSet(constant.TOKEN_CACHE_KEY, tokenId, row).catch(e => {
                console.error(constant.TOKEN_CACHE_KEY + "设置失败", e.message);
            });
        }
    }
    return row;
}


module.exports = {
    getCacheTokenByAddress,
    getToken,
    getCacheTokenById,
}
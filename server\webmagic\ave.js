/**
 * 描述: 用户路由模块
 * 作者: <PERSON>
 * 日期: 2020-06-20
 */

const express = require("express");
const router = express.Router();
const { getRandomWallet, getWalletBalance, getWalletBalanceTask } = require(
  "../services/webmagic/redPacketService",
);
const { validateCode, createCode } = require(
  "../services/webmagic/giftCardService",
);
const { findToken, tokenSecurity, pkSecurity, findWalletAddressU,findUsdt} = require(
  "../services/webmagic/javaService",
);
const {
  getPairBase,
  getHolder,
  get_balance,
  get_nft_list,
  get_complex_protocol_list,
  get_history_list,
} = require(
  "../services/webmagic/aveService",
);
const {
  getPkLpHold,
  getLpAddressBalance,
  getLpAddressCountRatio,
  getTokenHand_Chart,
  getLpAddressContract,
  getLpAddressContractOut,
  getTradeAmount_Chart,
  getTradeTop_Chart,
  getPool_Chart,
  getContractsHand_Base,
  getContractsHand_Chart,
  getPoolRatio_Chart,
  getContractsHolder,
  getTokenHandAmount_Chart,
  getChangeHand_Chart,
  getCirculationRatio_Chart,
  getTokenMaxTotal,
  setTokenMaxTotal
} = require("../services/webmagic/duneService");
const {
  getLiquidityList,
  getTrackInfo,
  getTradeList,
  getTransferList,
  getAllByUser,
} = require("../services/webmagic/trackService");
const {
  tradeGetPage,
  liquidityGetPage,
  transferGetPage,
} = require("../services/webmagic/monitorService");
const {
  getklineWeb,
  getklineWebD,
  getKlineTradeList,
  getKlinePairs,
  getKlineLiquidityList,
  getKlineLiquidityChart
} = require("../services/webmagic/klineService");

const {
  getWalletTokenList,
} = require("../services/webmagic/observeWalletService");

const {
  getChinaMemberInfo,verifyMember
} = require("../services/webmagic/memberService");

const {
  hotTop,
} = require("../services/webmagic/topPairService");
const {
  addEvaluation,
  showEvaluation,
  getNftList,
  getComplexProtocolList,
  getHistoryList,
  getBalance,
  initAddress,
  getCurrency,
  deleteEvaluation,
  Main_chips,
} = require("../services/webmagic/homeEvaluationService");

//注册
router.get("/balance", get_balance);
router.get("/nft_list", get_nft_list);
router.get("/complex_protocol_list", get_complex_protocol_list);
router.get("/history_list", get_history_list);

router.get("/getPairK", getPairBase); //池子
router.get("/getPairK/holder", getHolder); //持币人
router.get("/topPair/hotTop", hotTop); //热搜
router.post("/kline/getkline", getklineWeb); //获取kline
router.post("/kline/day", getklineWebD); //一天
router.post("/kline/getTradeList", getKlineTradeList); //交易
router.post("/kline/getKlinePairs", getKlinePairs); //池子
router.post("/kline/getKlineLiquidityList", getKlineLiquidityList); //流动性记录
router.post("/kline/getKlineLiquidityChart", getKlineLiquidityChart); //流动性记录
router.post("/track/getLiquidityList", getLiquidityList); //获取流动性
router.post("/track/getAllByUser", getAllByUser); //获取币
router.post("/track/getTrackInfo", getTrackInfo); //获取追踪信息
router.post("/track/getTradeList", getTradeList); //获取交易列表
router.post("/track/getTransferList", getTransferList); //获取转账列表
router.post("/home/<USER>", addEvaluation); //首页数据评测添加
router.post("/home/<USER>", deleteEvaluation); //首页数据评测删除
router.post("/home/<USER>", showEvaluation); //首页数据评测显示
router.post("/home/<USER>/chipsTime", Main_chips); //持币人top
router.post("/home/<USER>/getNftList", getNftList); //持币人top
router.post("/home/<USER>/getComplexProtocolList", getComplexProtocolList); //持币人top
router.post("/home/<USER>/getHistoryList", getHistoryList); //持币人top
router.post("/home/<USER>/getBalance", getBalance); //持币人top
router.post("/home/<USER>/initAddress", initAddress); //持币人top
router.post("/home/<USER>/getCurrency", getCurrency); //持币人top
/**
 * 红包相关
 */
router.post("/giftCard/validateCode", validateCode); //验证礼品卡是否合格
router.post("/giftCard/createCode", createCode); //验证礼品卡是否合格

/**
 * 红包相关
 */
router.post("/redPacket/getRandomWallet", getRandomWallet); //获取充值二维码
router.post("/redPacket/getWalletBalance", getWalletBalance); //获取充值金额
router.post("/redPacket/getWalletBalanceTask", getWalletBalanceTask); //获取充值金额
/**
 * 天眼监控
 */
router.post("/monitor/trade/getPage", tradeGetPage); //获取交易
router.post("/monitor/liquidity/getPage", liquidityGetPage); //获取流动性
router.post("/monitor/transfer/getPage", transferGetPage); //获取转账
/**
 * 声纳后端接口
 * @type {Router}
 */
router.post("/java/findUsdt", findUsdt); //搜素token的价格
router.post("/java/findToken", findToken); //搜素token栏目表
router.post("/java/tokenSecurity", tokenSecurity); //搜素lp锁仓持有
router.post("/java/pkSecurity", pkSecurity); //pk数据集
router.post("/java/findWalletAddressU", findWalletAddressU); //验证hashcode是否存在

/**
 * the graph api
 * @type {Router}
 */
router.post("/dune/getPkLpHold", getPkLpHold); //Pk详情页面的lp质押，占比
router.post("/dune/getLpAddressBalance", getLpAddressBalance); //LP数量走势图
router.post("/dune/getLpAddressCountRatio", getLpAddressCountRatio); //Lp占比走势图
router.post("/dune/getTokenHand_Chart", getTokenHand_Chart); //持币地址图表
router.post("/dune/getLpAddressContract", getLpAddressContract); //LP地址（合约内）
router.post("/dune/getLpAddressContractOut", getLpAddressContractOut); //Lp地址(合约外：EOA)图表
router.post("/dune/getTradeAmount_Chart", getTradeAmount_Chart); //交易额分析
router.post("/dune/getTradeTop_Chart", getTradeTop_Chart); //交易排行榜
router.post("/dune/getPool_Chart", getPool_Chart); //池子大小
router.post("/dune/getPoolRatio_Chart", getPoolRatio_Chart); //底池币占比
router.post("/dune/getChangeHand_Chart", getChangeHand_Chart); //换手率图表
router.post("/dune/getCirculationRatio_Chart", getCirculationRatio_Chart); //流通币走势图
router.post("/dune/getTokenHandAmount_Chart", getTokenHandAmount_Chart); //持币数量图表
router.post("/dune/getContractsHand_Base", getContractsHand_Base); //合约持币
router.post("/dune/getContractsHand_Chart", getContractsHand_Chart); //合约持币走势图
router.post("/dune/getContractsHolder", getContractsHolder); //单独合约接口

router.post("/dune/getTokenMaxTotal", getTokenMaxTotal); //获取TOKEN最大供应量
router.post("/dune/setTokenMaxTotal", setTokenMaxTotal); //设置TOKEN最大供应量

/**
 * 会员网体
 */
router.post("/member/getMemberIdentityList", getChinaMemberInfo); //获取TOKEN最大供应量
/**
 * 会员网体
 */
router.post("/member/verify", verifyMember);

/**
 * 观察千百哦
 */
router.post("/member/getWalletTokenList", getWalletTokenList); //获取TOKEN最大供应量

module.exports = router;
0
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis_in");
const {getTokenAAndBPriceByToken} = require("../utils/klineUtils");
const {dbTransferQuery} = require('../model/mysql/AviBaseTransferModel');
const {slAveTransfer} = require('../utils/doris/aveTransferSL')
const {slAveTransferUser} = require('../utils/doris/aveTransferUserSL')
const key_all = "block_trans_list";
const {formatUnits} = require("ethers");
const {delay} = require('../utils/functions');
const {default: BigNumber} = require('bignumber.js');
const {StringToTimeStamp, formatTimestamp, formatDateToString} = require("../utils/DateUtils");
const FirstAnalyzeEnum = require("../utils/enums/AnalyzeDorisKeyEnum");
let tokenCahche = {};
let pairCahche = {};

async function getCache(type, key) {
    /**
     * 判断缓存
     */
    let obj;
    switch (type) {
        case "token":
            if (!tokenCahche[key]) {
                obj = JSONbig.parse(await redis.hGet("token_json_hdata", key));
                tokenCahche[key] = obj;
            } else {
                obj = tokenCahche[key];//存入缓存
            }
            break;
        case "pair":
            if (!pairCahche[key]) {
                obj = JSONbig.parse(await redis.hGet("pair_qualified_table", key));
                pairCahche[key] = obj;
            } else {
                obj = pairCahche[key];//存入缓存
            }
            break;
    }
    return obj;
}

async function initCache() {
    /**
     * 初始化缓存
     */
    tokenCahche = {};
    pairCahche = {};
}

async function startRun() {
    try {
        while (true) {
            let block_listen = await redis.lPop(key_all);
            // await redis.lPush(key_all,block_listen);
            if (!block_listen) {
                console.log("等待新数据。。");
                await delay(5000);
                continue;
            }
            await initCache();
            let start = Date.now()
            let block_list = block_listen.split(',');
            console.log('开始查询' + block_list[0] + "_" + block_list[1])
            const transferList = await dbTransferQuery([{
                type: ">=",
                column: "block_number",
                value: Number(block_list[0])
            }, {
                type: "<",
                column: "block_number",
                value: Number(block_list[1])
            }]);
            if (transferList.length > 0) {
                let number_time = {};
                let insertList = [];
                let holdArrList = [];
                let holdMoneyList = {};
                let threadp = [];
                for (let i = 0; i < transferList.length; i++) {
                    //记录所有的block_number和time
                    number_time[transferList[i].block_number] = transferList[i].block_number + "_" + formatDateToString(transferList[i].block_time);
                    //执行解析
                    if (!transferList[i].quantity) {
                        continue;
                    }
                    threadp.push(new Promise(async (resolve, reject) => {
                        try {
                            /**
                             * 符合合格名单则解析
                             */
                            let token_addr = await getCache("token", transferList[i].token_address);
                            if (token_addr && token_addr.pair_address) {
                                let pair_addr = await getCache("pair", token_addr.pair_address);
                                if (pair_addr) {
                                    try {
                                        let price;//(await getTokenAAndBPrice(pair_addr, transferList[i].block_number, transferList[i].log_index)).priceA;
                                        price = await getTokenAAndBPriceByToken(pair_addr, transferList[i].block_time, transferList[i].log_index);
                                        if (price == undefined) {
                                            price = new BigNumber(0);
                                        }
                                        let info = {
                                            from_address: transferList[i].from_address,
                                            to_address: transferList[i].to_address,
                                            transfer_time: formatTimestamp(transferList[i].block_time),
                                            token_address: transferList[i].token_address,
                                            wallet_from: transferList[i].wallet_from,
                                            wallet_to: transferList[i].wallet_to,
                                            trans_hash: transferList[i].trans_hash,
                                            log_index: transferList[i].log_index,
                                            quantity: transferList[i].quantity,
                                            block_number: transferList[i].block_number,
                                            token_price: price.toFixed(18),//需要算
                                            amount_usd: "0",//通过token_price*quantity得到金额
                                        }
                                        //获取当前转账数量
                                        let quantity = new BigNumber(formatUnits(transferList[i].quantity, token_addr.decimals))
                                        //得到转账金额
                                        info.amount_usd = price.multipliedBy(quantity).toFixed(18);
                                        /**
                                         * form的钱包地址
                                         * @type {string}
                                         */
                                        let from_str = `${transferList[i].block_number}_${transferList[i].token_address}_${transferList[i].wallet_from}`;
                                        let from_obj = {
                                            current_price: price.toFixed(18) + "_" + transferList[i].log_index,
                                            address: transferList[i].wallet_from,
                                            token_address: transferList[i].token_address,
                                            quantity: transferList[i].quantity,
                                            block_number: transferList[i].block_number,
                                            block_time: formatTimestamp(transferList[i].block_time),
                                            type: null
                                        }
                                        holdMoneyList[from_str] = from_obj
                                        /**
                                         * to的钱包地址
                                         * @type {string}
                                         */
                                        let to_str = `${transferList[i].block_number}_${transferList[i].token_address}_${transferList[i].wallet_to}`;
                                        let to_obj = {
                                            current_price: price.toFixed(18) + "_" + transferList[i].log_index,
                                            address: transferList[i].wallet_to,
                                            token_address: transferList[i].token_address,
                                            quantity: transferList[i].quantity,
                                            block_number: transferList[i].block_number,
                                            block_time: formatTimestamp(transferList[i].block_time),
                                            type: null
                                        }
                                        holdMoneyList[to_str] = to_obj
                                        insertList.push(info);
                                    } catch (ex) {
                                        console.log(ex);
                                    }
                                }

                            }
                            resolve();
                        } catch (ex) {
                            console.log(ex);
                            reject(false);
                        }
                    }));
                    if (threadp.length >= 100) {
                        await Promise.allSettled(threadp);
                        threadp.length = 0;
                    }
                }
                if (threadp.length > 0) {
                    await Promise.allSettled(threadp);
                    threadp.length = 0;
                }
                let rows = [];
                /**
                 * 写入slAveTransfer
                 */
                for (let index = 0; index < insertList.length; index++) {
                    let element = insertList[index];
                    let row = `${element.token_address}|${element.transfer_time}|${element.log_index}|${element.block_number}|${element.from_address}|${element.to_address}|${element.wallet_from}|${element.wallet_to}|${element.trans_hash}|${element.quantity}|${element.token_price}|${element.amount_usd}`;
                    rows.push(row);
                }
                slAveTransfer(rows, block_listen);
                /**
                 * 写入slAveTransferUser
                 */
                let holdMoneyKeys = Object.keys(holdMoneyList);
                for (let j = 0; j < holdMoneyKeys.length; j++) {
                    holdArrList.push(holdMoneyList[holdMoneyKeys[j]])
                }
                holdMoneyKeys = {};
                let holders = [];
                for (let indexi = 0; indexi < holdArrList.length; indexi++) {
                    let element = holdArrList[indexi];
                    let row = `${element.address}|${element.token_address}|${element.block_time}|${element.block_number}|0|${element.current_price}|0|0|${element.quantity}|${element.type}`;
                    holders.push(row);
                }
                slAveTransferUser(holders, block_listen);
            }
            /**
             * 写入当前redis锁到达位置
             */
            await redis.hSet(FirstAnalyzeEnum.priceTime_lock, "parseTrans", block_list[1]);//获取redis上锁的值
            console.log('解析完成blockNumber:100,transLog:' + transferList.length + ',耗时', (Date.now() - start) + "ms")
        }
    } catch (ex) {
        console.log(ex);
    }


}

// startRun();

module.exports = {
    startRun
}
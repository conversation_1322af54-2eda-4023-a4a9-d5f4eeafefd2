const { GoPlus, ErrorCode } =  require("@goplus/sdk-node");
// const boom = require('boom');
// const { validationResult } = require('express-validator');
/**
 * 描述: 业务逻辑处理 - 检测相关接口
 * 作者: Jack <PERSON>
 * 日期: 2020-06-20
*/
  //检测
  async function check(req, res ,next){
    // const err = validationResult(req);
    // // 如果验证错误，empty不为空
    // if (!err.isEmpty()) {
    //   // 获取错误信息
    //   const [{ msg }] = err.errors;
    //   // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回 
    //   next(boom.badRequest(msg));
    // } else {
      let { checkType,address,chainId,tokenId } = req.body;
      let addresses = [address];
      if(checkType == 'Token'){
        let result = await GoPlus.tokenSecurity(chainId, addresses, 30);
        if (result.code != ErrorCode.SUCCESS) {
          res.json({code:500, msg:"success",data:false});
          return;
        } else {
          res.json({code:200,msg:"success",data:{tokenSecurity: result.result[address]}});
          return;
        }
      }else if(checkType == 'NFT'){
        let result = await GoPlus.nftSecurity(chainId, addresses, tokenId, 30);
        if (result.code != ErrorCode.SUCCESS) {
          res.json({code:500, msg:"success",data:result.code});
          return;
        } else {
          res.json({code:200,msg:"success",data:{nftSecurity: result.result}});
          return;
        }
      }
      res.json({code:500,msg:"获取失败",data:0});
      return;
    //}
  }


module.exports = {
  check
}

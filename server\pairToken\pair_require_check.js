
const redis = require("../database/redis_in");
const {delay,isUsdt,isBusd,formatAddress} = require("../utils/functions");

async function batchCallContracts() {
    let keys = await redis.hKeys("pair_json_hdata");
    let pairs = [];
    let theard_promise = [];
    console.log("开始")
    for (let index = 0; index < keys.length; index++) {
        console.log(index);
        const element = keys[index];
        pairs.push(element);
        
    }
    await Promise.allSettled(theard_promise);
    console.log("开始插入");
    await redis.rPush("pair_json_check",pairs);
    console.log("插入完成");
}

batchCallContracts();
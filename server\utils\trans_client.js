// const net = require('net')
const {getHistoryTrans} = require('../avi/trans');
const {child_exec} = require("./child_pm");
// const socket = new net.Socket({})
// socket.connect({
//   host: '127.0.0.1',
//   port: 4000
// })
let socket_trans;
// 引入客户端IO

function init(){
    var socket = require("socket.io-client")('ws://127.0.0.1:3000');
    socket.emit("admin","");
    //console.log("正式开始监听");
    socket.on('change_bai', data => {
        var json_object = data;
        var type = json_object.type;
        var address = json_object.address;
        if(type == 0){
            getHistoryTrans(address);
            var exec_string3 = "pm2 start app_getTransListen.js  --name translisten"+ address +" -- "+address;
            child_exec(exec_string3);
        }else{
            var exec_string1 = "pm2 delete translisten" + address;
            child_exec(exec_string1);
        }
    });
    socket.on('message',data=>{
        //console.log(data);
    })
    socket_trans = socket;
}

function getSocket(){
    //console.log("当前的socket："+socket_trans);
    return socket_trans;
}


module.exports={
    init,getSocket
}

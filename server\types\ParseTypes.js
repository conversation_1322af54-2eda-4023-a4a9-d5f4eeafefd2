/**
 * @typedef {Object} SwapRow
 * @property {number} amount - 交易数量
 * @property {string} amount_usd - 交易额
 * @property {number} from_id - 卖出tokenId
 * @property {string} from_amount - 卖出数量
 * @property {string} from_price_usd - 卖出价格
 * @property {number} log_index - log index
 * @property {number} pair_id - 交易对地址
 * @property {string} to_amount - 买入数量
 * @property {number} to_id - 买入tokenId
 * @property {string} transaction_hash - 交易哈希
 * @property {number} transaction_time - 交易时间
 * @property {string} tx_sender - 交易发送地址
 * @property {string} tx_to - 交易to地址
 * @property {TransactionTypeEnum} type - buy 买 sell 卖 
 * @property {string} wallet_address - 钱包地址
 */

const SwapRow = {};

module.exports = {
    SwapRow,
}
/**
 * 描述: 用户路由模块
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/

const express = require('express');
const router = express.Router();
const service = require('../services/userService');
//注册
router.post('/register', service.register);
//登录
router.post('/login', service.login);
//注销
router.post('/checkout', service.checkoutuser);
//收藏币种（取消收藏）
router.post('/collect', service.collect);
//根据用户得到收藏币种信息
router.post('/queryCollect',service.queryCollect);

module.exports = router;
/**
 *
 * 根据时间戳差值生成日期数组
 * @param startTimestamp
 * @param endTimestamp
 * @returns {[]}
 * @constructor
 */
function TimestampDiff_to_DateArray(startTimestamp, endTimestamp) {
    // 将时间戳转换为 Date 对象
    const startDate = new Date(startTimestamp);
    const endDate = new Date(endTimestamp);

    // 用于存储日期的数组
    const dateArray = [];

    // 逐天递增，直到结束时间
    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
        // 将当前日期添加到数组中（格式化为 YYYY-MM-DD）
        dateArray.push( formatDateToString(currentDate));
        // 日期加一天
        currentDate.setDate(currentDate.getDate() + 1);
    }
    return dateArray;
}
/**
 * 将number转为Date
 * @param time
 * @returns {null|Date}
 * @constructor
 */
function NumberToDate(time) {
    if (time) {
        return new Date(getTimeConvert(time));
    }
    return null;
}

/**
 * 将string转为时间戳
 * @param time
 * @returns {null|Date}
 * @constructor
 */
function StringToTimeStamp(time) {
    if (time) {
        return getTimeStamp(new Date(time).getTime());
    }
    return null;
}

/**
 * 将时间戳转换为long型时间
 * @param time
 * @returns {number}
 */
function getTimeConvert(time) {
    let timeStr = time + "";
    if (timeStr.length == 10) {
        time = time * 1000;
    }
    return time;
}

/**
 * 将long型时间转换为时间戳
 * @param time
 * @returns {number}
 */
function getTimeStamp(time) {
    let timeStr = time + "";
    if (timeStr.length > 10) {
        time = time / 1000;
    }
    return time;
}

/**
 * 时间戳转换成UTC格式化时间
 * @param timestamp
 * @returns {string}
 */
function formatTimestampUTC(timestamp) {
    timestamp = getTimeConvert(timestamp);
    const date = new Date(timestamp); // 将时间戳转换为Date对象
    let utc = date.getTimezoneOffset();
    date.setMinutes(date.getMinutes() + utc);
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
    const day = date.getDate().toString().padStart(2, '0'); // 获取日期并补零
    const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并补零
    const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零
    const seconds = date.getSeconds().toString().padStart(2, '0'); // 获取秒钟并补零
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化字符串
}

/**
 * date转换成UTC的Date
 * @param date
 * @returns {string}
 */
function formatDate_UTCDate(date) {
    let utc = date.getTimezoneOffset();
    date.setMinutes(date.getMinutes() + utc);
    return date;
}

/**
 * date转换成Hours的date
 * @param date
 * @returns {string}
 */
function formatDate_HoursDate(date) {
    date.setHours(date.getHours(),0,0,0);
    return date;
}

/**
 * date转换成Day的date
 * @param date
 * @returns {string}
 */
function formatDate_DayDate(date) {
    date.setHours(0,0,0,0);
    return date;
}

/**
 * date转换成Month的date
 * @param date
 * @returns {string}
 */
function formatDate_MonthDate(date) {
    date = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0, 0);
    return date;
}

/**
 * date转换成Year的date
 * @param date
 * @returns {string}
 */
function formatDate_YearDate(date) {
    date = new Date(date.getFullYear(), 1, 1, 0, 0, 0, 0);
    return date;
}

/**
 * date转换成UTC格式化时间
 * @param date
 * @returns {string}
 */
function formatDateUTC(date) {
    let utc = date.getTimezoneOffset();
    date.setMinutes(date.getMinutes() + utc);
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
    const day = date.getDate().toString().padStart(2, '0'); // 获取日期并补零
    const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并补零
    const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零
    const seconds = date.getSeconds().toString().padStart(2, '0'); // 获取秒钟并补零
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化字符串
}

/**
 * date转换成UTC时间戳
 * @param date
 * @returns {string}
 */
function Date_To_UTCTimeStamp(date) {
    let utc = date.getTimezoneOffset();
    date.setMinutes(date.getMinutes() + utc);
    return date.getTime(); // 时间戳
}

/**
 * UTC转换成中国date格式化时间
 * @param date
 * @returns {string}
 */
function formatUTCDate(date) {
    let utc = date.getTimezoneOffset();
    date.setMinutes(date.getMinutes() - utc);
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
    const day = date.getDate().toString().padStart(2, '0'); // 获取日期并补零
    const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并补零
    const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零
    const seconds = date.getSeconds().toString().padStart(2, '0'); // 获取秒钟并补零
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化字符串
}

/**
 * 时间戳转换成+8小时格式化时间
 * @param timestamp
 * @returns {string}
 */
function formatTimestamp_8(timestamp) {
    timestamp = getTimeConvert(timestamp);
    const date = new Date(timestamp); // 将时间戳转换为Date对象
    let utc = date.getTimezoneOffset();
    date.setMinutes(date.getMinutes() - utc);//utc是负数所以需要减
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
    const day = date.getDate().toString().padStart(2, '0'); // 获取日期并补零
    const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并补零
    const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零
    const seconds = date.getSeconds().toString().padStart(2, '0'); // 获取秒钟并补零
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化字符串
}

/**
 * 时间戳转换成格式化时间
 * @param timestamp
 * @returns {string}
 */
function formatTimestamp(timestamp) {
    timestamp = getTimeConvert(timestamp);
    const date = new Date(timestamp); // 将时间戳转换为Date对象
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
    const day = date.getDate().toString().padStart(2, '0'); // 获取日期并补零
    const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并补零
    const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零
    const seconds = date.getSeconds().toString().padStart(2, '0'); // 获取秒钟并补零
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化字符串
}

/**
 * Date转String格式化时间
 * @param timeStr
 * @returns {string}
 */
function formatDateToString(date) {
    const year = date.getFullYear(); // 获取年份
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
    const day = date.getDate().toString().padStart(2, '0'); // 获取日期并补零
    const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并补零
    const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零
    const seconds = date.getSeconds().toString().padStart(2, '0'); // 获取秒钟并补零
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化字符串
}

/**
 * +n天的格式化
 * @returns {*[]}
 */
function addDayDateFormatte(timeStr, n) {
    let currentDate = new Date(timeStr);
    // 格式化日期为 'YYYY-MM-DD' 格式
    currentDate.setDate(currentDate.getDate() + n);
    let year = currentDate.getFullYear();
    let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    let day = currentDate.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`; // 因为数组是从7天前到昨天的顺序，所以需要反转
}

/**
 * 获取当前时间往前100天的格式化
 * @returns {*[]}
 */
function get7DateFormatte(timeStr) {
    let currentDate = new Date(timeStr);
    let dates = [];
    for (let i = 0; i < 100; i++) {
        // 格式化日期为 'YYYY-MM-DD' 格式
        let year = currentDate.getFullYear();
        let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
        let day = currentDate.getDate().toString().padStart(2, '0');
        dates.push(`${year}-${month}-${day}`);
        currentDate.setDate(currentDate.getDate() - 1);
    }
    return dates; // 因为数组是从7天前到昨天的顺序，所以需要反转
}

module.exports = {
    NumberToDate,
    formatDate_UTCDate,
    formatDate_HoursDate,
    formatDate_DayDate,
    formatDate_YearDate,
    formatDate_MonthDate,
    Date_To_UTCTimeStamp,
    formatDateUTC,
    formatUTCDate,
    getTimeConvert,
    getTimeStamp,
    formatTimestamp,
    formatTimestampUTC,
    formatDateToString,
    StringToTimeStamp,
    formatTimestamp_8,
    get7DateFormatte,
    addDayDateFormatte,
    TimestampDiff_to_DateArray
};
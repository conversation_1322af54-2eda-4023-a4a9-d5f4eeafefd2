const {redis} = require("./redisUtils");
const https = require("https");
const FirstAnalyzeEnum = require("../utils/enums/AnalyzeDorisKeyEnum");
const {getHeaders} = require("../services/webmagic/config/httpHead");
const {img_url_upload} = require("../services/webmagic/task/imgUpload");
const {delay, formatAddress} = require("../utils/functions");
const BigNumber = require("bignumber.js");
const {HQL_sonar} = require("../database/PqueryQL/PostgresSQL");
//特殊地址（持有量较大）
const special_address = new Set(["55d398326f99059ff775485246999027b3197955",
    "e9e7cea3dedca5984780bafc599bd69add087d56"]);
const ave_url = "api.eskegs.com";

async function getTokenCache(address, cache) {
    let lock = true;
    let result;
    try {
        if (cache !== false) {
            cache = true;
        }
        if (cache) {
            let objStr = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", address);
            if (objStr) {
                result = JSON.parse(objStr);
                if (result.pair_address) {
                    return result;
                }
            }
        }
        let url = '/v1api/v3/tokens/' + address + "-bsc"
        const options = {
            hostname: ave_url, path: url, agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        let response = {};
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', async function () {
                try {
                    let thread = [];
                    let jsonData = JSON.parse(html);
                    if (!jsonData['data']) {
                        console.log("getTokenBase:encode_data为空");
                        lock = false;
                        return;
                    }
                    let data ;
                    if(typeof jsonData['data'] === 'string'){
                        data = JSON.parse(jsonData['data']);
                    }else{
                        data = jsonData['data'];
                    }

                    let imgPath = "/image/" + data["token"].token + ".png";
                    let mysqlObj = {
                        chain_id: 56,
                        address: formatAddress(data["token"].token),
                        logo_url: imgPath,
                        name: data["token"].name,
                        symbol: data["token"].symbol,
                        total_supply: data["token"].total,
                        decimals: data["token"].decimal,
                        holders: data["token"].holders,
                        is_open_source: 1,
                        current_price_usd: data["token"].current_price_usd,
                        price_change: data["token"].price_change,
                        risk_score: data["token"].risk_score,
                        status: 1,
                        pair_address: "",
                        publish_time: data["token"].publish_at
                    };
                    let pairs = [];
                    if (data["pairs"] && data["pairs"].length > 0) {
                        mysqlObj.pair_address = data["pairs"][0].pair;
                            for (let n = 0; n < data["pairs"].length; n++) {
                                let pairStr = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata+"_v2", mysqlObj.pair_address);
                                let pairRedis = {};
                                if (pairStr) {
                                    pairRedis = JSON.parse(pairStr);
                                } else {
                                    pairRedis.decimals = 0;
                                    pairRedis.total_supply = 0;
                                    pairRedis.reserve0 = 0;
                                    pairRedis.reserve1 = 0;
                                }
                                pairs.push({
                                    address: formatAddress(data["pairs"][n].pair),
                                    create_time: '',
                                    symbol: "Cake-LP",
                                    token0_addr: formatAddress(data["pairs"][n].token0_address),
                                    token1_addr: formatAddress(data["pairs"][n].token1_address),
                                    token_addr: formatAddress(data["pairs"][n].target_token),
                                    token_index: data["pairs"][n].target_token == data["pairs"][n].token0_address ? 0 : 1,
                                    decimals: pairRedis.decimals,
                                    total_supply: pairRedis.total_supply,
                                    reserve0: pairRedis.reserve0,
                                    reserve1: pairRedis.reserve1
                                });
                            }
                    }
                    thread.push(new Promise((r, j) => {
                        try {
                            let img_url = data['token']['logo_url']
                            let imgFilePath = "." + imgPath;
                            img_url_upload(img_url, imgFilePath);
                            r();
                        } catch (e) {
                            j();
                        }
                    }));
                    Promise.all(thread).then(obj => {
                        //创建代币
                        redis.hSet(FirstAnalyzeEnum.token_json_hdata+"_v2", formatAddress(address), JSON.stringify(mysqlObj));
                        if (pairs.length > 0) {
                            //创建交易对
                            for (let n = 0; n < pairs.length; n++) {
                                redis.hSet(FirstAnalyzeEnum.pair_json_hdata+"_v2", formatAddress(pairs[n].address), JSON.stringify(pairs[n]));
                            }
                        }
                        result = mysqlObj;
                        lock = false;
                    }, e => {
                        console.error("ave.ai->getToken:" + e);
                        lock = false;
                    });
                } catch (e) {
                    console.error("ave.ai->getToken:" + e);
                    lock = false;
                }
            });
            res.on('error', function (e) {
                console.error("ave.ai->getToken:" + e);
                lock = false;
            });
        });
        req.on('error', function (e) {
            console.error("ave.ai->getToken:" + e);
            lock = false;
        });
        req.on('timeout', () => {
            console.error('请求超时:getToken');
            lock = false;
        });
        req.setTimeout(60000);
        req.end();
    } catch (e) {
        console.error("ave.ai->getToken:" + e);
        lock = false;
    }
    while (lock) {
        await delay(1000);//等待执行完成
    }
    return result;
}
async function getTokenToPair(token) {
    let token_address = token.address.replace("0x", "")
    if (special_address.has(token_address)) {//大币不查询
        return {pairAddress: undefined};
    }
    let arg = `WITH
                    pairs AS (  -- 获取当前合约持有的所有地址(最后时刻数据)
                         select address as pairAddress,token0,token1,datetime from MV_pairs_token0 FINAL where token0 = '${token_address}'
                         union all
                         select address as pairAddress,token0,token1,datetime from MV_pairs_token1 FINAL where token1 = '${token_address}' 
                    ),
                    pairReserves as (
                         select address as pairAddress,reserve0,reserve1 from MV_LAST_syncs FINAL
                         where address in(select pairAddress from pairs)
                    ),
                    pairDecimals as (
                         select address as tokenAddress,decimals from MV_LAST_contracts FINAL
                         where address in(select token0 from pairs) or address in(select token1 from pairs) or address in(select pairAddress from pairs)
                    )
                    SELECT toJSONString(map(
                        'type','pairs',
                        'pairAddress',toString(pairAddress),
                        'token0',toString(token0),
                        'token1',toString(token1),
                        'time',formatDateTime(datetime,'%Y-%m-%d %H:%i:%s')
                    )) AS json FROM pairs
                    union all
                    SELECT toJSONString(map(
                        'type','pairResreves',
                        'pairAddress',toString(pairAddress),
                        'reserve0',toString(reserve0),
                        'reserve1',toString(reserve1)
                    )) AS json FROM pairReserves
                    union all
                    SELECT toJSONString(map(
                        'type','pairDecimals',
                        'tokenAddress',toString(tokenAddress),
                        'decimals',toString(decimals)
                    )) AS json FROM pairDecimals`;
    let data = await HQL_sonar(arg);
    let listPairs = [];
    let mapReserves = {};
    let mapDecimals = {};
    if (data && data.length > 100) {//如果大于100个交易对
        return {pairAddress: undefined};
    }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "pairs":
                    if (jsonColumn.token0 == token_address) {
                        jsonColumn.token_index = 0;
                        jsonColumn.target_token = jsonColumn.token0;
                    } else {
                        jsonColumn.token_index = 1;
                        jsonColumn.target_token = jsonColumn.token1;
                    }
                    listPairs.push(jsonColumn);
                    break;
                case "pairResreves":
                    mapReserves[jsonColumn.pairAddress] = {
                        reserve0: jsonColumn.reserve0,
                        reserve1: jsonColumn.reserve1
                    };
                    break;
                case "pairDecimals":
                    mapDecimals[jsonColumn.tokenAddress] = {
                        decimals: jsonColumn.decimals
                    };
                    break;
            }
        }
        for (let i = 0; i < listPairs.length; i++) {
            let item = listPairs[i];
            let reserves = mapReserves[item.pairAddress];
            let decimals = mapDecimals[item.pairAddress];
            let decimals0 = mapDecimals[item.token0];
            let decimals1 = mapDecimals[item.token1];
            item.decimals = decimals.decimals;
            if (reserves) {
                item.reserve0 = formatStrDecimal(reserves.reserve0, decimals0.decimals);
                item.reserve1 = formatStrDecimal(reserves.reserve1, decimals1.decimals);
            } else {
                item.reserve0 = 0;
                item.reserve1 = 0;
            }
        }
    } else {
        return {pairAddress: undefined};
    }
    let index_max = 0;
    let max = new BigNumber(0);
    for (let index = 0; index < listPairs.length; index++) {
        let element = listPairs[index];
        if (element['token_index'] == 0) {
            let reserve0Big = new BigNumber(element['reserve0']);
            if (reserve0Big.comparedTo(max) > 0) {
                max = reserve0Big;
                index_max = index;
            }
        } else {
            let reserve1Big = new BigNumber(element['reserve1']);
            if (reserve1Big.comparedTo(max) > 0) {
                max = reserve1Big;
                index_max = index;
            }
        }
    }
    let pair_max = listPairs[index_max];//获取最大的储备交易对
    if (pair_max) {
        token.pair_address = '0x' + pair_max.pairAddress;
        await redis.hSet(FirstAnalyzeEnum.token_json_hdata+"_v2", token.address, JSON.stringify(token));
    }
    return pair_max;
}

function formatStrDecimal(balance, decimal) {
    if(!balance||!decimal){
        throw Error("未找到decimal");
    }
    if (typeof balance !== 'string') {
        balance = new BigNumber(balance).toFixed();
    }
    let sp = balance.split(".");
    if (sp.length > 1) {
        balance = sp[0];
    }
    if (balance.length <= decimal) {
        let fix_0 = decimal - balance.length;//需要补零的数量
        if (fix_0 > 0) {
            let fix_decimal = new BigNumber(0.1).pow(fix_0);
            fix_decimal = fix_decimal.toFixed();
            fix_decimal = fix_decimal.replace('1', '0');
            balance = fix_decimal + balance;
        } else {
            balance = "0." + balance;
        }
    } else {
        let d_0 = balance.substring(0, balance.length - decimal);
        let d_1 = balance.substring(balance.length - decimal);
        if (d_1 != "") {
            balance = d_0 + "." + d_1;
        } else {
            balance = d_0;
        }
    }
    return balance;
}

/**
 * 判断是否是大币
 * @param address
 * @returns {boolean}
 */
function hasSpecial(address) {
    address = address.replace("0x", "")
    if (special_address.has(address)) {//大币不查询
        return true;
    }
    return false;
}

// getTokenCache("0x73454a2cf532f884b16d620ce6eb4d993fe82032",false);
module.exports = {
    getTokenCache, formatStrDecimal, getTokenToPair, hasSpecial
}
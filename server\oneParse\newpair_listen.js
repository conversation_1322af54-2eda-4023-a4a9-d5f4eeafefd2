const { ethers } = require('ethers');
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const {queryProvider} = require('../avi/factory_provider');
const provider = queryProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const { parseLogAbi, factoryAbi, allAbi,V3AllAbi } = require("../abi");
const {delay} = require("../utils/functions");
async function batchCallContracts() {
    let flag = true;
    while(flag){
        try{
            const contractAddress = '******************************************';
            flag = false;
            // 使用 ABI 和地址创建合约实例
            const contract = new ethers.Contract(contractAddress, factoryAbi, provider);
            // 使用 callStatic 调用合约函数
            let result = await contract.allPairsLength();//contract.callStatic[functionName](...args);//contract.allPairs(...args);//contract.callStatic[functionName](...args);
            console.log(Number(result));
            redis.set("pair_new_length",Number(result));
            await delay(10000);
            flag = true;
        }catch(e){
            console.log(e);

        }
    }
}

batchCallContracts();
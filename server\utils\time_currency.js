const { JsonRp<PERSON><PERSON><PERSON><PERSON> } = require("ethers");
const constant = require("../utils/constant");
const https = require('https');
const axios = require('axios');
const cheerio = require("cheerio");
const {queryLinProvider,queryGuiProvider} = require('../avi/factory_provider');
const provider = queryGuiProvider();
const {} = require('./functions');

let headers = {
'Accept':'*/*',
'Accept-Encoding':'gzip, deflate, br',
'Accept-Language':'zh-CN,zh;q=0.9',
'Cookie':'_ga=GA1.1.**********.**********; OAID=9a62e1b1d93034098d4188784a93fc01; cf_clearance=a6nIUu2SbdDaxwADtrWHMpztjvHTl0FXTXp9x4yMLeg-**********-*******-I_lAs8KU6rNjrBRaRCxKLikftKUCgP6AbKkT1j5PnFgMFsWgIcZHn2rizfErJBMEsJYPo_XDEF19EXAH5gxdGg; OAGEO=2%7CUS%7CNA%7C%7CRiverside%7C92503%7C33.9249%7C-117.4592%7C500%7CAmerica%2FLos_Angeles%7C803%7CCA%7C%7C%7C%7C%7C%7C%7C%7C%7C%7C%7C%7C%7C%7C%7C; cf_clearance=AyaK_jEpRDqz7UydcCPwFGUZRorUsLqVP3iOs.k7zPQ-**********-*******-n8PP_TzzPfi3z4QqPehdYbOg1BBl8yDO.HpOJe.tgiVZDq.a_uOKdRXMvhGrOQ7efh6w.5ToRrDJtvz3ZJB9lQ; _ga_PQY6J2Q8EP=GS1.1.**********.60.1.**********.0.0.0',
'Origin':'https://bscscan.com',
'Referer':'https://bscscan.com/',
'Sec-Ch-Ua':'"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
'Sec-Ch-Ua-Mobile':'?0',
'Sec-Ch-Ua-Platform':'"Windows"',
'Sec-Fetch-Dest':'empty',
'Sec-Fetch-Mode':'cors',
'Sec-Fetch-Site':'same-site',
'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

async function getCreateBlockTxHash(address) {
  return new Promise((resolve, reject) => {

//     const options = {
//       hostname: 'mct.bscscan.com',
//       path: '/www/d/asyncspces.php?zones=1%7C3&prefix=revive-0-&loc=https%3A%2F%2Fbscscan.com%2Faddress%2F'+ address,
//       agent: new https.Agent({
//           proxy: "http://127.0.0.1:7890"
//       }),
//       headers: headers,
//     timeout: 60000 // 设置超时时间为5秒
//   };

    https.get('https://bscscan.com/address/'+address, function (res) {
      //console.log(res);
      // 分段返回的 自己拼接
      let html = ""; // 有数据产生的时候 拼接
      res.on("data", function (chunk) {
        html += chunk;
      }); // 拼接完成
      //console.log(html);
      res.on("end", function () {
        const $ = cheerio.load(html);
        const creatorTxnHash = $(
          "#ContentPlaceHolder1_trContract span"
        ).next().text();
        console.log(creatorTxnHash);
        console.log("完成");
        resolve(creatorTxnHash);
      });
    });
  });
  
}

async function getCreateBlockByTxHash(txHash) {
  try{
    const trade = await provider.getTransaction(txHash);
    console.log(trade)
    if(!trade){
      return null;
    }
    const block = await provider.getBlock(trade.blockNumber);
    console.log(block)
    if(!block){
      return null;
    }
    let res = {
      created_block:block.number,
      block_time:block.timestamp
    }
    return res;
  }catch(ex){
    console.log(ex)
    return null;
  }
  
}

//getCreateBlockTxHash("0x0E09FaBB73Bd3Ade0a17ECC321fD13a19e81cE82");
getCreateBlockByTxHash("0x81daa67f33e0ed890bc39def4a40b38a84f459738d6b0847b5a2b576db434427");
module.exports={
    getCreateBlockTxHash,
    getCreateBlockByTxHash
}

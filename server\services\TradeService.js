const RedisClient1 = require("../database/redis/index1");
const _ = require('lodash');
const PairModel = require('../model/mysql/PairModel');
const TradeModel = require('../model/mysql/TradeModel');
const { default: BigNumber } = require("bignumber.js");
const KlineModel = require("../model/mysql/KlineModel");
const { getKlineTime } = require("../utils/functions");
const { getMaxSupply } = require("../utils/EtherUtils");
const PeriodEnum = require("../utils/enums/PeriodEnum");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const UpdateSwapJob = require("../jobs/UpdateSwapJob");
const TokenModel = require("../model/mysql/TokenModel");
const RankCacheEnum = require("../utils/enums/RankCacheEnum");
const JobEnum = require("../utils/enums/JobEnum");
const PairService = require("../services/pairService");
const { SwapRow } = require('../types/ParseTypes');
/**
 * @param { Array<object> } parseList 
 */
async function saveSwap(parseRow,blockTime){
    let [trade, created] = await TradeModel().findOrCreate({
        where: {
            transaction_hash: parseRow.transaction_hash,
            log_index: parseRow.log_index,
        },
        defaults: parseRow // 用于创建记录的默认值
    });
    if (!created) {
        await trade.update(parseRow);
    }else{
        //await updatePairTxCount(parseRow);
        await updateKlineVolume(parseRow,blockTime);
    }
}

/**
 * 
 * @param { Array<SwapRow> } list 
 * @param {Number} blockTime 
 */
async function saveBatchSwap(list,blockTime){
    /** @type {Array<SwapRow>} */
    const successList = await TradeModel().bulkCreate(list, { ignoreDuplicates: true })
    .catch((error) => {
        console.error('插入失败:', error);
    });

    let parseMap = {};
    for (const item of successList) {
        if(!parseMap.hasOwnProperty(item.pair_id)){
            parseMap[item.pair_id] = {
                txCount: 0,
                txVolumeUsd: new BigNumber(0),
                txVolume: new BigNumber(0),
            }
        }
        parseMap[item.pair_id].txCount += 1;
        parseMap[item.pair_id].txVolumeUsd = parseMap[item.pair_id].txVolumeUsd.plus(item.amount_usd);
        parseMap[item.pair_id].txVolume = parseMap[item.pair_id].txVolume.plus(item.amount);
    }

    for(const key in parseMap){
        if(!parseMap.hasOwnProperty(key)){
            continue;
        }
        await updateKlineVolume0({
            pair_id: key,
            ...parseMap[key],
        },blockTime);
    }
}

/**
 * 更新k线中的交易量
 * @param { object } row 
 * @param { Number } row.pair_id 
 * @param { Number } row.txCount 
 * @param { BigNumber } row.txVolumeUsd
 * @param { BigNumber } row.txVolume 
 * @param { Number } blockTime 
 */
async function updateKlineVolume0(row,blockTime){
    //保存k线数据的交易量数据 
    let klineModel = KlineModel();
    /**
     * @param { PeriodEnum } period 
     */
    const updateVolume = async function(period){
       let klineTime = getKlineTime(blockTime,period);
       let klineData = {
           pair_id: row.pair_id,
           volume: row.txVolume.toFixed(20),
           trade_amount: row.txVolumeUsd.toFixed(20),
           period: period,
           trade_num: row.txCount,
           kline_time: klineTime,
       };
       let [kline, created] = await klineModel.findOrCreate({
           where: {
               pair_id: row.pair_id,
               kline_time: klineTime,
               period: period
           },
           defaults: klineData // 用于创建记录的默认值
       });
       if (!created) {
           const trade_amount = row.txVolumeUsd.plus(kline.trade_amount).toFixed(20);
           const volume = row.txVolume.plus(kline.volume).toFixed(20);
           const trade_num = klineData.trade_num + kline.trade_num;
           await kline.update({
               trade_amount, 
               volume,
               trade_num,
           });
       }

       if(period == PeriodEnum.DAY){
           await kline.reload();
           await updatePair(kline,blockTime);
       }
    }
    //一分K线
    await updateVolume(PeriodEnum.MINUTE_1);
    //一天k线
    await updateVolume(PeriodEnum.DAY);

    await updatePairTxCount(row.pair_id);
}

/**
 * 更新交易对交易次数
 * @param { Number } pairId  交易对ID
 */
async function updatePairTxCount(pairId){
    /** @type {UpdateSwapJob} */
    const updateSwapJob = new UpdateSwapJob();
    await updateSwapJob.addJobLockHandler({pairId},{});
}

/**
 * 更新k线中的交易量
 * @param { object } row 
 * @param { Number } row.pair_id 
 * @param { Number } row.transaction_time 
 * @param { String } row.transaction_hash
 * @param { Number } row.log_index 
 * @param { String } row.sender 
 * @param { String } row.to 
 * @param { Number } row.from_id  
 * @param { String } row.from_amount 
 * @param { String } row.from_price_usd 
 * @param { Number } row.to_id 
 * @param { String } row.to_amount 
 * @param { String } row.to_price_usd 
 * @param { String } row.amount_usd 
 * @param { TradeTypeEnum } row.type 
 * @param { String } row.wallet_address 
 * @param { Number } blockTime 
 */
async function updateKlineVolume(row,blockTime){
    //保存k线数据的交易量数据 
    const volume = row.type == TradeTypeEnum.BUY ? row.to_amount : row.from_amount;
    let klineModel = KlineModel();

    /**
     * @param { PeriodEnum } period 
     */
    const updateVolume = async function(period){
       let klineTime = getKlineTime(row.transaction_time,period);
       let klineData = {
           pair_id: row.pair_id,
           volume: volume,
           trade_amount: row.amount_usd,
           period: period,
           trade_num: 1,
           kline_time: klineTime,
       };
       let [kline, created] = await klineModel.findOrCreate({
           where: {
               pair_id: row.pair_id,
               kline_time: klineTime,
               period: period
           },
           defaults: klineData // 用于创建记录的默认值
       });
       if (!created) {
           const trade_amount = new BigNumber(klineData.trade_amount).plus(kline.trade_amount).toFixed(40);
           const volume = new BigNumber(klineData.volume).plus(kline.volume).toFixed(40);
           const trade_num = klineData.trade_num + kline.trade_num;
           await kline.update({
               trade_amount, 
               volume,
               trade_num,
           });
       }

       if(period == PeriodEnum.DAY){
           await kline.reload();
           await updatePair(kline,blockTime);
       }
    }
    //一分K线
    await updateVolume(PeriodEnum.MINUTE_1);
    //一天k线
    await updateVolume(PeriodEnum.DAY);
}

async function updatePair(kline,blockTime){
   const todayStartTimeStamp = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);

   //更新pair 和 token 的价格
   if(todayStartTimeStamp < blockTime){
       console.error("更新pair中的24小时数据");
       await PairModel().update({
           tx_volume_u_24h: kline.trade_amount,
           tx_count_24h: kline.trade_num,
           tx_volume_24h: kline.volume,
       },{
           where: {
               id: kline.pair_id,
           },
       });

       let token = await TokenModel().findOne({
           where:{
               pair_id: kline.pair_id,
           },
           attributes: ['id','pair_id','max_supply','decimals','address','total_supply']
       });
       if(token && token.pair_id == kline.pair_id){
            console.log("=======================更新转手率和交易排行榜======================================");
           /** @type { RedisClient1 } */
           const redis = new RedisClient1();
           //换手榜
           //获取最大供应量
           let maxSupply = new BigNumber(token.max_supply);
            if(maxSupply.isZero() || maxSupply.isNaN()){
               maxSupply = await getMaxSupply(token.address,token.decimals,token.total_supply);
               await TokenModel().update({
                    max_supply: maxSupply.toFixed(30),
               },{
                where: {
                    id: token.id,
                }
               });
            }
           
            if(!maxSupply.isZero() && !maxSupply.isNaN()){
                let turnoverRate = new BigNumber(kline.volume).div(maxSupply).multipliedBy(100);
                turnoverRate = Number(turnoverRate.toFixed(18));
                await redis.zAdd(RankCacheEnum.TURNOVER_RANK,turnoverRate,kline.pair_id);
            }
    
           //成交榜
           await redis.zAdd(RankCacheEnum.TRADE_RANK,Number(kline.trade_amount),kline.pair_id);
       }
   }
}


async function updateTxData(pairId){
    let tradeList = await TradeModel().findAll({
        where: { pair_id: pairId },
        attributes: ['id','amount','amount_usd']
    });
    if(tradeList.length === 0) {
        console.log("没有交易记录");
        return;
    }

    const txVolumeUsd = tradeList.reduce((totalUsd, trade) => {
        return totalUsd.plus(trade.amount_usd);
    }, BigNumber(0));

    const txVolume = tradeList.reduce((totalVolume, trade) => {
        return totalVolume.plus(trade.amount);
    }, BigNumber(0));

    const [count] = await PairModel().update({
        tx_count: tradeList.length,
        tx_volume: txVolume.toFixed(30),
        tx_volume_usd: txVolumeUsd.toFixed(30),
    }, {
        where: { id: pairId }
    });
}


module.exports = {
    saveSwap,
    updateTxData,
    saveBatchSwap,
}
const axios = require("axios");
const constant = require("../utils/constant");
const redis = require("../database/redis");
const rpc = constant.RPC;
const rpc_new = constant.RPC_NEW;

async function getBlockByNumber(block_no){
  try{
    let blockNo = block_no.toString(16);
    console.log(blockNo);
    let block_string = "0x"+blockNo;
    let data = {
      jsonrpc:"2.0",
      method:"eth_getBlockByNumber",
      params:[block_string,true],
      id:1
    }
    //console.log("当前block_no",block_no);
    let res = await axios.post(rpc,data,{timeout:60000});
    //console.log("获取区块数据成功");
    let res_data = res['data']['result'];
    return res_data;
  }catch(error){
    console.log("当前block出现了错误",error)
    await redis.rPush("fall_block_last",block_no);
    return null;
  }
    
}

async function getTransactionByHash(transhash){
  try{  
    let data_trans = {
      jsonrpc:"2.0",
      method:"eth_getTransactionByHash",
      params:[transhash['hash']],
      id:1
    }
    //console.log("当前hash",transhash['hash']);
    let res_trans = await axios.post(rpc,data_trans,{timeout:60000});
    //console.log("得到hashre结果",res_trans['data']['result']['hash']);
    //console.log("获取hash成功")
    return res_trans['data']['result'];
  }catch(error){
    console.log("当前hash出现了错误")
    await redis.rPush("fall_transaction_last",transhash['hash']);
    return null;
  }
}

async function getTransactionByHash1(transhash){
  try{  
    let data_trans = {
      jsonrpc:"2.0",
      method:"eth_getTransactionByHash",
      params:[transhash],
      id:1
    }
    //console.log("当前hash",transhash['hash']);
    let res_trans = await axios.post(rpc,data_trans,{timeout:60000});
    //console.log("得到hashre结果",res_trans['data']['result']['hash']);
    //console.log("获取hash成功")
    return res_trans['data']['result'];
  }catch(error){
    console.log("当前hash出现了错误")
    await redis.rPush("fall_transaction_last",transhash['hash']);
    return null;
  }
}

async function getTransactionRepictByHash(transhash){
  try{
    let data_trans = {
      jsonrpc:"2.0",
      method:"eth_getTransactionReceipt",
      params:[transhash['hash']],
      id:1
    }
    //console.log("当前date_hash",transhash['hash']);
    let res_trans = await axios.post(rpc,data_trans,{timeout:60000});
    //console.log("得到hashrdde结果",res_trans['data']['result']['transactionHash']);
    //console.log("获取hashlogs成功")
    return res_trans['data']['result'];
  }catch(error){
    console.log("当前hash出现了错误")
    await redis.rPush("fall_transaction_last",transhash['hash']);
    return null;
  }
  
}


module.exports={
  getBlockByNumber,
  getTransactionByHash,
  getTransactionRepictByHash,
  getTransactionByHash1
}

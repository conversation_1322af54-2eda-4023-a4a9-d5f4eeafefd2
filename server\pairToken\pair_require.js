const { ethers } = require('ethers');
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const { queryProvider } = require('../avi/factory_provider');
const provider = queryProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const { multicall, allAbi } = require("../abi");
const { slAvePairsOne } = require("../utils/doris/avePairsSL");
const { delay, isUsdt, isBusd, formatAddress } = require("../utils/functions");
const { } = require("../utils/")
const key = "pair_address_data";
const key_doris = "pair_doris_data";
async function batchCallContracts() {
    let flag = true;
    const contractAddress = '******************************************';
    // 使用 ABI 和地址创建合约实例
    const contract = new ethers.Contract(contractAddress, multicall, provider);
    let all_address = [];
    let all_function = [];
    let results = [];
    console.log("开始时间：", Date.now())
    while (flag) {
        try {
            //console.log(number);

            let pair_address = await redis.lPop(key);

            if (pair_address) {
                //await redis.rPush(key,pair_address);
                let flag_now = await redis.hGet("pair_json_hdata", formatAddress(pair_address));
                all_address.push(pair_address);

                let contract_pair = new ethers.Contract(pair_address, allAbi, provider);
                all_function.push(contract_pair.interface.encodeFunctionData("getReserves"));
                let result = await contract.multicall(all_address, all_function);
                console.log("结果哟：", result.length);
                let threadPromise = [];
                for (let index = 0; index < result.length; index++) {
                    let promise = new Promise(async (resolve, reject) => {
                        try {
                            let address = formatAddress(all_address[index]);
                            let contract_pair_decode = new ethers.Contract(address, allAbi, provider);
                            let getReserves = contract_pair_decode.interface.decodeFunctionResult("getReserves", result[index]);
                            //查询token0和token1的创建时间，并判断token0与token1是不是稳定币
                            //console.log(token0,token1);
                            let pair = JSONbig.parse(flag_now);
                            pair['reserve0'] = getReserves._reserve0;
                            pair['reserve1'] = getReserves._reserve1;
                            results.push(pair);
                            resolve();

                        } catch (ex) {

                            console.log("出错了", ex);
                            await redis.rPush(key, formatAddress(all_address[index]));
                            resolve();
                        }

                    })

                    threadPromise.push(promise);
                }

                await Promise.allSettled(threadPromise);
                console.log(results.length);
                let threadPromiseRedis = [];
                for (let indexj = 0; indexj < results.length; indexj++) {
                    threadPromiseRedis.push(new Promise(async (resolve, reject) => {
                        const element = results[indexj];
                        //console.log(element.address);
                        await redis.hSet("pair_json_hdata", element.address, JSONbig.stringify(element));
                        //await redis.rPush("pair_json_consumer", JSONbig.stringify(element));

                        //let row = `${element.address ? element.address : ''}|${element.created_time ? element.created_time : ''}|${element.symbol ? element.symbol : ''}|${element.token0_addr ? element.token0_addr : ''}|${element.token1_addr ? element.token1_addr : ''}|${element.swap_id ? element.swap_id : 1}|${element.decimals ? element.decimals : 18}|${element.created_block ? element.created_block : 0}|${element.chain_id ? element.chain_id : 0}|${element.created_hash ? element.created_hash : ''}|${element.reserve0 ? element.reserve0 : ''}|${element.reserve1 ? element.reserve1 : ''}|${element.pair_index ? element.pair_index : 0}|${element.token0_price_usd ? element.token0_price_usd : ''}|${element.token0_price_eth ? element.token0_price_eth : ''}|${element.token1_price_usd ? element.token1_price_usd : ''}|${element.token1_price_eth ? element.token1_price_eth : ''}|${element.reserve_usd ? element.reserve_usd : ''}|${element.path ? element.path : ''}|${element.token_addr ? element.token_addr : ''}|${element.token_index ? element.token_index : 0}|${element.status ? element.status : 1}|${element.holders ? element.holders : 0}|${element.tx_count ? element.tx_count : 0}|${element.tx_volume ? element.tx_volume : ''}|${element.tx_volume_usd ? element.tx_volume_usd : ''}|${element.tx_volume_u_24h ? element.tx_volume_u_24h : ''}|${element.tx_count_24h ? element.tx_count_24h : 0}|${element.tx_volume_24h ? element.tx_volume_24h : ''}|${element.price_change_24h ? element.price_change_24h : ''}|${element.token_confirm ? element.token_confirm : 0}|${element.open_price ? element.open_price : ''}|${element.open_time ? element.open_time : 0}|${element.is_open ? element.is_open : 0}|${element.total_supply ? element.total_supply : ''}|${element.create_time ? element.create_time : ''}|${element.update_time ? element.update_time : ''}|${element.lp_proportion ? element.lp_proportion : 0}|${element.publish_time ? element.publish_time : ''}|${element.open_tag ? element.open_tag : ''}`;
                        //slAvePairsOne(row, element.address);
                        await redis.rPush(key_doris,JSONbig.stringify(element));
                        resolve();
                    }));

                }
                await Promise.allSettled(threadPromiseRedis);
                console.log("存入完成");
                console.log("一批次结束时间：", Date.now())
                number = 0;
                results = [];
                all_address = [];
                all_function = [];
            } else {

            }


        } catch (e) {
            console.log(e);
            flag = true;
            all_address = [];
            all_function = [];
            number = 0;
        }
    }
}

batchCallContracts();
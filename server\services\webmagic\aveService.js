let https = require("https");
const {formatStrDecimal} = require("../../utils/BaseDataUtils");
const { formatTimestamp } = require("../../utils/DateUtils");
const { default: BigNumber } = require("bignumber.js");
BigNumber.config({ DECIMAL_PLACES: 100 });
const { getHeaders } = require("./config/httpHead");
const { redis } = require("../../utils/redisUtils");
const { img_url_upload } = require("./task/imgUpload");
const { Contract, JsonRpcProvider, formatUnits } = require("ethers");
const { RPC_NEW } = require("../../utils/constant");
const { allAbi } = require("../../abi");
const { formatAddress } = require("../../utils/functions");
const aveapi = require("./module.aveapi.js");
const provider = new JsonRpcProvider(RPC_NEW, undefined, { polling: true });
const blackHoldAddressOne = "******************************************"; //黑洞地址
const blackHoldAddressTwo = "******************************************"; //黑洞地址
const klineIntroductionModel = require(
  "../../model/mysql/klineIntroductionModel",
);
const { tokenSecurity } = require("../webmagic/javaService");
const {getTokenCache} = require("../../utils/BaseDataUtils"); //暗礁检查接口
const {HQL_sonar} = require("../../database/PqueryQL/PostgresSQL");
const get_history_list = async (req, resp) => {
  let {
    address,
    page_count,
    start_time,
    token_id,
    transaction,
  } = req.query;
  if (!address) {
    resp.json({
      code: 400,
      msg: "address参数不能为空",
      data: null,
    });
    return;
  }
  page_count = parseInt(page_count);
  if (isNaN(page_count) || page_count < 20 || page_count > 100) {
    page_count = 20;
  }
  let headers = await getHeaders();
  let res = await aveapi.get_history_list(
    headers,
    address,
    "bsc",
    page_count,
    start_time ? start_time : "",
    token_id ? token_id : "",
    transaction ? transaction : "",
  );
  resp.json({
    code: 200,
    data: res,
  });
};

const get_complex_protocol_list = async (req, resp) => {
  let {
    address,
  } = req.query;
  if (!address) {
    resp.json({
      code: 400,
      msg: "address参数不能为空",
      data: null,
    });
    return;
  }
  let headers = await getHeaders();
  let res = await aveapi.get_complex_protocol_list(headers, address, "bsc");
  resp.json({
    code: 200,
    data: res,
  });
};

const get_nft_list = async (req, resp) => {
  let {
    address,
  } = req.query;
  if (!address) {
    resp.json({
      code: 400,
      msg: "address参数不能为空",
      data: null,
    });
    return;
  }
  let headers = await getHeaders();
  let res = await aveapi.get_nft_list(headers, address, "bsc");
  resp.json({
    code: 200,
    data: res,
  });
};

const get_balance = async (req, resp) => {
  let {
    address,
  } = req.query;
  if (!address) {
    resp.json({
      code: 400,
      msg: "address参数不能为空",
      data: null,
    });
    return;
  }
  let headers = await getHeaders();
  let res = await aveapi.get_balance(headers, address, "bsc");
  resp.json({
    code: 200,
    data: res,
  });
};

function decode(encode_data) {
  return JSON.parse(
    global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")),
  );
}
/**
 * 检验response合法性
 * @param jsonData
 * @returns {Promise<boolean>}
 */
function checkData(jsonData) {
  if (jsonData["msg"].indexOf("Authorization") > -1) {
    return true;
  } else if (jsonData["msg"].indexOf("failure") > -1) {
    return true;
  }
  return false;
}
async function lp_holders(token, call) {
  const options = {
    hostname: "febweb002.com",
    path: "/v1api/v2/tokens/" + token + "-bsc/extraDetail",
    agent: new https.Agent({
      //proxy: "http://127.0.0.1:7890",
    }),
    headers: await getHeaders(),
    timeout: 60000, // 设置超时时间为5秒
  };
  // https://api.dryespah.com/v1api/v3/tokens/******************************************-bsc/extraDetail
  let html = "";
  let data = [];
  const req = https.get(options, function (res) {
    res.on("data", function (chunk) {
      html += chunk;
    });

    res.on("end", function () {
      try {
        let jsonData = JSON.parse(html);
        if (checkData(jsonData)) {
          console.log("认证过期！");
          call({
            code: 200,
            msg: "认证过期！",
            data: null,
          });
          return;
        }
        if (jsonData["data_type"] == 2) {
          data = decode(jsonData["encode_data"]);
          let map = {
            pair_holders: data["pair_holders"], //lp持币人
            pair_lock_percent: data["pair_lock_percent"], //锁仓
          };
          call({
            code: 200,
            msg: "成功！",
            data: map,
          });
        } else {
          call({
            code: 200,
            msg: "无数据！",
            data: null,
          });
        }
      } catch (e) {
        call({
          code: 500,
          msg: e,
          data: null,
        });
      }
    });
    res.on("error", function (e) {
      call({
        code: 500,
        msg: e,
        data: null,
      });
    });
  });
  req.on("error", function (e) {
    call({
      code: 500,
      msg: e,
      data: null,
    });
  });
  req.end();
}

async function chips_time(token, call) {
  let url = getFilterUrl("chips_time", token);
  const options = {
    hostname: "api.dryespah.com",
    path: url,
    agent: new https.Agent({
      //proxy: "http://127.0.0.1:7890",
    }),
    headers: await getHeaders(),
    timeout: 60000, // 设置超时时间为5秒
  };

  let html = "";
  let data = [];
  const req = https.get(options, function (res) {
    res.on("data", function (chunk) {
      html += chunk;
    });

    res.on("end", function () {
      try {
        let map = {
          time: [],
          data: {
            top10: [],
            top20: [],
            top50: [],
            top100: [],
          },
          totalAmount: 0,
        };
        let jsonData = JSON.parse(html);
        if (checkData(jsonData)) {
          console.log("chips_time:认证过期！");
          call({
            code: 200,
            msg: "认证过期！",
            data: null,
          });
          return;
        }
        if (jsonData["data_type"] == 2) {
          data = decode(jsonData["encode_data"]);
          for (let i = 0; i < data.length; i++) {
            let obj = data[i];
            map.time.push(obj.date);
            map.data.top10.push(obj.top10_amount);
            map.data.top20.push(obj.top20_amount);
            map.data.top50.push(obj.top50_amount);
            map.data.top100.push(obj.top100_amount);
            map.totalAmount = obj.total_amount;
          }
          call({
            code: 200,
            msg: "成功！",
            data: map,
          });
        } else {
          call({
            code: 200,
            msg: "排名时间未知！",
            data: null,
          });
        }
      } catch (e) {
        call({
          code: 500,
          msg: e,
          data: null,
        });
      }
    });
    res.on("error", function (e) {
      call({
        code: 500,
        msg: e,
        data: null,
      });
    });
  });
  req.on("error", function (e) {
    call({
      code: 500,
      msg: e,
      data: null,
    });
  });
  req.on("timeout", () => {
    console.error("请求超时:chips_time");
    call({
      code: 500,
      msg: null,
      data: null,
    });
  });
  req.setTimeout(60000);
  req.end();
}

/**
 * 持币人排名
 * @param token
 * @param result
 * @constructor
 */
async function chips(token, call) {
  let url = getFilterUrl("chips", token);
  const options = {
    hostname: "api.dryespah.com",
    path: url,
    agent: new https.Agent({
      //proxy: "http://127.0.0.1:7890",
    }),
    headers: await getHeaders(),
    timeout: 60000, // 设置超时时间为5秒
  };

  let html = "";
  const req = https.get(options, function (res) {
    res.on("data", function (chunk) {
      html += chunk;
    });
    res.on("end", function () {
      try {
        let data = {
          time: [],
          data: {
            top10: [],
            top20: [],
            top50: [],
            top100: [],
          },
        };
        let jsonData = JSON.parse(html);
        if (checkData(jsonData)) {
          console.log("chips:认证过期！");
          call({
            code: 200,
            msg: "认证过期！",
            data: null,
          });
          return;
        }
        if (jsonData["data_type"] == 2) {
          let decoded = decode(jsonData["encode_data"]);
          for (let i = 0; i < decoded.length; i++) {
            let obj = decoded[i];
            obj["tag"] = "-";
            if (obj["wallet_tag_v2"]) {
              obj["tag"] = obj["wallet_tag_v2"].replace(/,[0-9]*/, "");
            }
            delete obj["cost_cur"];
            delete obj["cost_diff_3days"];
            delete obj["sell_amount_cur"];
            delete obj["sell_amount_diff_3days"];
            delete obj["sell_volume_cur"];
            delete obj["sell_volume_diff_3days"];
            delete obj["buy_volume_cur"];
            delete obj["buy_volume_diff_3days"];
            delete obj["buy_amount_cur"];
            delete obj["buy_amount_diff_3days"];
            delete obj["buy_tx_count_cur"];
            delete obj["sell_tx_count_cur"];
            delete obj["trade_first_at"];
            delete obj["trade_last_at"];
            delete obj["wallet_tag_v2"];
            delete obj["wallet_tag_extra"];
            delete obj["is_wallet_address_fav"];
          }
          call({
            code: 200,
            msg: "成功！",
            data: decoded,
          });
        } else {
          call({
            code: 200,
            msg: "持币量未知！",
            data: null,
          });
        }
      } catch (e) {
        call({
          code: 500,
          msg: e,
          data: null,
        });
      }
    });
    res.on("error", function (e) {
      call({
        code: 500,
        msg: e,
        data: null,
      });
    });
  });
  req.on("error", function (e) {
    call({
      code: 500,
      msg: e,
      data: null,
    });
  });
  req.on("timeout", () => {
    console.error("请求超时:chips");
    call({
      code: 500,
      msg: null,
      data: null,
    });
  });
  req.setTimeout(60000);
  req.end();
}

/**
 * 获取交易记录
 * @param pair
 * @param call
 */
async function transaction(pair, call) {
  let url = getFilterUrl("transaction", pair);
  let html2 = "";
  let data;
  const options2 = {
    hostname: "api.eskegs.com",
    path: url,
    agent: new https.Agent({
      //proxy: "http://127.0.0.1:7890",
    }),
    headers: await getHeaders(),
    timeout: 60000, // 设置超时时间为5秒
  };
  // console.log(options2)
  const req = https.get(options2, function (res) {
    res.on("data", function (chunk) {
      html2 += chunk;
    });

    res.on("end", function () {
      let jsonData = JSON.parse(html2);
      if (checkData(jsonData)) {
        console.log("认证过期！");
        call({
          code: 200,
          msg: "认证过期！",
          data: null,
        });
        return;
      }
      if (jsonData["data"].length>0) {
        data = jsonData["data"];
        call({
          code: 200,
          msg: "成功！",
          data: data,
        });
      } else {
        call({
          code: 200,
          msg: "交易未知！",
          data: null,
        });
      }
    });
    res.on("error", function (e) {
      call({
        code: 500,
        msg: e,
        data: null,
      });
    });
  });
  req.on("error", function (e) {
    console.log(e)
    call({
      code: 500,
      msg: e,
      data: null,
    });
  });
  req.end();
}

/**
 * 查询流动性
 * @param pair
 * @returns {Promise<unknown>}
 */
async function lq(pair, call) {
  let url = getFilterUrl("lq", pair);
  let html2 = "";
  let data;
  const options2 = {
    hostname: "api.avegac.com",
    path: url,
    agent: new https.Agent({
      //proxy: "http://127.0.0.1:7890",
    }),
    headers: await getHeaders(),
    timeout: 60000, // 设置超时时间为5秒
  };
  const req = https.get(options2, function (res) {
    res.on("data", function (chunk) {
      html2 += chunk;
    });

    res.on("end", function () {
      let jsonData = JSON.parse(html2);
      if (checkData(jsonData)) {
        console.log("认证过期！");
        call({
          code: 200,
          msg: "认证过期！",
          data: null,
        });
        return;
      }
      data = jsonData["encode_data"];
      if (data && data !== "[]") {
        data = decode(data);
        call({
          code: 200,
          msg: "成功！",
          data: data,
        });
      } else {
        call({
          code: 200,
          msg: "流动性未知！",
          data: null,
        });
      }
    });
    res.on("error", function (e) {
      call({
        code: 500,
        msg: e,
        data: null,
      });
    });
  });
  req.on("error", function (e) {
    call({
      code: 500,
      msg: e,
      data: null,
    });
  });
  req.end();
}

/**
 * 查询流动性_趋势图
 * @param pair
 * @returns {Promise<unknown>}
 */
async function lq_chart(pair, call) {
  let url = getFilterUrl("lq_chart", pair);
  let html2 = "";
  let data;
  const options2 = {
    hostname: "api.avegac.com",
    path: url,
    agent: new https.Agent({
      //proxy: "http://127.0.0.1:7890",
    }),
    headers: await getHeaders(),
    timeout: 60000, // 设置超时时间为5秒
  };
  const req = https.get(options2, function (res) {
    res.on("data", function (chunk) {
      html2 += chunk;
    });

    res.on("end", function () {
      let jsonData = JSON.parse(html2);
      if (checkData(jsonData)) {
        console.log("认证过期！");
        call({
          code: 200,
          msg: "认证过期！",
          data: null,
        });
        return;
      }
      data = jsonData["data"];
      if (data && data !== "[]") {
        call({
          code: 200,
          msg: "成功！",
          data: data,
        });
      } else {
        call({
          code: 200,
          msg: "流动性未知！",
          data: null,
        });
      }
    });
    res.on("error", function (e) {
      call({
        code: 500,
        msg: e,
        data: null,
      });
    });
  });
  req.on("error", function (e) {
    call({
      code: 500,
      msg: e,
      data: null,
    });
  });
  req.end();
}

function getFilterUrlKey(value) {
  let key = "bsc";
  if ("******************************************" == value) {
    key = "depass";
  } else if ("******************************************" == value) {
    key = "eth";
  }
  return key;
}

/**
 * 过滤token地址url
 * @param type
 * @param value
 * @returns {string}
 */
function getFilterUrl(type, value) {
  let result = "";
  let v = ""; //特殊处理
  switch (type) {
    case "getPairBase":
      result = "/v1api/v3/tokens/" + value + "-" + getFilterUrlKey(value);
      break;
    case "lq":
      v = value.split("|");
      result = "/v1api/v2/pairs/" + v[1] + "-" + getFilterUrlKey(v[0]) + "/liq";
      break;
    case "lq_chart":
      v = value.split("|");
      result = "/v1api/v3/pairs/" + v[1] + "-" + getFilterUrlKey(v[0]) +
        "/liq?interval=7";
      break;
    case "transaction":
      v = value.split("|");
      result = "/v1api/v3/pairs/" + v[1] + "-" + getFilterUrlKey(v[0]) +
        "/txs?address=";
      break;
    case "chips_time":
      result = "/v1api/v2/stats/top100range?token_id=" + value + "-" +
        getFilterUrlKey(value);
      break;
    case "chips":
      result = "/v1api/v3/stats/top100balance?token_id=" + value + "-" +
        getFilterUrlKey(value) + "&size=100&address=";
      break;
  }
  return result;
}

/**
 * 获取K线基本信息
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getPairBase(request, result) {
  try {
    // let cacheOpen = request.cacheOpen;
    // if (cacheOpen !== false) {
    //     cacheOpen = false;
    // }
    if (!request.query.address) {
      result.json({
        code: 400,
        msg: "address参数不能为空",
        data: null,
      });
      return;
    }
    request.query.address=request.query.address.toLowerCase()
    // if (cacheOpen) {
    //
    //     /**
    //      * 从doris缓存查询
    //      * @type {unknown}
    //      */
    //     // const pairBase = await dbGetPairBaseQuery([{
    //     //     type: "string",
    //     //     column: "address",
    //     //     value: request.query.address
    //     // }]);
    //     // if (pairBase.length > 0) {
    //     //     result.json({
    //     //         code: 200, msg: '请求成功！', data: JSON.parse(pairBase[0]["content"])
    //     //     });
    //     //     return;
    //     // }
    //     // let objStr = await redis.hGet("webmagic_api:getPairBase", request.query.address);
    //     // if (objStr) {
    //     //     result.json({
    //     //         code: 200, msg: '请求成功！', data: JSON.parse(objStr)
    //     //     });
    //     //     return;
    //     // }
    // }
    let url = getFilterUrl("getPairBase", request.query.address);
    const options = {
      hostname: "api.eskegs.com",
      path: url,
      agent: new https.Agent({
        //proxy: "http://127.0.0.1:7890",
      }),
      headers: await getHeaders(),
      timeout: 60000, // 设置超时时间为5秒
    };
    let html = "";
    let response = {};
    const req = https.get(options, function (res) {
      res.on("data", function (chunk) {
        html += chunk;
      });
      res.on("end", async function () {
        try {
          let thread = [];
          let jsonData = JSON.parse(html);
          if (!jsonData["data"]) {
            console.log("getPairBase:encode_data为空");
            result.json({
              code: 200,
              msg: "数据为空！",
              data: null,
            });
            return;
          }
          let data = jsonData["data"];
          response["topTitle"] = []; //头部标题
          response["pairs"] = []; //池子表格
          response["volumes"] = {}; //交易表格
          response["liquidities"] = {}; //流动性折线图
          response["liquiditieList"] = {}; //流动性表格
          response["chips"] = []; //持币人折线图
          response["chipsList"] = []; //持币人表格
          response["introduction"] = {
            symbol: "-",
            totalSupply: "-",
            logo: "",
            content: "",
          }; //简介
          let chipsSort; //持币人排序集合
          let chipsListSort = []; //持币人表格排序集合
          let token = data["token"];
          let liquidity; //流通量
          let total_supply;//最大供应量
          let openCate;//开盘回报率
          /**
           * 计算流通市值
           */
          if (token) {
            //查询简介
            response["introduction"]["symbol"] = token.symbol;
            response["introduction"]["totalSupply"] = token.total;
            let klineIntroduction = await klineIntroductionModel().findOne({
              where: { token_address: token.token },
            });
            if (klineIntroduction) {
              response["introduction"]["content"] = klineIntroduction.content;
            }
            total_supply = new BigNumber(token.total);
            try {
              /**
               * 查询持有该合约的黑洞地址
               * @type {string}
               */
              // let params="";
              // for (let i = 0; i < data["pairs"].length; i++) {
              //   params = params + "'"+ data["pairs"][i]['pair'].replace("0x","") + "',";
              // }
              // params = params.replace(/[,]+$/, '');
              let params=token.token.toLowerCase().replace("0x","");
              let arg_black = `WITH to_black_holes_AddressAll AS ( -- 查询所有黑洞持有总和
                                  select sum(sum_value) as value
                                               from MV_SUM_LAST_black_transfers FINAL
                                               where address in ('${params}')
                                  )
                                  SELECT toJSONString(map(-- 合约持币总量（当前区块数据）
                                          'type', 'all_black_sum',
                                          'value', toString(value)
                                   )) AS json FROM to_black_holes_AddressAll`;
              let data_black = await HQL_sonar(arg_black);
              let black_sum = 0;
              if (data_black.length > 0) {
                let jsonColumn = JSON.parse(data_black[0].json);
                if (jsonColumn.value) {
                  black_sum = formatStrDecimal(jsonColumn.value, token.decimal);//记录所有黑洞的持币
                }
              }
              liquidity = new BigNumber(token.total).minus(black_sum);
            } catch (e) {
              //异常放过
            }
            //开盘回报率
            openCate = new BigNumber(token.current_price_usd).dividedBy(token.open_price).multipliedBy(100).toFixed(2);
          }
          if (data["pairs"] && data["pairs"].length>0) {
            for (let i = 0; i < data["pairs"].length; i++) {
              let item = data["pairs"][i];
              /**
               * 计算当前池子总流动性(取辅币来算更准确，因为数量更多)
               */
              let pair1 = {};
              let token0_price_usd; //当前币价格
              let token1_price_usd; //辅助币价格
              if (item.target_token.includes(item.token0_address)) {
                pair1["symbol0"] = item.token0_symbol; //币1名称
                pair1["symbol1"] = item.token1_symbol; //币2名称
                pair1["reserve0"] = item.reserve0; //币1数量
                pair1["reserve1"] = item.reserve1; //币2数量
                token0_price_usd = item.token0_price_usd; //主币U价格
                token1_price_usd = item.token1_price_usd; //主币U价格
              } else {
                pair1["symbol0"] = item.token1_symbol;
                pair1["symbol1"] = item.token0_symbol;
                pair1["reserve0"] = item.reserve1;
                pair1["reserve1"] = item.reserve0;
                token0_price_usd = item.token1_price_usd; //主币U价格
                token1_price_usd = item.token0_price_usd; //主币U价格
              }
              pair1["liquiditie"] = "-";
              pair1["liquiditieNumber"] = new BigNumber(0);
              if (token1_price_usd) {
                //计算辅币会更准确
                pair1["current_price_usd"] = token0_price_usd;
                pair1["FDV"] = total_supply.multipliedBy(token0_price_usd).toFixed(2);//FDV=当前代币价格×最大供应量（Max Supply）
                pair1["liquiditieNumber"] = new BigNumber(pair1["reserve1"])
                  .multipliedBy(new BigNumber(token1_price_usd)).multipliedBy(
                    2,
                  );
                pair1["liquiditie"] = pair1["liquiditieNumber"].toFixed(3); //流动性总额
                //流通市值=当前价格x流通量（发行量-黑洞地址）
                pair1["money"] = liquidity.multipliedBy(token0_price_usd)
                  .toFixed(2);
                /*
                流通率（流通率/FDV）：
                avi的逻辑是:(流通率/FDV)×100%
                官方逻辑是:(24小时成交量/流通供应量)×100%
                */
                pair1["liqRate"] = liquidity.dividedBy(total_supply).multipliedBy(100).toFixed(1,BigNumber.ROUND_UP);
                pair1["openCate"] = openCate;
              }
              pair1["pairsData"] = item;
              response["pairs"].push(pair1);
            }
            response["pairs"].sort((a, b) =>
              b["liquiditieNumber"].comparedTo(a["liquiditieNumber"])
            );
            let pair_map = {};
            for (let i = 0; i < response["pairs"].length; i++) {
              let pair1 = response["pairs"][i];
              let item = pair1["pairsData"];
              response["liquidities"][item.pair] = { x: [], y: [] }; //初始化每个池子的流动性
              response["liquiditieList"][item.pair] = []; //初始化流动性表格
              let pair0 = {};
              // top_title_k(item.pair, pair0);
              /**
               *  topTitle计算
               */
              pair0["poolSize"] = pair1.liquiditie; //池子大小
              pair0["liquidity"] = liquidity; //流通量
              pair0["liqRate"] = pair1.liqRate;//流通率
              pair0["openCate"] = pair1.openCate;//开盘回报率
              pair0["FDV"] = pair1.FDV; //最大流通量
              pair0["txVolume24h"] = item.volume_u; //24H额
              pair0["money"] = pair1.money; //流通市值
              pair0["txCount24h"] = item.tx_count; //24H交易数
              pair0["high24h"] = item.high_u; //24H高
              pair0["turnover24h"] = "-"; //24H换手(24H换手=24H量/流通量（发行量-黑洞地址）)
              pair0["low24h"] = item.low_u; //24H低
              pair0["roi"] = "-"; //投资回报率
              pair0["txAmount24h"] = "-"; //24H量
              pair0["chain"] = item.chain; //所属公链
              pair0["address"] = item.pair;
              pair0["currentPriceUsd"] = pair1.current_price_usd; //当前价格
              pair0["priceChange"] = item.price_change; //价格变动
              pair0["tokenAddress"] = data["token"].token; //token地址
              pair0["holders"] = data["token"].holders; //持有人
              pair0["totalSupply"] = data["token"].total; //总量
              pair0["isMint"] = 0; //是否可增发
              pair0["openPrice"] = data["token"].open_price; //开盘价
              pair0["lp_holder"] = item.lp_holders; //lp持币人
              pair0["lockStore"] = item.lp_locked_percent; //锁仓
              response["topTitle"].push(pair0);
              /**
               * @type {string|*}
               */
              pair1["address"] = item.pair;
              pair1["showName"] = item.show_name; //池子类型名称
              let token_price_usd = pair1.current_price_usd;
              if (token_price_usd) {
                if (pair0["txVolume24h"]) {
                  /**
                   * 计算当前池子24H总量
                   */
                  // let qute = new BigNumber(pair1['reserve1']).div(pair1['reserve0']);
                  // let currentPriceUsd = qute.multipliedBy(token_price_usd);//当前价格
                  //24H量
                  pair0["txAmount24h"] = new BigNumber(pair0["txVolume24h"])
                    .div(token_price_usd).toFixed(10);
                  //24H额
                  pair0["txVolume24h"] = new BigNumber(pair0["txVolume24h"])
                    .toFixed(10);
                  //24H换手
                  pair0["turnover24h"] = new BigNumber(pair0["txAmount24h"])
                    .div(liquidity).multipliedBy(100).toFixed(3);
                }
              }
              /**
               * 流动性获取
               */
              thread.push(
                new Promise((r2, j2) => {
                  try {
                    let liquiditiesSort = []; //流动性排序集合
                    lq(token.token + "|" + item.pair, function (call) {
                      if (call.code == 200) {
                        if (call.data) {
                          call.data.forEach((liquiditie) => { //池子流动性集合（day）
                            let dateformat = formatTimestamp(liquiditie.time);
                            let dataObj2 = {
                              time: dateformat,
                              type: liquiditie.type,
                              t: liquiditie.time,
                              token0Symbol: liquiditie.token0_symbol,
                              token1Symbol: liquiditie.token1_symbol,
                              amount0: liquiditie.amount0,
                              amount1: liquiditie.amount1,
                              amountUsd: liquiditie.amount_usd,
                              walletAddress: liquiditie.wallet_address,
                            };
                            dataObj2.amountUsdPair = new BigNumber(
                              dataObj2.amountUsd,
                            ).multipliedBy(2).toFixed(); //计算当前时刻池子价格
                            liquiditiesSort.push(dataObj2);
                          });
                          //流动性
                          if (liquiditiesSort.length > 0) {
                            liquiditiesSort.sort((a, b) => b.t - a.t);
                            liquiditiesSort.forEach((lq) => {
                              response["liquiditieList"][item.pair].push(lq);
                            });
                            for (let key in pair_map) {
                              response["liquidities"][item.pair].x.push(key);
                              response["liquidities"][item.pair].y.push(
                                pair_map[key].toFixed(),
                              );
                            }
                          }
                        }
                        r2();
                      } else {
                        console.info("流动性获取失败", call.msg);
                        j2(call.msg);
                      }
                    });
                  } catch (e) {
                    j2(e);
                  }
                }),
              );
              /**
               * 流动性获取—趋势图
               */
              thread.push(
                new Promise((r2, j2) => {
                  try {
                    lq_chart(token.token + "|" + item.pair, function (call) {
                      if (call.code == 200) {
                        if (call.data) {
                          call.data.forEach((liquiditie) => { //池子流动性集合（day）
                            let dateformat = formatTimestamp(liquiditie.time);
                            let dateValue = new BigNumber(
                              liquiditie["removeliquidity_total"],
                            ).plus(liquiditie["addliquidity_total"]);
                            response["liquidities"][item.pair].x.push(
                              dateformat.substring(0, 10),
                            );
                            response["liquidities"][item.pair].y.push(
                              dateValue.toFixed(),
                            );
                          });
                        }
                        r2();
                      } else {
                        console.info("流动性-趋势获取失败", call.msg);
                        j2(call.msg);
                      }
                    });
                  } catch (e) {
                    j2(e);
                  }
                }),
              );
              /**
               * 交易记录获取
               */
              thread.push(
                new Promise((r2, j2) => {
                  try {
                    let transSort = []; //交易排序集合
                    transaction(token.token + "|" + item.pair, function (call) {
                      if (call.code == 200) {
                        if (call.data) {
                          call.data.forEach((trans) => { //交易集合（day）
                            let dateformat = formatTimestamp(trans.time);
                            let dataObj2;
                            if (
                              data["token"].token.includes(trans.from_address)
                            ) {
                              dataObj2 = {
                                type: "from",
                                t: trans.time,
                                transactionTime: dateformat,
                                fromPriceUsd: trans.from_price_usd,
                                amount: trans.from_amount,
                                amountUsd: trans.amount_usd,
                                walletAddress: trans.wallet_address,
                                newTags:trans.newTags
                              };
                            } else {
                              dataObj2 = {
                                type: "to",
                                t: trans.time,
                                transactionTime: dateformat,
                                fromPriceUsd: trans.to_price_usd,
                                amount: trans.to_amount,
                                amountUsd: trans.amount_usd,
                                walletAddress: trans.wallet_address,
                                newTags:trans.newTags
                              };
                            }
                            transSort.push(dataObj2);
                          });
                          //交易
                          if (transSort.length > 0) {
                            transSort.sort((a, b) => b.t - a.t);
                            response["volumes"][item.pair] = transSort;
                          }
                        }
                        r2();
                      } else {
                        console.info("交易获取失败", call.msg);
                        j2(call.msg);
                      }
                    });
                  } catch (e) {
                    j2(e);
                  }
                }),
              );
              delete pair1["pairsData"];
            }

            /**
             * lp持币和锁仓
             */
            // thread.push(new Promise((r2, j2) => {
            //     lp_holders(data['token'].token, function (call) {
            //         if (call.code == 200) {
            //             if (call.data) {
            //                 for (let i = 0; i < response['topTitle'].length; i++) {
            //                     let lp = response['topTitle'][i];
            //                     lp['lp_holder'] = call.data.pair_holders;//lp持币人
            //                     lp['lockStore'] = new BigNumber(call.data.pair_lock_percent).multipliedBy(100).toFixed(3);//锁仓
            //                 }
            //             }
            //             r2();
            //         } else {
            //             console.info("获取lp持币人失败", call.msg);
            //             j2(call.msg);
            //         }
            //     });
            // }));
            /**
             * 持币人获取
             */
            thread.push(
              new Promise((r2, j2) => {
                try {
                  chips_time(data["token"].token, function (call) {
                    if (call.code == 200) {
                      if (call.data) {
                        chipsSort = call.data; //持币人集合（day）
                      }
                      r2();
                    } else {
                      console.info("获取持币人折线图失败", call.msg);
                      j2(call.msg);
                    }
                  });
                } catch (e) {
                  j2(e);
                }
              }),
            );
            thread.push(
              new Promise((r2, j2) => {
                try {
                  chips(data["token"].token, function (call) {
                    if (call.code == 200) {
                      if (call.data) {
                        chipsListSort = call.data; //持币人集合（day）
                      }
                      r2();
                    } else {
                      console.info("获取持币人表格失败", call.msg);
                      j2(call.msg);
                    }
                  });
                } catch (e) {
                  j2(e);
                }
              }),
            );
          }
          let imgPath = "/image/" + data["token"].token + ".png";
          response["introduction"]["logo"] = imgPath; //填充简介
          let mysqlObj = {
            chain_id: 56,
            address: formatAddress(data["token"].token),
            logo_url: imgPath,
            name: data["token"].name,
            symbol: data["token"].symbol,
            total_supply: data["token"].total,
            decimals: data["token"].decimal,
            holders: data["token"].holders,
            is_open_source: 1,
            current_price_usd: data["token"].current_price_usd,
            price_change: data["token"].price_change,
            risk_score: data["token"].risk_score,
            status: 1,
            publish_time: data["token"].publish_at,
          };
          /**
           * 查询可增发
           */
          thread.push(
            new Promise((r, j) => {
              try {
                tokenSecurity({ body: { token: token.token } }, {
                  json: function (result) {
                    response["topTitle"].forEach((item) => {
                      item["isMint"] = result.data.base.has_mint_method;
                    });
                    r();
                  },
                });
              } catch (e) {
                j(e);
              }
            }),
          );
          /**
           * 下载图片
           */
          thread.push(
            new Promise((r, j) => {
              try {
                let logo_url = data["token"]["logo_url"];
                let imgFilePath = "." + imgPath;
                img_url_upload(logo_url, imgFilePath);
                response["imgUrl"] = imgPath;
                r();
              } catch (e) {
                j(e);
              }
            }),
          );
          await Promise.all(thread).then(() => {
            //持币人
            let totalAmount = 0;
            if (chipsSort) {
              response["chips"] = chipsSort;
              totalAmount = chipsSort.totalAmount;
            }
            //持币人表格
            if (chipsListSort.length > 0) {
              chipsListSort.forEach((item) => {
                item.proportion = (totalAmount == 0)
                  ? "-"
                  : Number(item.amount_cur / totalAmount * 100).toFixed(2) +
                    "%";
              });
              response["chipsList"] = chipsListSort;
            }
            // let row = `${request.query.address}|${JSON.stringify(response)}`;
            // slGetPairBase([row]);
            // redis.hSet("webmagic_api:getPairBase", request.query.address, JSON.stringify(response));
            result.json({
              code: 200,
              msg: "请求成功！",
              data: response,
            });
          }, (e) => {
            result.json({
              code: 500,
              msg: e,
              data: null,
            });
          });
        } catch (e) {
          result.json({
            code: 500,
            msg: e,
            data: null,
          });
        }
      });
      res.on("error", function (e) {
        result.json({
          code: 500,
          msg: e,
          data: null,
        });
      });
    });
    req.on("error", function (e) {
      result.json({
        code: 500,
        msg: e,
        data: null,
      });
    });
    req.end();
  } catch (e) {
    result.json({
      code: 500,
      msg: e,
      data: null,
    });
  }
}

async function getHolder(request, result, cacheOpen) {
  try {
    if (!request.query.address) {
      result.json({
        code: 400,
        msg: "address参数不能为空",
        data: null,
      });
      return;
    }
    // if (cacheOpen !== false) {
    cacheOpen = false;
    // }
    if (cacheOpen) {
      let objStr = await redis.hGet(
        "webmagic_api:getHolder",
        request.query.address,
      );
      if (objStr) {
        result.json({
          code: 200,
          msg: "请求成功！",
          data: JSON.parse(objStr),
        });
        return;
      }
    }
    let response = {};
    let thread = [];
    let chipsSort = [];
    let chipsListSort = [];
    thread.push(
      new Promise((r2, j2) => {
        chips_time(request.query.address, function (call) {
          if (call.code == 200) {
            if (call.data) {
              chipsSort = call.data; //持币人集合（day）
            }
            r2();
          } else {
            console.info("获取持币人折线图失败", call.msg);
            j2(call.msg);
          }
        });
      }),
    );
    //持币人表格排序集合
    thread.push(
      new Promise((r2, j2) => {
        chips(request.query.address, function (call) {
          if (call.code == 200) {
            if (call.data) {
              chipsListSort = call.data; //持币人集合（day）
              r2();
            }
          } else {
            console.info("获取持币人表格失败", call.msg);
            j2(call.msg);
          }
        });
      }),
    );

    Promise.all(thread).then(() => {
      //持币人
      let totalAmount = 0;
      if (chipsSort) {
        response["chips"] = chipsSort;
        totalAmount = chipsSort.totalAmount;
      }
      //持币人表格
      if (chipsListSort.length > 0) {
        chipsListSort.forEach((item) => {
          item.proportion = (totalAmount == 0)
            ? "-"
            : Number(item.amount_cur / totalAmount * 100).toFixed(2) + "%";
        });
        response["chipsList"] = chipsListSort;
      }
      result.json({
        code: 200,
        msg: "请求成功！",
        data: response,
      });
      redis.hSet(
        "webmagic_api:getHolder",
        formatAddress(request.query.address),
        JSON.stringify(response),
      );
    }, (e) => {
      result.json({
        code: 500,
        msg: e,
        data: null,
      });
    });
  } catch (e) {
    result.json({
      code: 500,
      msg: e,
      data: null,
    });
  }
}

module.exports = {
  getPairBase,
  getHolder,
  get_balance,
  get_nft_list,
  get_complex_protocol_list,
  get_history_list,
};


const redis = require("../database/redis");

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * 失败的记录
 * @param blockRList
 * @returns {Promise<void>}
 */
async function errorToRedis(key, blockNumbers) {
    let count = 5;//尝试次数
    let tag = true;
    while (tag) {
        try {
            await redis.rPush(key, blockNumbers);
            tag = false;
        } catch (error) {
            count--;
            if (count > 0) {
                console.log("标号" + count + "尝试等待5s再次记录失败数据");
                await sleep(5000);
            } else {
                tag = false;
            }
        }
    }
}

module.exports={
    errorToRedis,redis
}

const { ethers } = require('ethers');
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const {queryProvider} = require('../avi/factory_provider');
const provider = queryProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const { parseLogAbi, factoryAbi, allAbi,V3AllAbi } = require("../abi");
const {delay} = require("../utils/functions");
async function batchCallContracts() {
    let flag = true;
    while(flag){
        try{
            const contractAddress = '******************************************';
            let new_length = await redis.get("pair_new_length");
            let new_now = await redis.get("pair_new_now");
            console.log(new_length,new_now);
            const contract = new ethers.Contract(contractAddress, factoryAbi, provider);
            if(Number(new_now) < Number(new_length)){
                for (let index = Number(new_now); index < Number(new_length); index++) {
                    
                    let result = await contract.allPairs(index);
                    await redis.hSet("pair_json_chain",result,'');
                    await redis.rPush("pair_json_all",result);
                }
            }
            console.log("完成");
            await  redis.set("pair_new_now",Number(new_length));    
            await delay(10000);      
            // const contractAddress = '******************************************';
            // flag = false;
            // // 使用 ABI 和地址创建合约实例
            // const contract = new ethers.Contract(contractAddress, factoryAbi, provider);
            // // 使用 callStatic 调用合约函数
            // let result = await contract.allPairsLength();//contract.callStatic[functionName](...args);//contract.allPairs(...args);//contract.callStatic[functionName](...args);
            
        }catch(e){
            console.log(e);
            flag = true;
        }
    }
}

batchCallContracts();
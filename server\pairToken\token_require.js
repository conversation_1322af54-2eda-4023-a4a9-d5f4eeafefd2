const {ethers} = require('ethers');
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const {queryNginxProvider} = require('../avi/factory_provider');
const provider = queryNginxProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const {multicall, allAbi} = require("../abi");
const {formatTimestampUTC} = require("../utils/DateUtils")
const {delay, isUsdt, isBusd, formatAddress} = require("../utils/functions");
const {getCreateBlockTxHash, getCreateBlockByTxHash} = require("../utils/time_currency");
const axios = require("axios");
const key = "token_address_data";
const token_key = "token_json";

async function batchCallContracts() {
    let flag = true;
    let start_flag = true;
    while (flag) {
        try {
            if (start_flag) {
                let token_address = await redis.lPop(key);
                //await redis.rPush(key,formatAddress(token_address));
                console.log("开始查询：", token_address);
                let token_json = await redis.hGet(token_key, formatAddress(token_address));
                if (!token_json) {
                    start_flag = false;
                    //let url = "http://127.0.0.1:8088/api/ave/token/getTokenBase?token_address="+token_address;
                    // let url = "http://127.0.0.1:5000/bsc?address="+token_address;
                    // let res = await axios.get(url); 
                    // let txHash = await getCreateBlockTxHash(token_address);
                    // console.log("hash：",txHash);
                    // let res = await getCreateBlockByTxHash(txHash);
                    // console.log("结果：",res);
                    // if(res){
                    // let data_token = res;
                    let token = {
                        address: token_address,
                        created_block: "",//data_token.created_block,
                        block_time: ""
                    }
                    console.log(token_address);
                    console.log(JSONbig.stringify(token))
                    await redis.hSet(token_key, token_address, JSONbig.stringify(token));
                    await redis.rPush("token_json_consumer", JSONbig.stringify(token));
                    console.log("存入redis完成");
                    // }else{
                    //     await redis.rPush(key,formatAddress(token_address));
                    //     console.log("存入redis异常");
                    // }
                    start_flag = true;
                } else {
                    await delay(10000);
                    continue;
                }
            }

        } catch (ex) {
            start_flag = true;
            console.log(ex);
        }
    }
}

async function start() {
    for (let index = 0; index < 1; index++) {
        new Promise(async (resolves, rejects) => {
            batchCallContracts();
        });

    }

}

batchCallContracts();
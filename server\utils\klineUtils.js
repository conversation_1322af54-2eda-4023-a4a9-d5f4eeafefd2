const {errorToRedis, redis} = require("../utils/redisUtils");
const FirstAnalyzeEnum = require("../utils/enums/AnalyzeDorisKeyEnum");
const {default: BigNumber} = require("bignumber.js");
const {formatUnits, Contract} = require("ethers");
BigNumber.config({DECIMAL_PLACES: 100});
const {delay, isBnb, isUsdt, isBusd} = require("../utils/functions");
const {dbSyncsQuery} = require("../model/mysql/AviBaseSyncsModel");
const {dbPriceTimeQuery} = require('../model/mysql/PriceTimeModel');
const {isUsdc, formatAddress} = require("./functions");
const JSONbig = require('json-bigint')({storeAsString: true});
const {formatDateToString} = require("../utils/DateUtils");
const https = require("https");
const {getHeaders} = require("../services/webmagic/config/httpHead");
const {allAbi} = require("../abi");
const {formatTimestamp} = require("./DateUtils");
const {img_url_upload} = require("../services/webmagic/task/imgUpload");
const ave_url = "api.eskegs.com";
let pairCache = {};
let tokenCache = {};
let wbnbCache = {};
let syncCache = {};

/**
 * 写入缓存
 * @param pCache
 * @param tCache
 * @param wCache
 * @returns {Promise<void>}
 */
async function setUtilsCache(pCache, tCache, wCache) {
    pairCache = pCache;
    tokenCache = tCache;
    wbnbCache = wCache;
    syncCache = {};
}

/**
 * 获取当前Sync的价格
 * @param { BaseSyncModel } sync
 * @param { String } path
 * @param { Number } tokenIndex
 * @returns { { tokenAPriceUsd: BigNumber,
 * tokenBPriceUsd: BigNumber,
 * price: BigNumber,
 * reserve0: string,
 * reserve1: string } }
 */
async function getSyncPairPrice(sync, pair) {
    let tokenIndex = pair.token_index;
    //获取两个token的decimal(精度)
    let token0 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pair.token0_addr);
    token0 = JSON.parse(token0);
    // let token_list0 = dbTokenQuery([{//根据token0_id查询出token0
    //     type: "string",
    //     column: "id",
    //     value: pair.token0_id
    // }]);
    // if (token_list0.length == 0) {
    //     return null;
    // }
    // let token0 = token_list0[0];
    let token1 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pair.token1_addr);
    token1 = JSON.parse(token1);
    // let token_list1 = dbTokenQuery([{//根据token1_id查询出token1
    //     type: "string",
    //     column: "id",
    //     value: pair.token1_id
    // }]);
    // if (token_list1.length == 0) {
    //     return null;
    // }
    // let token1 = token_list1[0];
    let quote;//计算比率
    let reserve0 = new BigNumber(formatUnits(sync.reserve0, token0.decimals));
    let reserve1 = new BigNumber(formatUnits(sync.reserve1, token1.decimals));
    const isZero = reserve0.eq(0) || reserve1.eq(0);
    if (tokenIndex == 0) {
        quote = !isZero ? reserve1.div(reserve0) : new BigNumber(0);
    } else {
        quote = !isZero ? reserve0.div(reserve1) : new BigNumber(0);
    }

    //获取到USDT价格
    let priceUsd = await getTokenBPriceUsdByPath(pair, sync);
    let tokenAPriceUsd = quote.multipliedBy(priceUsd);
    return {
        tokenAPriceUsd: tokenAPriceUsd,
        tokenBPriceUsd: priceUsd,
        price: tokenAPriceUsd,
        reserve0: formatUnits(sync.reserve0, token0.decimals),
        reserve1: formatUnits(sync.reserve1, token1.decimals),
    };
}

async function getSyncCache(key) {
    let syncItem = syncCache[key];
    return syncItem;
}

async function getTokenCache(address) {
    let token = tokenCache[address];
    if (!token) {
        token = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, address);
        if (token) {
            token = JSON.parse(token);
            tokenCache[address] = token;
        }
    }
    return token;
}

async function getTokenCacheTwo(address) {

    let token = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, address);
    if (token) {
        token = JSON.parse(token);
    }
    return token;
}

async function getPairCache(address) {
    let pair = pairCache[address];
    if (!pair) {
        pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata, address);
        if (pair) {
            pair = JSON.parse(pair);
            pairCache[address] = pair;
        }
    }
    return pair;
}

async function getWbnbCache(address, blockNumber) {
    let priceU = wbnbCache[address + "_" + blockNumber];
    if (!priceU) {
        priceU = await getWbnbUsd(address, blockNumber);
        if (priceU) {
            wbnbCache[address + "_" + blockNumber] = priceU;
        }
    }
    return priceU;
}

/**
 * 获取当前Sync的价格(缓存传参方式)
 * @param { BaseSyncModel } sync
 * @param { String } path
 * @param { Number } tokenIndex
 * @returns { { tokenAPriceUsd: BigNumber,
 * tokenBPriceUsd: BigNumber,
 * price: BigNumber,
 * reserve0: string,
 * reserve1: string } }
 */
async function getSyncPairPriceCache(sync, pair) {

    let tokenIndex = pair.token_index;
    //获取两个token的decimal(精度)
    let token0 = await getTokenCache(pair.token0_addr);
    if (!token0) {
        throw Error("未找到token0:" + pair.token0_addr);
    }
    // let token_list0 = dbTokenQuery([{//根据token0_id查询出token0
    //     type: "string",
    //     column: "id",
    //     value: pair.token0_id
    // }]);
    // if (token_list0.length == 0) {
    //     return null;
    // }
    // let token0 = token_list0[0];
    let token1 = await getTokenCache(pair.token1_addr);
    if (!token1) {
        throw Error("未找到token1:" + pair.token1_addr);
    }
    // let token_list1 = dbTokenQuery([{//根据token1_id查询出token1
    //     type: "string",
    //     column: "id",
    //     value: pair.token1_id
    // }]);
    // if (token_list1.length == 0) {
    //     return null;
    // }
    // let token1 = token_list1[0];
    let quote;//计算比率
    let reserve0;
    let reserve1;
    let reserve0_decimal;
    let reserve1_decimal;
    let isZero;
    if (tokenIndex == 0) {
        reserve0 = formatUnits(sync.reserve0, token0.decimals);
        reserve1 = formatUnits(sync.reserve1, token1.decimals);
        reserve0_decimal = sync.reserve0;
        reserve1_decimal = sync.reserve1;
    } else {
        reserve0 = formatUnits(sync.reserve1, token1.decimals);
        reserve1 = formatUnits(sync.reserve0, token0.decimals);
        reserve0_decimal = sync.reserve1;
        reserve1_decimal = sync.reserve0;
    }
    let bigN0 = new BigNumber(reserve0);
    let bigN1 = new BigNumber(reserve1);
    isZero = bigN0.eq(0) || bigN1.eq(0);
    quote = !isZero ? bigN1.div(bigN0) : new BigNumber(0);
    //获取到USDT价格
    let priceUsd = await getTokenBPriceUsdByPathCache(pair, sync);
    let tokenAPriceUsd = quote.multipliedBy(priceUsd);
    return {
        tokenAPriceUsd: tokenAPriceUsd,
        tokenBPriceUsd: priceUsd,
        price: tokenAPriceUsd,
        reserve0: reserve0,
        reserve1: reserve1,
        reserve0_decimal: reserve0_decimal,//存放未解析的decimal
        reserve1_decimal: reserve1_decimal//存放未解析的decimal
    };
}

async function getTokenBPriceUsdByPathCache(pair, sync) {
    let priceUsd;
    /**
     * 判断当前pair是否能直接到u
     */
    if (pair.token_index == 0) {
        priceUsd = await getWbnbCache(pair.token1_addr, sync.block_number);
        if (priceUsd) {
            return priceUsd;
        }
    } else {
        priceUsd = await getWbnbCache(pair.token0_addr, sync.block_number);
        if (priceUsd) {
            return priceUsd;
        }
    }
    /**
     * 如果当前pair到不了u则找路径
     * @type {BigNumber.default | BigNumber}
     */
    priceUsd = new BigNumber(1);
    let isUok = false;
    if (!pair.path || pair.path == '') {
        throw Error("path为空");
    }
    const pairAddress = pair.path.split(",");
    for (let i = 1; i < pairAddress.length; i++) {
        //查询pair交易对
        let pairGet = await getPairCache(pairAddress[i]);
        if (!pairGet) {
            throw Error("未找到pair");
        }
        //查询token0代币
        let token0 = await getTokenCache(pairGet.token0_addr);
        if (!token0) {
            throw Error("未找到token0");
        }
        //查询token1代币
        let token1 = await getTokenCache(pairGet.token1_addr);
        if (!token1) {
            throw Error("未找到token1");
        }
        //查询当前变动这一时刻前它的最后一次sync
        let key = sync.block_number + '_' + pairGet.address + '_' + sync.log_index;
        let findSync;
        if (!getSyncCache[key]) {
            findSync = await dbSyncsQuery([{//先查询当前区块
                    type: "string",
                    column: "block_number",
                    value: Number(sync.block_number)
                }, {
                    type: "string",
                    column: "address",
                    value: pairGet.address
                }, {
                    type: "lt",
                    column: "log_index",
                    value: Number(sync.log_index)
                }],
                [{
                    type: "desc",
                    column: "block_number"
                }, {
                    type: "desc",
                    column: "log_index"
                }], {start: 0, end: 1});

            if (findSync.length == 0) {//如果没有则查询之前的区块
                let end = Number(sync.block_number);
                let start = end - 100000;//往前查询100000个
                findSync = await dbSyncsQuery([
                    {
                        type: "ge",
                        column: "block_number",
                        value: start
                    },
                    {
                        type: "lt",
                        column: "block_number",
                        value: end
                    },
                    {
                        type: "string",
                        column: "address",
                        value: pairGet.address
                    }
                ], [{
                    type: "desc",
                    column: "block_number"
                }, {
                    type: "desc",
                    column: "log_index"
                }], {start: 0, end: 1});
            }
            syncCache[key] = findSync;//缓存
        } else {
            findSync = getSyncCache(key);
        }
        if (findSync.length > 0) {
            let reserve0 = new BigNumber(formatUnits(findSync[0].reserve0, token0.decimals));
            let reserve1 = new BigNumber(formatUnits(findSync[0].reserve1, token1.decimals));
            let usd;
            if (pairGet.token_index == 0) {
                priceUsd = new BigNumber(reserve1).div(reserve0).multipliedBy(priceUsd);
                usd = await getWbnbCache(pairGet.token1_addr, findSync[0].block_number);
                if (usd) {
                    isUok = true;
                    priceUsd = priceUsd.multipliedBy(usd);
                    break;
                }
            } else {
                priceUsd = new BigNumber(reserve0).div(reserve1).multipliedBy(priceUsd);
                usd = await getWbnbCache(pairGet.token0_addr, findSync[0].block_number);
                if (usd) {
                    isUok = true;
                    priceUsd = priceUsd.multipliedBy(usd);
                    break;
                }
            }
        }
    }
    if (!isUok) {//如果始终没有找到u则置为0
        priceUsd = BigNumber(0);
    }
    return priceUsd;
}

async function getWbnbUsd(token_addr, block_no) {
    let price = new BigNumber(0);
    if (isBnb(token_addr)) {
        let no = Number(block_no);
        let index = 0;
        while (true) {
            let priceUsd = await redis.hGet("WBNB_LIST_KEY", no + '');
            if (priceUsd) {
                price = new BigNumber(priceUsd);
                break;
            } else {//如果当前没有往前个区块找
                no--;
                index++;
                if (no < 6816708) {//如果找到最前面都没有则退出
                    break;
                }
            }
            if (index > 10) {
                throw Error("查找超出10次");
            }
        }
    } else if (isUsdt(token_addr) || isBusd(token_addr) || isUsdc(token_addr)) {
        price = new BigNumber(1);
    }
    return price;
}

async function getAveUsd(token_addr) {
    let price = new BigNumber(0);
    if (isUsdt(token_addr) || isBusd(token_addr) || isUsdc(token_addr)) {
        price = new BigNumber(1);
    } else if (isBnb(token_addr)) {
        let requestBnb = new Promise(async function (resolve, reject) {
            let url = '/v1api/v3/tokens/' + token_addr + "-bsc"
            let options = {
                hostname: ave_url, path: url, agent: new https.Agent({
                    //proxy: "http://127.0.0.1:7890"
                }), headers: await getHeaders(),
                timeout: 60000 // 设置超时时间为5秒
            };
            let html = ''
            let response = {};
            let req = https.get(options, function (res) {
                res.on('data', function (chunk) {
                    html += chunk;
                });
                res.on('end', async function () {
                    try {
                        let jsonData = JSON.parse(html);
                        if (!jsonData['data']) {
                            resolve(0);
                            return;
                        }
                        let data ;
                        if(typeof jsonData['data'] === 'string'){
                            data = JSON.parse(jsonData['data']);
                        }else{
                            data = jsonData['data'];
                        }
                        let token = data['token'];
                        if (token) {
                            resolve(token.current_price_usd);
                        } else {
                            resolve(0);
                        }
                    } catch (e) {
                        resolve(0);
                    }
                });
                res.on('error', function (e) {
                    resolve(0);
                });
            });
            req.on('error', function (e) {
                resolve(0);
            })
            req.end();
        });
        price = await requestBnb;
        price = new BigNumber(price);
    }else{
        let requestBnb = new Promise(async function (resolve, reject) {
            let url = '/v1api/v3/tokens/' + token_addr + "-bsc"
            let options = {
                hostname: ave_url, path: url, agent: new https.Agent({
                    //proxy: "http://127.0.0.1:7890"
                }), headers: await getHeaders(),
                timeout: 60000 // 设置超时时间为5秒
            };
            let html = ''
            let response = {};
            let req = https.get(options, function (res) {
                res.on('data', function (chunk) {
                    html += chunk;
                });
                res.on('end', async function () {
                    try {
                        let jsonData = JSON.parse(html);
                        if (!jsonData['data']) {
                            resolve(0);
                            return;
                        }
                        let data ;
                        if(typeof jsonData['data'] === 'string'){
                            data = JSON.parse(jsonData['data']);
                        }else{
                            data = jsonData['data'];
                        }
                        let token = data['token'];
                        if (token) {
                            resolve(token.current_price_usd);
                        } else {
                            resolve(0);
                        }
                    } catch (e) {
                        resolve(0);
                    }
                });
                res.on('error', function (e) {
                    resolve(0);
                });
            });
            req.on('error', function (e) {
                resolve(0);
            })
            req.end();
        });
        price = await requestBnb;
        price = new BigNumber(price);
    }
    return price;
}

async function getTokenAAndBPrice(pair, block_number, log_index) {

    let tokenAprice;
    let tokenBprice;
    let priceUsd;
    if (pair.token_index == 0) {
        priceUsd = await getWbnbUsd(pair.token1_addr, block_number);
        if (priceUsd) {
            tokenBprice = Number(priceUsd);
        }
    } else {
        priceUsd = await getWbnbUsd(pair.token0_addr, block_number);
        if (priceUsd) {
            tokenBprice = Number(priceUsd);
        }
    }

    let token0 = await getTokenCacheTwo(pair.token0_addr);
    if (!token0) {
        throw Error("未找到token0");
    }
    let token1 = await getTokenCacheTwo(pair.token1_addr);
    if (!token1) {
        throw Error("未找到token1");
    }
    console.log("开始获取价格");
    let findSync = await dbSyncsQuery([{//先查询当前区块
            type: "string",
            column: "block_number",
            value: Number(block_number)
        }, {
            type: "string",
            column: "address",
            value: pair.address
        }, {
            type: "lt",
            column: "log_index",
            value: Number(log_index)
        }],
        [{
            type: "desc",
            column: "block_number"
        }, {
            type: "desc",
            column: "log_index"
        }], {start: 0, end: 1});

    if (findSync.length == 0) {//如果没有则查询之前的区块
        let end = Number(block_number);
        let start = end - 10000;//往前查询10000个
        // let outTime = Date.now();
        // while (true) {
        findSync = await dbSyncsQuery([
            {
                type: "ge",
                column: "block_number",
                value: start
            },
            {
                type: "lt",
                column: "block_number",
                value: end
            },
            {
                type: "string",
                column: "address",
                value: pair.address
            }
        ], [{
            type: "desc",
            column: "block_number"
        }, {
            type: "desc",
            column: "log_index"
        }], {start: 0, end: 1});
        // if (findSync) {
        //    console.log("当前查询sync超时")
        // }
        // else {
        //     end = start;
        //     start = start - 100;
        //     if (Date.now() - outTime > 900000) {//如果查询超过15分钟也没有则退出
        //         // throw Error("当前查询sync超时");
        //         break;
        //     }
        // }
        // }
        // if (findSync.length > 0) {
        //     key = findSync[0].block_number + '_' + pairGet.address + '_' + findSync[0].log_index;
        // }
    }
    if (findSync.length > 0) {
        let reserve0 = new BigNumber(formatUnits(findSync[0].reserve0, token0.decimals));
        let reserve1 = new BigNumber(formatUnits(findSync[0].reserve1, token1.decimals));
        if (pair.token_index == 0) {
            tokenAprice = tokenBprice * (reserve1 / reserve0);
        } else {
            tokenAprice = tokenBprice * (reserve0 / reserve1);
        }
    }

    let data_price = {
        'priceA': tokenAprice,
        'priceB': tokenBprice
    }
    return data_price;

}


async function getTokenAAndBPriceByTime(pair, block_number, log_index, amount0, amount1) {

    let tokenAprice = new BigNumber(0);
    let tokenBprice = new BigNumber(0);
    let priceUsd = new BigNumber(0);
    if (pair.token_index == 0) {
        priceUsd = await getWbnbUsd(pair.token1_addr, block_number);
        if (priceUsd) {
            tokenBprice = new BigNumber(priceUsd);
        }
    } else {
        priceUsd = await getWbnbUsd(pair.token0_addr, block_number);
        if (priceUsd) {
            tokenBprice = new BigNumber(priceUsd);
        }
    }
    if (tokenBprice.isEqualTo(0)) {
        /**
         * 如果当前pair到不了u则找路径
         * @type {BigNumber.default | BigNumber}
         */
        priceUsd = new BigNumber(1);
        let isUok = false;
        if (!pair.path || pair.path == '') {
            throw Error("path为空");
        }
        const pairAddress = pair.path.split(",");
        for (let i = 1; i < pairAddress.length; i++) {
            //查询pair交易对
            let pairGet = await getPairCache(pairAddress[i]);
            if (!pairGet) {
                throw Error("未找到pair");
            }
            //查询token0代币
            let token0 = await getTokenCache(pairGet.token0_addr);
            if (!token0) {
                throw Error("未找到token0");
            }
            //查询token1代币
            let token1 = await getTokenCache(pairGet.token1_addr);
            if (!token1) {
                throw Error("未找到token1");
            }
            //查询当前变动这一时刻前它的最后一次sync
            let key = block_number + '_' + pairGet.address + '_' + log_index;
            let findSync;
            if (!getSyncCache[key]) {
                findSync = await dbSyncsQuery([{//先查询当前区块
                        type: "string",
                        column: "block_number",
                        value: Number(block_number)
                    }, {
                        type: "string",
                        column: "address",
                        value: pairGet.address
                    }, {
                        type: "lt",
                        column: "log_index",
                        value: Number(log_index)
                    }],
                    [{
                        type: "desc",
                        column: "block_number"
                    }, {
                        type: "desc",
                        column: "log_index"
                    }], {start: 0, end: 1});

                if (findSync.length == 0) {//如果没有则查询之前的区块
                    let end = Number(block_number);
                    let start = end - 100000;//往前查询100000个
                    findSync = await dbSyncsQuery([
                        {
                            type: "ge",
                            column: "block_number",
                            value: start
                        },
                        {
                            type: "lt",
                            column: "block_number",
                            value: end
                        },
                        {
                            type: "string",
                            column: "address",
                            value: pairGet.address
                        }
                    ], [{
                        type: "desc",
                        column: "block_number"
                    }, {
                        type: "desc",
                        column: "log_index"
                    }], {start: 0, end: 1});
                }
                syncCache[key] = findSync;//缓存
            } else {
                findSync = getSyncCache(key);
            }
            if (findSync.length > 0) {
                let reserve0 = new BigNumber(formatUnits(findSync[0].reserve0, token0.decimals));
                let reserve1 = new BigNumber(formatUnits(findSync[0].reserve1, token1.decimals));
                let usd;
                if (pairGet.token_index == 0) {
                    priceUsd = new BigNumber(reserve1).div(reserve0).multipliedBy(priceUsd);
                    usd = await getWbnbCache(pairGet.token1_addr, findSync[0].block_number);
                    if (usd) {
                        isUok = true;
                        priceUsd = priceUsd.multipliedBy(usd);
                        break;
                    }
                } else {
                    priceUsd = new BigNumber(reserve0).div(reserve1).multipliedBy(priceUsd);
                    usd = await getWbnbCache(pairGet.token0_addr, findSync[0].block_number);
                    if (usd) {
                        isUok = true;
                        priceUsd = priceUsd.multipliedBy(usd);
                        break;
                    }
                }
            }
        }
        if (!isUok) {//如果始终没有找到u则置为0
            tokenBprice = BigNumber(0);
        } else {
            tokenBprice = priceUsd;
        }
    }

    if (pair.token_index == 0) {
        tokenAprice = tokenBprice.multipliedBy(new BigNumber(amount1).div(amount0));
    } else {
        tokenAprice = tokenBprice.multipliedBy(new BigNumber(amount0).div(amount1));
    }

    let data_price = {
        'priceA': tokenAprice,
        'priceB': tokenBprice
    }
    return data_price;

}

async function getTokenAAndBPriceByToken(pair, block_time, log_index) {
    let priceUsd = new BigNumber(0);
    let timestamp = block_time.getTime();
    let findPriceTime = await redis.zRangeByScore(FirstAnalyzeEnum.priceTime_init + ":" + pair.address, timestamp, timestamp);
    let objsFilter = [];
    if (findPriceTime.length > 0) {
        /**
         * 筛选小于当前log_index的
         */
        objsFilter = findPriceTime.filter(str => {
            let jsons = JSON.parse(str);
            if (jsons.log_index < log_index) {
                return true;
            } else {
                return false;
            }
        });
    }
    if (objsFilter.length == 0) {
        //计算查询的时间区间
        let start = timestamp - 604800000;//往前查询7天(因为超过7天说明不在一周以内,无需再查询,不在一周的内的线属于其他周的线了)
        findPriceTime = await redis.zRangeByScore(FirstAnalyzeEnum.priceTime_init + ":" + pair.address, "(" + start, "(" + timestamp);
        if (findPriceTime.length == 0) {
            console.log("没有价格：pair:" + pair.address + "_<" + timestamp);
            return priceUsd;
        } else {
            objsFilter = findPriceTime;
        }
    }
    if (objsFilter.length > 0) {
        let block_map = {}
        let max_obj = JSON.parse(objsFilter[objsFilter.length - 1]);//最后一个就是最大block_number的对象（redis查出来是升序）
        block_map[max_obj.block_number] = [];
        objsFilter.forEach(a => {
            a = JSON.parse(a);
            if (a.block_number == max_obj.block_number) {//只取最大block
                block_map[max_obj.block_number].push(a);
            }
        })
        let arry_logIndex = block_map[max_obj.block_number];//获取最大block_number的对象集合
        arry_logIndex.sort((a, b) => b.log_index - a.log_index);
        let max_logIndex = arry_logIndex[0];//获取最大的logIndex的对象集合
        priceUsd = new BigNumber(max_logIndex.price);
    }
    return priceUsd;

}

//获取价格
async function getTokenBPriceUsdByPath(pair, sync) {
    let priceUsd;
    /**
     * 判断当前pair是否能直接到u
     */
    if (pair.token_index == 0) {
        priceUsd = await getWbnbUsd(pair.token1_addr, sync.block_number);
        if (priceUsd) {
            return priceUsd;
        }
    } else {
        priceUsd = await getWbnbUsd(pair.token0_addr, sync.block_number);
        if (priceUsd) {
            return priceUsd;
        }
    }
    /**
     * 如果当前pair到不了u则找路径
     * @type {BigNumber.default | BigNumber}
     */
    priceUsd = new BigNumber(1);
    let isUok = false;
    const pairAddress = pair.path.split(",");
    for (let i = 1; i < pairAddress.length; i++) {
        //const pairRow = await PairService.getCachePairById(pairIds[i]);
        //查询pair交易对
        // const list = await dbPairQuery([{
        //     type: "string",
        //     column: "address",
        //     value: pairAddress[i]
        // }]);
        // if (list.length === 0) {
        //     continue;
        // }
        // const pair = list[0];
        let pairGet = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata, pairAddress[i]);
        pairGet = JSON.parse(pairGet);
        if (!pairGet) {
            throw Error("未找到pair");
        }
        //查询token0代币
        // let token_list0 = dbTokenQuery([{//根据token0_id查询出token0
        //     type: "string",
        //     column: "token0_id",
        //     value: pair.token0_id
        // }]);
        // if (token_list0.length == 0) {
        //     continue;
        // }
        // let token0 = token_list0[0];
        let token0 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pairGet.token0_addr);
        token0 = JSON.parse(token0);
        if (!token0) {
            throw Error("未找到token0");
        }
        //查询token1代币
        // let token_list1 = dbTokenQuery([{//根据token1_id查询出token1
        //     type: "string",
        //     column: "token1_id",
        //     value: pair.token1_id
        // }]);
        // if (token_list1.length == 0) {
        //     continue;
        // }
        // let token1 = token_list1[0];
        let token1 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pairGet.token1_addr);
        token1 = JSON.parse(token1);
        if (!token1) {
            throw Error("未找到token1");
        }
        //const SyncModel = await BaseSyncModel();
        // let token0 = await TokenModel().findOne({where: {id: pairRow.token0_id}});
        // let token1 = await TokenModel().findOne({where: {id: pairRow.token1_id}});
        //查询当前变动这一时刻前它的最后一次sync
        let findSync = await dbSyncsQuery([{//先查询当前区块
                type: "string",
                column: "block_number",
                value: Number(sync.block_number)
            }, {
                type: "string",
                column: "address",
                value: pairGet.address
            }, {
                type: "lt",
                column: "log_index",
                value: Number(sync.log_index)
            }],
            [{
                type: "desc",
                column: "block_number"
            }, {
                type: "desc",
                column: "log_index"
            }], {start: 0, end: 1});
        if (findSync.length == 0) {//如果没有则查询之前的区块
            // let end = Number(sync.block_number);
            // let start = end - 100;//往前查询10个
            // let outTime = Date.now();
            // while (true) {
            findSync = await dbSyncsQuery([
                {
                    type: "lt",
                    column: "block_number",
                    value: Number(sync.block_number)
                },
                {
                    type: "string",
                    column: "address",
                    value: pairGet.address
                }
            ], [{
                type: "desc",
                column: "block_number"
            }, {
                type: "desc",
                column: "log_index"
            }], {start: 0, end: 1});
            // if (findSync) {
            //    console.log("当前查询sync超时")
            // }
            // else {
            //     end = start;
            //     start = start - 100;
            //     if (Date.now() - outTime > 900000) {//如果查询超过15分钟也没有则退出
            //         // throw Error("当前查询sync超时");
            //         break;
            //     }
            // }
            // }
        }
        if (findSync.length > 0) {
            let reserve0 = new BigNumber(formatUnits(findSync[0].reserve0, token0.decimals));
            let reserve1 = new BigNumber(formatUnits(findSync[0].reserve1, token1.decimals));
            let usd;
            if (pairGet.token_index == 0) {
                usd = await getWbnbUsd(pairGet.token1_addr, findSync[0].block_number);
                if (usd) {
                    priceUsd = usd;//获取u单价
                    isUok = true;
                    break;
                }
                priceUsd = new BigNumber(reserve1).div(reserve0).multipliedBy(priceUsd);
            } else {
                usd = await getWbnbUsd(pairGet.token0_addr, findSync[0].block_number);
                if (usd) {
                    priceUsd = usd;//获取u单价
                    isUok = true;
                    break;
                }
                priceUsd = new BigNumber(reserve0).div(reserve1).multipliedBy(priceUsd);
            }
        }
    }
    if (!isUok) {//如果始终没有找到u则置为0
        priceUsd = BigNumber(0);
    }
    return priceUsd;
}

//
// async function getTokenBPriceUsdByPathTrans(token,pair,info) {
//     let priceUsd;
//
//     if (pair.token_index == 0) {
//         priceUsd = await getWbnbUsd(pair.token1_addr, info.block_number);
//         if (priceUsd) {
//             return priceUsd;
//         }
//     } else {
//         priceUsd = await getWbnbUsd(pair.token0_addr, info.block_number);
//         if (priceUsd) {
//             return priceUsd;
//         }
//     }
//
//     priceUsd = new BigNumber(1);
//     let isUok = false;
//     const pairAddress = pair.path.split(",");
//     for (let i = 1; i < pairAddress.length; i++) {
//
//         let pairGet = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata, pairAddress[i]);
//         pairGet = JSON.parse(pairGet);
//         if (!pairGet) {
//             throw Error("未找到pair");
//         }
//
//         let token0 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pairGet.token0_addr);
//         token0 = JSON.parse(token0);
//         if (!token0) {
//             throw Error("未找到token0");
//         }
//
//         let token1 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pairGet.token1_addr);
//         token1 = JSON.parse(token1);
//         if (!token1) {
//             throw Error("未找到token1");
//         }
//         let findSync = await dbSyncsQuery([{//先查询当前区块
//                 type: "string",
//                 column: "block_number",
//                 value: Number(info.block_number)
//             }, {
//                 type: "string",
//                 column: "address",
//                 value: pairGet.address
//             }, {
//                 type: "lt",
//                 column: "log_index",
//                 value: Number(info.log_index)
//             }],
//             [{
//                 type: "desc",
//                 column: "block_number"
//             }, {
//                 type: "desc",
//                 column: "log_index"
//             }], {start: 0, end: 1});
//         if (findSync.length == 0) {//如果没有则查询之前的区块
//
//             findSync = await dbSyncsQuery([
//                 {
//                     type: "lt",
//                     column: "block_number",
//                     value: Number(info.block_number)
//                 },
//                 {
//                     type: "string",
//                     column: "address",
//                     value: pairGet.address
//                 }
//             ], [{
//                 type: "desc",
//                 column: "block_number"
//             }, {
//                 type: "desc",
//                 column: "log_index"
//             }], {start: 0, end: 1});
//
//         }
//         if (findSync.length > 0) {
//             let reserve0 = new BigNumber(formatUnits(findSync[0].reserve0, token0.decimals));
//             let reserve1 = new BigNumber(formatUnits(findSync[0].reserve1, token1.decimals));
//             let usd;
//             if (pairGet.token_index == 0) {
//                 usd = await getWbnbUsd(pairGet.token1_addr, findSync[0].block_number);
//                 if (usd) {
//                     priceUsd = usd;//获取u单价
//                     isUok = true;
//                     break;
//                 }
//                 priceUsd = new BigNumber(reserve1).div(reserve0).multipliedBy(priceUsd);
//             } else {
//                 usd = await getWbnbUsd(pairGet.token0_addr, findSync[0].block_number);
//                 if (usd) {
//                     priceUsd = usd;//获取u单价
//                     isUok = true;
//                     break;
//                 }
//                 priceUsd = new BigNumber(reserve0).div(reserve1).multipliedBy(priceUsd);
//             }
//         }
//     }
//     if (!isUok) {//如果始终没有找到u则置为0
//         console.log("始终没找到U")
//         priceUsd = BigNumber(0);
//     }
//     return priceUsd;
// }


module.exports = {
    getSyncPairPrice,
    getSyncPairPriceCache,
    getWbnbUsd,
    setUtilsCache,
    getTokenAAndBPrice,
    getTokenAAndBPriceByToken,
    getTokenAAndBPriceByTime,
    getTokenBPriceUsdByPath,
    getTokenBPriceUsdByPathCache,
    // getTokenBPriceUsdByPathTrans
    getAveUsd
};
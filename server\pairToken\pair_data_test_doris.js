
const redis = require("../database/redis");
const { delay, isUsdt, isBusd, formatAddress } = require("../utils/functions");

const JSONbig = require('json-bigint')({ storeAsString: true });
async function batchCallContracts() {
    while(true){
        let pair_address = await redis.lPop("avePairsSL_error");
        if(pair_address){

        }else{
            break;
        }
        //将token存入到redis
        let flag_now = await redis.hGet("pair_json_hdata", formatAddress(pair_address));
        let element = JSONbig.parse(flag_now);

        if(!element['created_time']){
            await redis.hSet("pairs_time_error",pair_address,flag_now);
        }

    }
    console.log("插入完成");
}

batchCallContracts();
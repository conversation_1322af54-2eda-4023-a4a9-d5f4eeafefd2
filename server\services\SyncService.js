const {default: BigNumber} = require("bignumber.js");
const {errorToRedis, redis} = require("../utils/redisUtils");
const {getKlineTime, isBnb, isUsdt, isBusd} = require('../utils/functions');
const {formatUnits} = require("ethers");
// const {getMaxSupply} = require('../utils/EtherUtils');
const KlineModel = require("../model/mysql/KlineModel");
const PeriodEnum = require("../utils/enums/PeriodEnum");
const {Model, Op} = require("sequelize");
const {dbAviKline1MQuery} = require('../model/mysql/AviKline_1MModel');
const {dbAviKline5MQuery} = require('../model/mysql/AviKline_5MModel');
const {dbAviKline15MQuery} = require('../model/mysql/AviKline_15MModel');
const {dbAviKline30MQuery} = require('../model/mysql/AviKline_30MModel');
const {dbAviKline1HQuery} = require('../model/mysql/AviKline_1HModel');
const {dbAviKline2HQuery} = require('../model/mysql/AviKline_2HModel');
const {dbAviKline4HQuery} = require('../model/mysql/AviKline_4HModel');
const {dbAviKline1DQuery} = require('../model/mysql/AviKline_1DModel');
const {dbAviKline1WQuery} = require('../model/mysql/AviKline_1WModel');
// const {dbSyncsQuery} = require("../model/mysql/AviBaseSyncsModel");
const {dbPriceTimeQuery} = require("../model/mysql/PriceTimeModel");
const {slKline1MSLOne, slKline1MSLDistroy} = require('../utils/doris/aveKline1MSL');
const {slKline5MSLOne, slKline5MSLDistroy} = require('../utils/doris/aveKline5MSL');
const {slKline15MSLOne, slKline15MSLDistroy} = require('../utils/doris/aveKline15MSL');
const {slKline30MSLOne, slKline30MSLDistroy} = require('../utils/doris/aveKline30MSL');
const {slKline1HSLOne, slKline1HSLDistroy} = require('../utils/doris/aveKline1HSL');
const {slKline2HSLOne, slKline2HSLDistroy} = require('../utils/doris/aveKline2HSL');
const {slKline4HSLOne, slKline4HSLDistroy} = require('../utils/doris/aveKline4HSL');
const {slKline1DSLOne, slKline1DSLDistroy} = require('../utils/doris/aveKline1DSL');
const {slKline1WSLOne, slKline1WSLDistroy} = require('../utils/doris/aveKline1WSL');
const {slAvePairsOne} = require("../utils/doris/avePairsSL");
const {slAveTokensOne} = require("../utils/doris/aveTokensSL");
const FirstAnalyzeEnum = require("../utils/enums/AnalyzeDorisKeyEnum");
const {StringToTimeStamp, formatTimestamp} = require("../utils/DateUtils");
const {getSyncPairPrice, getSyncPairPriceCache} = require("../utils/klineUtils");

// const dayjs = require("dayjs");
let syncBlockNumber;
let pairCache;
let tokenCache;
let wbnbCache;
let priceTimeCache;
let klineCache;

/**
 *
 * @param { String } path
 * @param { Number } blockNumber
 * @returns { { tokenAPriceUsd: BigNumber, tokenBPriceUsd: BigNumber } }
 */

function formatDataPrice(data) {
    data.tokenAPriceUsd = new BigNumber(data.tokenAPriceUsd);
    data.tokenBPriceUsd = new BigNumber(data.tokenBPriceUsd);
    data.price = new BigNumber(data.price);
    return data;
}

async function saveKline(parseList, blockNo, blockTime) {
    /**
     * 处理sync
     */
    if (parseList.length > 0) {
        //根据log_index排序
        parseList.forEach(item => {
            formatDataPrice(item);//转换price为BigNumber
        });
        parseList.sort(function (a, b) {
            return a.logIndex - b.logIndex;
        });
        const openData = parseList[0];
        // let pairInfo = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata, openData.pair_address);
        // pairInfo = JSON.parse(pairInfo);
        let pairInfo = pairCache[openData.pair_address];
        if (!pairInfo || pairInfo.status == 0) {
            return;
        }
        const closeData = parseList[parseList.length - 1];

        //根据价格排序 升序
        let tempList = parseList.slice();
        tempList.sort(function (a, b) {
            return (a.price).comparedTo(b.price);
        })
        const lowData = tempList[0];
        const highData = tempList[tempList.length - 1];
        //获取当前的关盘价格的tag，tag的方式：当前区块号
        const closeTag = String(blockNo).padEnd(30, '0') + String(closeData.logIndex).padStart(30, '0');//补足位数
        //获取当前的开盘价格的tag，tag的方式：当前区块号
        const openTag = String(blockNo).padEnd(30, '0') + String(openData.logIndex).padStart(30, '0');//补足位数
        //1分钟K线
        await doSaveKlineData(PeriodEnum.MINUTE_1, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //5分钟K线
        await doSaveKlineData(PeriodEnum.MINUTE_5, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //15分钟K线
        await doSaveKlineData(PeriodEnum.MINUTE_15, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //30分钟K线
        await doSaveKlineData(PeriodEnum.MINUTE_30, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //1小时K线
        await doSaveKlineData(PeriodEnum.MINUTE_60, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //2小时K线
        await doSaveKlineData(PeriodEnum.MINUTE_120, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //4小时K线
        await doSaveKlineData(PeriodEnum.MINUTE_240, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //1周K线(这里放1天k线前面是因为防止1天k线解析的updatePairAndToken方法报错导致无法执行)
        await doSaveKlineData(PeriodEnum.WEEK, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
        //1天K线
        await doSaveKlineData(PeriodEnum.DAY, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime);
    }
}

async function getKlineByPeriod(address, klineTime, period) {
    let kline;
    let key = address + '_' + klineTime + '_' + period;
    if (!klineCache[key]) {
        let param = [
            {
                type: "string",
                column: "pair_address",
                value: address
            }, {
                type: "string",
                column: "kline_time",
                value: klineTime
            }, {
                type: "string",
                column: "period",
                value: period
            }];
        switch (period) {
            case PeriodEnum.MINUTE_1:
                kline = await dbAviKline1MQuery(param);
                break;
            case PeriodEnum.MINUTE_5:
                kline = await dbAviKline5MQuery(param);
                break;
            case PeriodEnum.MINUTE_15:
                kline = await dbAviKline15MQuery(param);
                break;
            case PeriodEnum.MINUTE_30:
                kline = await dbAviKline30MQuery(param);
                break;
            case PeriodEnum.MINUTE_60:
                kline = await dbAviKline1HQuery(param);
                break;
            case PeriodEnum.MINUTE_120:
                kline = await dbAviKline2HQuery(param);
                break;
            case PeriodEnum.MINUTE_240:
                kline = await dbAviKline4HQuery(param);
                break;
            case PeriodEnum.DAY:
                kline = await dbAviKline1DQuery(param);
                break;
            case PeriodEnum.WEEK:
                kline = await dbAviKline1WQuery(param);
                break;
        }
        klineCache[key] = kline;
    } else {
        kline = klineCache[key];
    }
    return kline;
}

async function doSaveKlineData(period, pairInfo, openData, closeData, lowData, highData, closeTag, openTag, blockTime) {

    let klineTime = getKlineTime(StringToTimeStamp(blockTime), period);
    let klineTimeStr = formatTimestamp(klineTime);
    let kline = await getKlineByPeriod(pairInfo.address, klineTimeStr, period);
    //更新价格参数
    if (kline.length > 0) {//如果当前时间有kline
        await updateKline(pairInfo, kline[0], openData, closeData, highData, lowData, openTag, closeTag, blockTime);
    } else {
        await createKline(pairInfo, period, klineTimeStr, openData, closeData, highData, lowData, openTag, closeTag, blockTime);
    }

}

async function createKline(pairInfo, period, klineTime, openData, closeData, highData, lowData, openTag, closeTag, blockTime) {
    let open = openData.price;
    let list;
    //计算查询的时间区间
    let end = StringToTimeStamp(klineTime);//时间戳
    let endStr = klineTime;
    let start = end - 604800;//往前查询7天(因为超过7天说明不在一周以内,无需再查询,不在一周的内的线属于其他周的线了)
    let startStr = formatTimestamp(start);
    // let outTime = Date.now();
    // while (true) {
    let key = startStr + '_' + endStr + '_' + pairInfo.address;
    //没有k线数据线 找开盘价 查询上一个节点的价格
    if (!priceTimeCache[key]) {
        list = await dbPriceTimeQuery([{
            type: "ge",
            column: "block_time",
            value: startStr
        }, {
            type: "lt",
            column: "block_time",
            value: endStr
        }, {
            type: "string",
            column: "address",
            value: pairInfo.address
        }], [{
            type: "desc",
            column: "block_time",
        }, {
            type: "desc",
            column: "log_index",
        }], {start: 0, end: 1});
        priceTimeCache[key] = list;
    } else {
        list = priceTimeCache[key];
    }
    if (list.length > 0) {
        let priceTime = list[0];
        open = new BigNumber(priceTime.price);
        openTag = priceTime.block_number.toString().padEnd(30, '0') + priceTime.log_index.toString().padStart(30, '0');
        // break;
    }
    // else {
    // end = start;
    // start = end - 86400;//往前查询1天
    // endStr = formatTimestamp(end);
    // startStr = formatTimestamp(start);
    // if (Date.now() - outTime > 900000) {//如果查询超过15分钟也没有则退出
    //     // throw Error("当前查询sync超时");
    //     break;
    // }
    // }
    // }
    let high = highData.price;
    if (open.comparedTo(high) > 0) {
        high = open;
    }
    let low = lowData.price;
    if (open.comparedTo(low) < 0) {
        low = open;
    }

    const klineData = {
        pair_address: pairInfo.address,
        open: open.toFixed(30),
        close: closeData.price.toFixed(30),
        high: high.toFixed(30),
        low: low.toFixed(30),
        kline_time: klineTime,
        period: period,
        close_tag: closeTag,
        open_tag: openTag,
    }
    const priceChange = new BigNumber(klineData.close).minus(klineData.open);
    klineData.price_change = priceChange.toFixed(30);
    klineData.amplitude = "0";
    klineData.change_pct = "0";
    if (!(new BigNumber(klineData.open)).isZero()) {
        klineData.amplitude = new BigNumber(klineData.high)
            .minus(klineData.low)
            .div(klineData.open)
            .multipliedBy(100)
            .toFixed(30);
        klineData.change_pct = priceChange.div(klineData.open)
            .multipliedBy(100).toFixed(30);
    }
    //判断是否超出长度
    if (klineData.price_change.length > 60) {
        klineData.price_change = ">" + klineData.price_change.substring(0, 57);
    }
    //判断是否超出长度
    if (klineData.amplitude.length > 60) {
        klineData.amplitude = ">" + klineData.amplitude.substring(0, 57);
    }
    //判断是否超出长度
    if (klineData.change_pct.length > 60) {
        klineData.change_pct = ">" + klineData.change_pct.substring(0, 57);
    }
    //创建kline
    let row = `${pairInfo.address ? pairInfo.address : ''}|${klineData.kline_time ? klineData.kline_time : ''}|${klineData.period ? klineData.period : 0}|${klineData.open ? klineData.open : 0}|${klineData.high ? klineData.high : ''}|${klineData.low ? klineData.low : ''}|${klineData.close ? klineData.close : ''}|${klineData.volume ? klineData.volume : ''}|${klineData.open_tag ? klineData.open_tag : ''}|${klineData.close_tag ? klineData.close_tag : ''}|${klineData.amplitude ? klineData.amplitude : ''}|${klineData.trade_amount ? klineData.trade_amount : ''}|${klineData.price_change ? klineData.price_change : ''}|${klineData.change_pct ? klineData.change_pct : ''}|${klineData.trade_num ? klineData.trade_num : 0}`;
    let kObjK = pairInfo.address + '_' + klineData.kline_time + '_' + klineData.period;
    let kObj = klineCache[kObjK];
    if (!kObj || kObj.length == 0) {
        kObj = [{}];
    }
    kObj[0].pair_address = pairInfo.address;
    kObj[0].kline_time = klineData.kline_time;
    kObj[0].period = klineData.period;
    kObj[0].open = klineData.open;
    kObj[0].high = klineData.high;
    kObj[0].low = klineData.low;
    kObj[0].close = klineData.close;
    kObj[0].volume = klineData.volume;
    kObj[0].open_tag = klineData.open_tag;
    kObj[0].close_tag = klineData.close_tag;
    kObj[0].amplitude = klineData.amplitude;
    kObj[0].trade_amount = klineData.trade_amount;
    kObj[0].price_change = klineData.price_change;
    kObj[0].change_pct = klineData.change_pct;
    kObj[0].trade_num = klineData.trade_num;
    klineCache[kObjK] = kObj;//更新缓存
    switch (period) {
        case PeriodEnum.MINUTE_1:
            slKline1MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_5:
            slKline5MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_15:
            slKline15MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_30:
            slKline30MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_60:
            slKline1HSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_120:
            slKline2HSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_240:
            slKline4HSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.DAY:
            slKline1DSLOne(row, syncBlockNumber);
            await updatePairAndToken(pairInfo, klineData, closeData, blockTime);//更新24小时涨跌幅
            break;
        case PeriodEnum.WEEK:
            slKline1WSLOne(row, syncBlockNumber);
            break;
    }
}

/**
 *
 * @param { Model } kline
 * @param { JSON } openData
 * @param { JSON } closeData
 * @param { JSON } highData
 * @param { JSON } lowData
 * @param { String } openTag
 * @param { String } closeTag
 */
async function updateKline(pairInfo, kline, openData, closeData, highData, lowData, openTag, closeTag, blockTime) {
    let klineData = {
        pair_address: pairInfo.address,
        high: kline.high,
        low: kline.low,
        open: kline.open,
        close: kline.close,
        open_tag: kline.open_tag,
        close_tag: kline.close_tag
    };
    //当前openTag
    let openTagNumber = new BigNumber(openTag);
    //旧openTag
    let k_openTagNumber = new BigNumber(kline.open_tag);

    if (openTagNumber.comparedTo(k_openTagNumber) < 0) {
        klineData.open = openData.price.toFixed(30);
        klineData.open_tag = openTag;
        kline.open = klineData.open;
        kline.open_tag = klineData.open_tag;
    }
    //当前closeTag
    let closeTagNumber = new BigNumber(closeTag);
    //旧closeTag
    let k_closeTagNumber = new BigNumber(kline.close_tag);

    if (closeTagNumber.comparedTo(k_closeTagNumber) > 0) {
        klineData.close = closeData.price.toFixed(30);
        klineData.close_tag = closeTag;
        kline.close = klineData.close;
        kline.close_tag = klineData.close_tag;
    }
    if (highData.price.comparedTo(kline.high) > 0) {
        klineData.high = highData.price.toFixed(30);
        kline.high = klineData.high;
    }
    if (lowData.price.comparedTo(kline.low) < 0) {
        klineData.low = lowData.price.toFixed(30);
        kline.low = klineData.low;
    }
    let openPrice = new BigNumber(klineData.open)
    let closePrice = new BigNumber(klineData.close);
    let highPrice = new BigNumber(klineData.high);
    let lowPrice = new BigNumber(klineData.low);

    const priceChange = closePrice.minus(openPrice);
    klineData.price_change = priceChange.toFixed(30);
    klineData.amplitude = highPrice
        .minus(lowPrice)
        .div(openPrice)
        .multipliedBy(100)
        .toFixed(30);
    klineData.change_pct = priceChange.div(openPrice).multipliedBy(100).toFixed(30);
    //判断是否超出长度
    if (klineData.price_change.length > 60) {
        klineData.price_change = ">" + klineData.price_change.substring(0, 57);
    }
    //判断是否超出长度
    if (klineData.amplitude.length > 60) {
        klineData.amplitude = ">" + klineData.amplitude.substring(0, 57);
    }
    //判断是否超出长度
    if (klineData.change_pct.length > 60) {
        klineData.change_pct = ">" + klineData.change_pct.substring(0, 57);
    }
    //覆盖旧kline
    kline.price_change = klineData.price_change;
    kline.amplitude = klineData.amplitude;
    kline.change_pct = klineData.change_pct;
    //更新kline
    kline.kline_time = formatTimestamp(kline.kline_time.getTime());
    let row = `${pairInfo.address ? pairInfo.address : ''}|${kline.kline_time ? kline.kline_time : ''}|${kline.period ? kline.period : 0}|${klineData.open ? klineData.open : 0}|${klineData.high ? klineData.high : ''}|${klineData.low ? klineData.low : ''}|${klineData.close ? klineData.close : ''}|${kline.volume ? kline.volume : ''}|${klineData.open_tag ? klineData.open_tag : ''}|${klineData.close_tag ? klineData.close_tag : ''}|${klineData.amplitude ? klineData.amplitude : ''}|${kline.trade_amount ? kline.trade_amount : ''}|${klineData.price_change ? klineData.price_change : ''}|${klineData.change_pct ? klineData.change_pct : ''}|${kline.trade_num ? kline.trade_num : ''}`;
    let kObjK = pairInfo.address + '_' + kline.kline_time + '_' + kline.period;
    let kObj = klineCache[kObjK];
    if (!kObj) {
        kObj = [{}];
    }
    kObj[0].pair_address = pairInfo.address;
    kObj[0].kline_time = kline.kline_time;
    kObj[0].period = kline.period;
    kObj[0].open = klineData.open;
    kObj[0].high = klineData.high;
    kObj[0].low = klineData.low;
    kObj[0].close = klineData.close;
    kObj[0].volume = kline.volume;
    kObj[0].open_tag = klineData.open_tag;
    kObj[0].close_tag = klineData.close_tag;
    kObj[0].amplitude = klineData.amplitude;
    kObj[0].trade_amount = kline.trade_amount;
    kObj[0].price_change = klineData.price_change;
    kObj[0].change_pct = klineData.change_pct;
    kObj[0].trade_num = kline.trade_num;
    klineCache[kObjK] = kObj;//更新缓存
    switch (kline.period) {
        case PeriodEnum.MINUTE_1:
            slKline1MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_5:
            slKline5MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_15:
            slKline15MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_30:
            slKline30MSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_60:
            slKline1HSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_120:
            slKline2HSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.MINUTE_240:
            slKline4HSLOne(row, syncBlockNumber);
            break;
        case PeriodEnum.DAY:
            slKline1DSLOne(row, syncBlockNumber);
            await updatePairAndToken(pairInfo, kline, closeData, blockTime);//更新24小时涨跌幅
            break;
        case PeriodEnum.WEEK:
            slKline1WSLOne(row, syncBlockNumber);
            break;
    }
}

async function updatePairAndToken(pairInfo, kline, closeData, blockTime) {
    //更新pair 和 token 的价格
    if (!pairInfo.publish_time || (new Date(pairInfo.publish_time) < new Date(blockTime))) {
        let token0_price_usd;
        let token1_price_usd;
        let reserve0_decimal;
        let reserve1_decimal;
        if (pairInfo.token_index == 0) {
            token0_price_usd = closeData.tokenAPriceUsd.toFixed(30);
            token1_price_usd = closeData.tokenBPriceUsd.toFixed(30);
            reserve0_decimal = closeData.reserve0_decimal;
            reserve1_decimal = closeData.reserve1_decimal;
        } else {
            token1_price_usd = closeData.tokenAPriceUsd.toFixed(30);
            token0_price_usd = closeData.tokenBPriceUsd.toFixed(30);
            reserve1_decimal = closeData.reserve0_decimal;
            reserve0_decimal = closeData.reserve1_decimal;
        }

        let pairUpdateData = {
            reserve0: reserve0_decimal,
            reserve1: reserve1_decimal,
            token0_price_usd: token0_price_usd,
            token1_price_usd: token1_price_usd,
            price_change_24h: kline.change_pct,
            publish_time: blockTime
        };
        // let openPrice = pairInfo.open_price;
        if (!pairInfo.open_tag || pairInfo.open_tag > kline.open_tag) {
            pairUpdateData.open_tag = kline.open_tag;
            pairUpdateData.open_time = StringToTimeStamp(blockTime);
            pairUpdateData.open_price = kline.open;
            // openPrice = kline.open;
        }
        pairInfo.reserve0 = pairUpdateData.reserve0;
        pairInfo.reserve1 = pairUpdateData.reserve1;
        pairInfo.token0_price_usd = pairUpdateData.token0_price_usd;
        pairInfo.token1_price_usd = pairUpdateData.token1_price_usd;
        pairInfo.price_change_24h = pairUpdateData.price_change_24h;
        pairInfo.publish_time = pairUpdateData.publish_time;
        pairInfo.open_tag = pairUpdateData.open_tag;
        pairInfo.open_time = pairUpdateData.open_time;
        pairInfo.open_price = pairUpdateData.open_price;
        // await pairInfo.update(pairUpdateData);
        let row = `${pairInfo.address ? pairInfo.address : ''}|${pairInfo.created_time ? pairInfo.created_time : ''}|${pairInfo.symbol ? pairInfo.symbol : ''}|${pairInfo.token0_addr ? pairInfo.token0_addr : ''}|${pairInfo.token1_addr ? pairInfo.token1_addr : ''}|${pairInfo.swap_id ? pairInfo.swap_id : 1}|${pairInfo.decimals ? pairInfo.decimals : 18}|${pairInfo.created_block ? pairInfo.created_block : 0}|${pairInfo.chain_id ? pairInfo.chain_id : 0}|${pairInfo.created_hash ? pairInfo.created_hash : ''}|${pairInfo.reserve0 ? pairInfo.reserve0 : ''}|${pairInfo.reserve1 ? pairInfo.reserve1 : ''}|${pairInfo.pair_index ? pairInfo.pair_index : 0}|${pairInfo.token0_price_usd ? pairInfo.token0_price_usd : ''}|${pairInfo.token0_price_eth ? pairInfo.token0_price_eth : ''}|${pairInfo.token1_price_usd ? pairInfo.token1_price_usd : ''}|${pairInfo.token1_price_eth ? pairInfo.token1_price_eth : ''}|${pairInfo.reserve_usd ? pairInfo.reserve_usd : ''}|${pairInfo.path ? pairInfo.path : ''}|${pairInfo.token_addr ? pairInfo.token_addr : ''}|${pairInfo.token_index ? pairInfo.token_index : 0}|${pairInfo.status ? pairInfo.status : 1}|${pairInfo.holders ? pairInfo.holders : 0}|${pairInfo.tx_count ? pairInfo.tx_count : 0}|${pairInfo.tx_volume ? pairInfo.tx_volume : ''}|${pairInfo.tx_volume_usd ? pairInfo.tx_volume_usd : ''}|${pairInfo.tx_volume_u_24h ? pairInfo.tx_volume_u_24h : ''}|${pairInfo.tx_count_24h ? pairInfo.tx_count_24h : 0}|${pairInfo.tx_volume_24h ? pairInfo.tx_volume_24h : ''}|${pairInfo.price_change_24h ? pairInfo.price_change_24h : ''}|${pairInfo.token_confirm ? pairInfo.token_confirm : 0}|${pairInfo.open_price ? pairInfo.open_price : ''}|${pairInfo.open_time ? pairInfo.open_time : 0}|${pairInfo.is_open ? pairInfo.is_open : 0}|${pairInfo.total_supply ? pairInfo.total_supply : ''}|${pairInfo.create_time ? pairInfo.create_time : ''}|${pairInfo.update_time ? pairInfo.update_time : ''}|${pairInfo.lp_proportion ? pairInfo.lp_proportion : 0}|${pairInfo.publish_time ? pairInfo.publish_time : ''}|${pairInfo.open_tag ? pairInfo.open_tag : ''}`;
        slAvePairsOne(row, pairInfo.address);
        await redis.hSet(FirstAnalyzeEnum.pair_json_hdata, pairInfo.address, pairInfo);//更新redis
        pairCache[pairInfo.address] = pairInfo;//更新缓存
        // await dbUpdatePair([{
        //     type: "string",
        //     column: "id",
        //     value: pairInfo.id
        // }], [pairUpdateData])

        // let token = await TokenModel().findByPk(pairInfo.token_id, {
        //     attributes: ['id', 'current_price_usd', 'price_change', 'publish_time', 'max_supply', 'address', 'decimals', 'total_supply', 'pair_id'],
        // });
        // let token = await redis.hGet(FirstAnalyzeEnum.token_json_hdata, pairInfo.token_addr);
        let token = tokenCache[pairInfo.token_addr];
        /**
         * 需要先跑出token.pair_address
         */
        if (token) {
            if (token.pair_address && token.pair_address != '') {
                if (token.pair_address == pairInfo.address) {
                    let tokenSaveData = {
                        current_price_usd: closeData.price.toFixed(30),
                        price_change: kline.change_pct,
                        publish_time: blockTime,
                    };
                    /**
                     * 池子榜另外跑
                     */
                    // let poolValue = new BigNumber(closeData.reserve0).times(new BigNumber(token0_price_usd)).times(2);
                    // poolValue = Number(poolValue.toFixed(30));
                    // //更新池子排行榜
                    // await redis.zAdd(RankCacheEnum.POOL_RANK, poolValue, pairInfo.address);
                    // //更新涨跌幅榜
                    // await redis.zAdd(RankCacheEnum.CHG_RANK, kline.change_pct, pairInfo.address);
                    //
                    // //更新投资回报率排行榜  现价/发行价*100%
                    // let roi = 0;
                    // if (!new BigNumber(openPrice).isZero()) {
                    //     roi = Number(new BigNumber(tokenSaveData.current_price_usd).div(openPrice).multipliedBy(100).toFixed(10));
                    // }
                    // await redis.zAdd(RankCacheEnum.ROI_RANK, roi, pairInfo.address);
                    //
                    // let maxSupply = new BigNumber(token.max_supply);
                    // if (maxSupply.isZero() || maxSupply.isNaN()) {//如果空
                    //     maxSupply = await getMaxSupply(token.address, token.decimals, token.total_supply);、、走合约？
                    //     tokenSaveData.max_supply = maxSupply.toFixed(30);
                    // }
                    //
                    // //市值榜  现币价*最大供应量
                    // if (maxSupply.comparedTo(0) > 0) {
                    //     let capValue = maxSupply.multipliedBy(tokenSaveData.current_price_usd);
                    //     capValue = Number(capValue.toFixed(30));
                    //     await redis.zAdd(RankCacheEnum.CAP_RANK, capValue, pairInfo.addres);
                    // }
                    token.current_price_usd = tokenSaveData.current_price_usd;
                    token.price_change = tokenSaveData.price_change;
                    token.publish_time = tokenSaveData.publish_time;
                    token.max_supply = tokenSaveData.max_supply;
                    // await token.update(tokenSaveData);
                    if (token.name && Buffer.from(token.name, "utf8").length > 50) {
                        console.log("name名称超出");
                        token.name = token.name.substring(0, 15);
                    }
                    if (token.symbol && Buffer.from(token.symbol, "utf8").length > 50) {
                        console.log("symbol名称超出");
                        token.symbol = token.symbol.substring(0, 15);
                    }
                    if (token.name) {
                        token.name = token.name.replace(/[\|]|[\n]/g, '-');
                    }
                    if (token.symbol) {
                        token.symbol = token.symbol.replace(/[\|]|[\n]/g, '-');
                    }
                    let row = `${token.address ? token.address : ''}|${token.created_time ? token.created_time : ''}|${token.created_block ? token.created_block : 0}|${token.chain_id ? token.chain_id : 0}|${token.logo_url ? token.logo_url : ''}|${token.name ? token.name : ''}|${token.symbol ? token.symbol : ''}|${token.total_supply ? token.total_supply : ''}|${token.decimals ? token.decimals : 0}|${token.holders ? token.holders : 0}|${token.lp_holder_count ? token.lp_holder_count : 0}|${token.is_open_source ? token.is_open_source : 0}|${token.is_mintable ? token.is_mintable : 0}|${token.current_price_usd ? token.current_price_usd : ''}|${token.price_change ? token.price_change : ''}|${token.pair_address ? token.pair_address : ''}|${token.risk_score ? token.risk_score : 0}|${token.buy_tax ? token.buy_tax : ''}|${token.sell_tax ? token.sell_tax : ''}|${token.is_in_dex ? token.is_in_dex : 0}|${token.is_discard_permission ? token.is_discard_permission : ''}|${token.max_supply ? token.max_supply : ''}|${token.owner_address ? token.owner_address : ''}|${token.status ? token.status : 1}|${token.creator_address ? token.creator_address : ''}|${token.publish_time ? token.publish_time : ''}`;
                    slAveTokensOne(row, token.address);//更新token
                    await redis.hSet(FirstAnalyzeEnum.token_json_hdata, token.address, token);//更新redis
                    tokenCache[token.address] = token;//更新缓存
                }
            }
        } else {
            throw Error("未找到token:" + pairInfo.token_addr);
        }
    }
}

/**
 * 获取最大供应量
 * @param {Number} tokenId
 * @param {String} address
 * @param {Number} decimals
 * @param {String } totalSupply
 * @returns { BigNumber }
 */
async function getCacheMaxSupply(tokenId, address, decimals, totalSupply) {
    /** @type { RedisClient3 } */
    const redis = new RedisClient3();
    const cacheKey = "token_max_supply";
    const hExists = await redis.hExists(cacheKey, tokenId);
    if (hExists) {
        const maxSupply = await redis.hGet(cacheKey, tokenId);
        return new BigNumber(maxSupply);
    }
    const maxSupply = await getMaxSupply(address, decimals, totalSupply);
    await redis.hSet(cacheKey, maxSupply.toFixed(20));
    return maxSupply;
}

/**
 *  写入缓存
 * @param pCache
 * @param tCache
 * @param wCache
 * @returns {Promise<void>}
 */
async function setSyncCache(no, pCache, tCache, wCache) {
    syncBlockNumber = no;
    pairCache = pCache;
    tokenCache = tCache;
    wbnbCache = wCache;
    klineCache = {};
    priceTimeCache = {};
}

module.exports = {
    saveKline,
    setSyncCache
}
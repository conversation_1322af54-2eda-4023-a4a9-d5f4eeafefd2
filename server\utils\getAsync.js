
const mapLimit = require('async/mapLimit');
const {getBlockByNumber,getTransactionByHash,getTransactionRepictByHash} = require('./getData')


async function finished(hash) {
  //console.log("当前hash", hash);
  const transaction = await getTransactionByHash(hash);
  const receipt = await getTransactionRepictByHash(hash);

  
  if(transaction){
    //console.log("得到transaction数据");
  }

  if(receipt){
    //console.log("得到receipt数据");
  }

  let baseInfo = {
      logs: null,
      from: null,
      to: null,
      contractAddress: null,
      transData: null,
  };

  if (transaction) {
      baseInfo = {
          ...baseInfo,
          from: transaction.from,
          to: transaction.to,
          transData: transaction.input,
      };
  }

  if (receipt) {
    const logs = receipt.logs.map(log => ({
      ...log,
      blockNumber: Number(log.blockNumber),
      transactionIndex: Number(log.transactionIndex),
      logIndex: Number(log.logIndex),
    }));

    baseInfo = {
        ...baseInfo,
        logs: logs,
        from: receipt.from || baseInfo.from,
        to: receipt.to || baseInfo.to,
        contractAddress: receipt.contractAddress,
    };
  }

  return baseInfo;
}


async function getDocByBlock(block_no) {
  try {
      //console.log("开始调用")
      block_no = Number(block_no);
      const model_res = await getBlockByNumber(block_no);
      console.log("获取到的区块",block_no);
      if (!model_res) return null;

      let trans_hashs = model_res.transactions;
      //console.log("当前的长度",trans_hashs.length);
      if (trans_hashs.length === 0) return null;

      let logs_hashs = [];
      let address_from = [];
      let address_to = [];
      let contract_address = [];
      let trans_data = [];


      //await getAlltrans(trans_hashs,logs_hashs,address_from,address_to,contract_address,trans_data);

      await new Promise((resolve, reject) => {
        mapLimit(trans_hashs, 100, async (hashData) => {
          try {
            await new Promise(async (resolves, rejects) => {
              try {
                
                const item = await finished(hashData);
               // console.log("当前查询的hash：",hashData['hash'])
                if (item) {
                  logs_hashs.push(item.logs);
                  address_from.push(item.from);
                  address_to.push(item.to);
                  contract_address.push(item.contractAddress);
                  trans_data.push(item.transData);
                }
                resolves();
              }catch(error){
                rejects(error);
              }
            });
            //console.log("100已完成",logs_hashs.length);
          } catch (err) {
            console.error("Error processing transaction:", err);
          }
        }, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      const docs = {
        _id: block_no,
        block_no,
        block_time: model_res.timestamp,
        trans_hashs,
        logs_hashs,
        address_from,
        address_to,
        contract_address,
        trans_data,
        name_renwu: "test_logs",
      };
      console.log("docs结果",docs.trans_hashs.length);
      return docs;
  } catch (error) {
    console.error("Error in startPairs:", error);
  }
}

// async function getAlltrans(trans_hashs,logs_hashs,address_from,address_to,contract_address,trans_data){
//   mapLimit(trans_hashs, 100, async (hashData) => {
//     try {
//       //console.log("当前查询的hash：",hashData['hash'])
//       // const item = await finished(hashData);
//       // if (item) {
//       //   logs_hashs.push(item.logs);
//       //   address_from.push(item.from);
//       //   address_to.push(item.to);
//       //   contract_address.push(item.contractAddress);
//       //   trans_data.push(item.transData);
//       // }
//       await new Promise(async (resolves, rejects) => {
//         try {
//           //console.log("当前查询的hash：",hashData['hash'])
//           const item = await finished(hashData);
//           if (item) {
//             logs_hashs.push(item.logs);
//             address_from.push(item.from);
//             address_to.push(item.to);
//             contract_address.push(item.contractAddress);
//             trans_data.push(item.transData);
//           }
//           resolves();
//         }catch(error){
//           rejects(error);
//         }
//       });
//       //console.log("结果是：",hashData['hash']);
//       console.log("100已完成",logs_hashs.length);
//     }catch(error){
//       resolves();
//     }
//   });
// }

module.exports={
  getDocByBlock
}

const http = require('http');
const {errorToRedis} = require("../redisUtils");
// Constants
const DORIS_HOST = '**************';
const DORIS_DB = 'asdic';
const DORIS_TABLE = 'getPairBase';
const DORIS_USER = 'dev';
const DORIS_PASSWORD = 'dev!sonar';
const DORIS_HTTP_PORT = 48523;//这里是doris的http端口
const errorKey = "getPairBase_error";

function basicAuthHeader(username, password) {
    const toBeEncoded = username + ':' + password;
    return 'Basic ' + Buffer.from(toBeEncoded).toString('base64');
}

const loadUrl = `/api/${DORIS_DB}/${DORIS_TABLE}/_stream_load`;
const options = {
    hostname: DORIS_HOST,
    port: DORIS_HTTP_PORT,
    path: loadUrl,
    method: 'PUT',
    headers: {
        'Expect': '100-continue',
        'Authorization': basicAuthHeader(DORIS_USER, DORIS_PASSWORD),
        'Content-Type': 'text/plain; charset=utf-8',
        'column_separator': '|'
    }
};

function handleRedirect(options, content, blockNumber, followRedirects = 5) {
    //console.log(JSON.stringify(content));
    if (followRedirects <= 0) {
        throw new Error('Too many redirects');
    }
    const req = http.request(options, (res) => {
        if (res.statusCode === 307 && res.headers.location) {
            // 解析新的URL
            const newUrl = new URL(res.headers.location);
            // 创建新的请求选项
            const newOptions = {
                hostname: newUrl.hostname,
                port: newUrl.port || 80,
                path: newUrl.pathname + newUrl.search,
                method: options.method,
                headers: options.headers
            };
            // 重新发送请求
            handleRedirect(newOptions, content, blockNumber, followRedirects - 1);
        } else {

            res.on('data', (chunk) => {
                let res_d=JSON.parse(chunk);
                if (res_d["Status"] != "Success") {
                    let setArray = Array.from(new Set(blockNumber));
                    errorToRedis(errorKey, setArray);
                    console.error("写入错误status:"+chunk);
                }
                //console.log("结果:",loadResult);
            });
            res.on('end', () => {
                
            });
        }
    });
    req.on('error', (error) => {
        let setArray = Array.from(new Set(blockNumber));
        errorToRedis(errorKey, setArray);
        console.error(`请求遇到问题: ${error}`);
    });
    req.write(content);
    req.end();
}

function sendData(content, blockNumber) {
    try {
        handleRedirect(options, content, blockNumber);
    } catch (e) {
    }
}

class BatchProcessor {
    constructor({maxInterval}) {
        this.maxInterval = maxInterval;
        this.buffer = [];
        this.bufferNo = [];
        this.startInterval();
    }
    addData(data, no) {
        this.buffer.push(data);//
        this.bufferNo.push(no);//记录当前所有的block_number
    }
    send() {
        //console.log(this.buffer.length);
        if (this.buffer.length > 0) {
            const dataToSend = this.buffer.join('\n');
            //console.log(dataToSend);
            sendData(dataToSend, this.bufferNo); // 实际发送数据的逻辑
            // 重置缓冲区状态
            this.buffer = [];
            this.bufferNo = [];
        }
    }
    startInterval() {
        this.intervalId = setInterval(() => {
            this.send(); // 定时检查并发送数据
        }, this.maxInterval);
    }

    // 清理资源，停止定时器
    distroyInterval() {
        clearInterval(this.intervalId);
    }
}
const batchProcessor = new BatchProcessor({
    maxInterval: 5000, // 每30秒发送一次数据
});

function slGetPairBase(array, no) {
    array.forEach(item => {
        batchProcessor.addData(item, no);
    });
}

module.exports = {
    slGetPairBase
}
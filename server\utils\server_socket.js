const net = require('net')
var mysocket = [];
const server = net.createServer(socket => {
    try{
        //console.log("开始111");
        mysocket.push(socket);
        // mysocket.on('error',function(){
        //     //console.log("客服端断开");
        // })
        // for(var i = 0;i<mysocket.length;i++){
        socket.on('error',function(){
            for(var i = 0;i<mysocket.length;i++){
                if(mysocket[i]==socket){
                    mysocket.splice(i,1);
                }
            }
            //console.log("客服端断开");
        })
        // }
    }catch(ex){
        //console.log("错误");
    }
})
server.listen(4000);
function getSocket(){
    return mysocket;
}
module.exports={
    getSocket
}


/**
 * 描述: 业务逻辑处理 - 任务相关接口
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/
const { currencyBaiModel, PairModel,queryNotice,lineModel} = require('../model/model');
const { querySql } = require('../database/config');

const axios = require("axios");
// 查询所有白名单的交易对
async function queryAll(req,res) {
  const connect = await querySql();
  var baiModel = currencyBaiModel(connect);
  var currModel = PairModel(connect);
  var allbai = await baiModel.find({is_del:0});
  //console.log(allbai.length);
  var address = [];
  for(var i = 0;i<allbai.length;i++){
    address.push(allbai[i].trans_address);
  }
  var all = await  currModel.find({"trans_address" : { $in :  address}});
  ////console.log(all);
  var bai_arrays = [];
  for(var i = 0;i<all.length;i++){
    if(all[i].flag == 0){
      var bai = {
          name:all[i].name_one,
          address:all[i].address_one,
          trans_address:all[i].trans_address,
          image:all[i].image_one,
          price:all[i].price,
          change:all[i].chg_hours
      }
      bai_arrays.push(bai);
    }else{
      var bai = {
        name:all[i].name_two,
        address:all[i].address_two,
        trans_address:all[i].trans_address,
        image:all[i].image_two,
        price:all[i].price,
        change:all[i].chg_hours
      }
      bai_arrays.push(bai);
    }
  }
  res.json({ 
    code: 200, 
    msg: 'success', 
    data: bai_arrays 
  })
}
  //查询所有公告
async function querysNotice(req,res){
  const connect = await querySql();
  var noticeModel = querysNotice(connect);
  var notices =await noticeModel.find();
  res.json({ 
    code: 200, 
    msg: 'success', 
    data: notices 
  })
}

//删除公告
async function delNotice(req,res){
  let { _id } = req.body;
  const connect = await querySql();
  var noticeModel = queryNotice(connect);
  var notices =await noticeModel.deleteOne({_id:_id})
  res.json({ 
    code: 200, 
    msg: 'success', 
    data: notices 
  })
}
//修改公告内容
async function updateNotice(req,res){
  let { _id,content } = req.body;
  const connect = await querySql();
  var noticeModel = queryNotice(connect);
  var notice =await noticeModel.updateOne({_id:_id},{content,content});

  res.json({ 
    code: 200, 
    msg: 'success', 
    data: null 
  })
}
//添加公告
async function addNotice(req,res){
  let { content } = req.body;
  const connect = await querySql();
  var noticeModel = queryNotice(connect);
  var docs = {
    content:content,
    time:new Date().toString(),
  }
  await noticeModel.create(docs);
  res.json({ 
    code: 200, 
    msg: 'success', 
    data: null 
  })
}
//查询币种基础数据
async function queryCurrency(req,res){
  let { address } = req.body;
  //console.log(address);
  const connect = await querySql();
  var currModel = PairModel(connect);
  var currency = await currModel.find({trans_address:address});
  var name = "";
  if(currency.flag == 0)
  {
    currency.name=currency.name_one;
    currency.address=currency.address_one;
    currency.image=currency.image_one;
    

  }else{
    currency.name=currency.name_two;
    currency.address=currency.address_two;
    currency.image=currency.image_two;
  }
  //查询当前币种所有的交易对
  var all_currencys1 = await currModel.find({address_one:currency.address,flag:0});
  var all_currencys2 = await currModel.find({address_two:currency.address,flag:1});
  var all_currencys = all_currencys1.concat(all_currencys2);
  currency.currencys = all_currencys;
  //查询当前K线数据（1分钟）
  var linesModel = lineModel(connect,currency.name_index);
  var klines = await linesModel.find({data_type:"1分钟"}).sort(data_qu);
  currency.klines = klines;

  res.json({ 
    code: 200, 
    msg: '12', 
    data: currency 
  })
}

//查询K线数据
async function queryKline(req,res){
  let { data_type,name_index } = req.body;
  //console.log(data_type);
  const connect = await querySql();
  var linesModel = lineModel(connect,name_index);
  var klines = await linesModel.find({data_type:data_type}).sort(data_qu);
  res.json({ 
    code: 200, 
    msg: '12', 
    data: klines 
  })
}

//blockchain
async function blockChain(req,res){
    let { num,page } = req.body;

    // let res_data = await axios.get("https://apis.tianapi.com/blockchain/index?key=3ea9712b75acae828102beddaedda9a3&num=" + num+"&page="+page);");
    // let data = res_data.data ;
    // if(data.code == 200){
    //   //将数据存入数据库中
    //   forreach(data.result.newslist,function(index,item){
    //     var docs={
    //       id: item.id,
    //       ctime: item.ctime ,
    //       title: item.title,
    //       description: item.description,
    //       source: item.source,
    //       picUrl: item.picUrl,
    //       url: item.url
    //     }
    //   });
    // }
  res.json({});

}

//查询交易数据

//查询用户持币数据

//查询币种简介

module.exports = {
  queryAll,
  queryCurrency,
  queryKline,
  querysNotice,
  blockChain,
}

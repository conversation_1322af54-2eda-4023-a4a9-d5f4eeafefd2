/**
 * 描述: 业务逻辑处理 - 任务相关接口
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/
const {
    currencyBaiModel,
    PairModel,
    queryNotice,
    lineModel,
    transModel,
    trancaUerModel,
    cunewModel,
    cudealModel,
    cuhandModel,
    cupoolModel,
    cufallModel,
    curiseModel,
    cutransModel,
    cuhotModel,
    userBiModel,
    mobilityModel,
    juhe
  } = require("../model/model");
const { querySql } = require("../database/config");

const {getIo} = require('../utils/socket');

const io = getIo();

async function klineListen(req, res){
    let {data} = req.body;
    //console.log(data);
    //当前数据进行处理后发送给前端
    //1.根据数据得到变化的币种信息，并发送给前端

    //2.根据数据得到变化的K线信息，并发送给前端

    //3.根据数据得到变化的交易信息，并发送给前端

    res.json({
        code: 200,
        msg: "success",
        data: null,
    });
}

async function liquidityListen(req, res){
    let {data} = req.body;
    //console.log(data);
    res.json({
        code: 200,
        msg: "success",
        data: null,
      });
}

async function userTransListen(req, res){
    let {data} = req.body;
    //console.log(data);
    res.json({
        code: 200,
        msg: "success",
        data: null,
      });
}

module.exports = {
    klineListen,liquidityListen,userTransListen
}

const redis = require("../../../database/redis");
const cron = require('node-cron');
const {getImgUrl} = require('../tokenService');
const {hotTop, getTokenBase} = require('../topPairService');
const {
    Main_chips, day_k
} = require('../homeEvaluationService');

/**
 * 定时任务
 */
cron.schedule('*/2 * * * *', function () {
    dataUpdateTaskInit();
    imgUrlTask();
    dataUpdateTask();
});
let lock_priceTask = true;
let PairBaseLock = false;//批次上锁，等待当前批次完成再置为true
let holderLock = false;//批次上锁，等待当前批次完成再置为true
let GetklineWebLock = false;//批次上锁，等待当前批次完成再置为true
let GetklineWebDLock = false;//批次上锁，等待当前批次完成再置为true
let mainChipsLock = true;//批次上锁，等待当前批次完成再置为true
let dayKLock = true;//批次上锁，等待当前批次完成再置为true
let tokenBaseLock = true;//批次上锁，等待当前批次完成再置为true
// dataUpdateTaskInit();
async function dataUpdateTaskInit() {
    try {
        let count = 1000;//批量插入数量
        /**
         * Main_chips初始化
         */
        if (mainChipsLock) {
            mainChipsLock = false;
            let mainChipsCount = [];
            let mainChips = await redis.hKeys("webmagic_api:Main_chips");
            if (mainChips && mainChips.length > 0) {
                console.info("Main_chips初始化" + mainChips.length);
                let key;
                for (let i = 0; i < mainChips.length; i++) {
                    key = mainChips[i];
                    // let objStr = await redis.hGet("webmagic_api:Main_chips",key);
                    if (mainChipsCount.length < count) {
                        mainChipsCount.push(key);
                        continue;
                    }
                    await redis.rPush("webmagic_api:Main_chips_consumer", mainChipsCount);//获取token列表key4295493（注意此表不可以变化否则下标失效）
                    mainChipsCount.length = 0;
                }
                if (mainChipsCount.length > 0) {
                    await redis.rPush("webmagic_api:Main_chips_consumer", mainChipsCount);
                    mainChipsCount.length = 0;
                }
            }

        }

        /**
         * day_k初始化
         */
        if (dayKLock) {
            dayKLock = false;
            let dayKCount = [];
            let dayK = await redis.hKeys("webmagic_api:day_k");
            if (dayK && dayK.length > 0) {
                console.info("day_k初始化" + dayK.length);
                let key;
                for (let i = 0; i < dayK.length; i++) {
                    key = dayK[i];
                    // let objStr = await redis.hGet("webmagic_api:day_k",key);
                    if (dayKCount.length < count) {
                        dayKCount.push(key);
                        continue;
                    }
                    await redis.rPush("webmagic_api:day_k_consumer", dayKCount);//获取token列表key4295493（注意此表不可以变化否则下标失效）
                    dayKCount.length = 0;
                }
                if (dayKCount.length > 0) {
                    await redis.rPush("webmagic_api:day_k_consumer", dayKCount);
                    dayKCount.length = 0;
                }
            }

        }

        /**
         * getTokenBase初始化
         */
        if (tokenBaseLock) {
            tokenBaseLock = false;
            let tokenBaseCount = [];
            let tokenBase = await redis.hKeys("webmagic_api:getTokenBase");
            if (tokenBase && tokenBase.length > 0) {
                console.info("getTokenBase初始化" + tokenBase.length);
                let key;
                for (let i = 0; i < tokenBase.length; i++) {
                    key = tokenBase[i];
                    // let objStr = await redis.hGet("webmagic_api:getTokenBase",key);
                    if (tokenBaseCount.length < count) {
                        tokenBaseCount.push(key);
                        continue;
                    }
                    await redis.rPush("webmagic_api:getTokenBase_consumer", tokenBaseCount);//获取token列表key4295493（注意此表不可以变化否则下标失效）
                    tokenBaseCount.length = 0;
                }
                if (tokenBaseCount.length > 0) {
                    await redis.rPush("webmagic_api:getTokenBase_consumer", tokenBaseCount);
                    tokenBaseCount.length = 0;
                }
            }

        }
    } catch (e) {
        console.error(e)
    }
}

/**
 * 缓存更新任务
 * @returns {Promise<void>}
 */

let dataUpdateTaskLock = true;

async function dataUpdateTask() {
    try {
        if (dataUpdateTaskLock) {
            dataUpdateTaskLock = false;
            let count = 50;//最多一批次的请求数
            let promise = [];
            let time = Date.now();
            /**
             * 更新getPairBase
             * @type {unknown}
             */
            // let PairBase = [];
            // for (let i = 0; i < count; i++) {
            //     let json = await redis.lPop("webmagic_api:getPairBase_consumer");
            //     if (!json) {
            //         PairBaseLock = true//批次上锁，等待当前批次完成再置为true
            //         break;
            //     }
            //     PairBase.push(JSON.parse(json));
            // }
            // if (PairBase && PairBase.length > 0) {
            //     console.info("更新getPairBase:" + PairBase.length);
            //     let key;
            //     for (let i = 0; i < PairBase.length; i++) {
            //         key = PairBase[i]["address"];
            //         promise.push(new Promise((r1, j1) => {
            //             //一个线程就包含(5+此线程pair个数x2)次的访问http数量
            //             let request = {
            //                 query: {
            //                     address: key
            //                 },
            //                 cacheOpen: false
            //             }
            //             let result = {
            //                 json: function (param) {
            //                     if (param.code == 200) {
            //                         r1(param);
            //                     } else {
            //                         j1(param.msg)
            //                     }
            //                 }
            //             }
            //             getPairBase(request, result);
            //             // getPairBaseTask(request, r1)
            //         }));
            //     }
            // }
            /**
             * 更新getHolder()（暂不开）
             * @type {unknown}
             */
            // let holder = [];
            // for (let i = 0; i < count; i++) {
            //     let json = await redis.lPop("webmagic_api:getHolder_consumer");
            //     if (!json) {
            //         holderLock = true;//批次上锁，等待当前批次完成再置为true
            //         break;
            //     }
            //     holder.push(json);
            // }
            // if (holder && holder.length > 0) {
            //     console.info("更新getHolder:" + holder.length);
            //     let key;
            //     for (let i = 0; i < holder.length; i++) {
            //         key = holder[i];
            //         let request = {
            //             query: {
            //                 address: key
            //             }
            //         }
            //         promise.push(new Promise((r1, j1) => {
            //             let result = {
            //                 json: function (param) {
            //                     if (param.code == 200) {
            //                         r1(param);
            //                     } else {
            //                         j1(param.msg)
            //                     }
            //                 }
            //             }
            //             getHolder(request, result, false)
            //         }));
            //     }
            // }
            /**
             * 更新getklineWeb
             * @type {unknown}
             */
            // let klineWeb = [];
            // for (let i = 0; i < count; i++) {
            //     let json = await redis.lPop("webmagic_api:getklineWeb_consumer");
            //     if (!json) {
            //         GetklineWebLock = true;//批次上锁，等待当前批次完成再置为true
            //         break;
            //     }
            //     klineWeb.push(JSON.parse(json));
            // }
            // if (klineWeb && klineWeb.length > 0) {
            //     console.info("更新getklineWeb:" + klineWeb.length);
            //     let key;
            //     for (let i = 0; i < klineWeb.length; i++) {
            //         key = klineWeb[i]["address"];
            //         let str = key.split("_");
            //         promise.push(new Promise((r1, j1) => {
            //             let request = {
            //                 body: {
            //                     pairAddress: str[0],
            //                     period: str[1]
            //                 },
            //                 cacheOpen: false
            //             }
            //             let result = {
            //                 json: function (param) {
            //                     if (param.code == 200) {
            //                         r1(param);
            //                     } else {
            //                         j1(param.msg)
            //                     }
            //                 }
            //             }
            //             getklineWeb(request, result);
            //             // getklineWebTask(request, result);
            //         }));
            //     }
            // }
            /**
             * 更新getklineWebD
             * @type {unknown}
             */
            // let klineWebD = [];
            // for (let i = 0; i < count; i++) {
            //     let json = await redis.lPop("webmagic_api:getklineWebD_consumer");
            //     if (!json) {
            //         GetklineWebDLock = true;//批次上锁，等待当前批次完成再置为true
            //         break;
            //     }
            //     klineWebD.push(JSON.parse(json));
            // }
            // if (klineWebD && klineWebD.length > 0) {
            //     console.info("更新getklineWebD:" + klineWebD.length);
            //     let key;
            //     for (let i = 0; i < klineWebD.length; i++) {
            //         key = klineWebD[i]["address"];
            //         let str = key.split("_");
            //         promise.push(new Promise((r1, j1) => {
            //             let request = {
            //                 body: {
            //                     pairAddress: str[0],
            //                     period: str[1]
            //                 },
            //                 cacheOpen: false
            //             }
            //             let result = {
            //                 json: function (param) {
            //                     if (param.code == 200) {
            //                         r1(param);
            //                     } else {
            //                         j1(param.msg)
            //                     }
            //                 }
            //             }
            //             getklineWebD(request, result);
            //             // getklineWebDTask(request, r1)
            //         }));
            //     }
            // }
            /**
             * 更新hotTop缓存
             * @type {unknown}
             */
            promise.push(new Promise((r1, j1) => {
                console.info("更新池子榜");
                //池子榜
                let request = {
                    query: {
                        id: "2"
                    },
                    cacheOpen: false
                }
                let result = {
                    json: function (param) {
                        if (param.code == 200) {
                            r1(param);
                        } else {
                            j1(param.msg)
                        }
                    }
                }
                hotTop(request, result);
            }));
            promise.push(new Promise((r1, j1) => {
                console.info("更新涨幅榜");
                //涨幅榜
                let request = {
                    query: {
                        id: "3"
                    },
                    cacheOpen: false
                }
                let result = {
                    json: function (param) {
                        if (param.code == 200) {
                            r1(param);
                        } else {
                            j1(param.msg)
                        }
                    }
                }
                hotTop(request, result);
            }));
            promise.push(new Promise((r1, j1) => {
                console.info("更新跌幅榜");
                //跌幅榜
                let request = {
                    query: {
                        id: "4"
                    },
                    cacheOpen: false
                }
                let result = {
                    json: function (param) {
                        if (param.code == 200) {
                            r1(param);
                        } else {
                            j1(param.msg)
                        }
                    }
                }
                hotTop(request, result);
            }));
            promise.push(new Promise((r1, j1) => {
                console.info("更新成交榜");
                //成交榜
                let request = {
                    query: {
                        id: "5"
                    },
                    cacheOpen: false
                }
                let result = {
                    json: function (param) {
                        if (param.code == 200) {
                            r1(param);
                        } else {
                            j1(param.msg)
                        }
                    }
                }
                hotTop(request, result);
            }));
            promise.push(new Promise((r1, j1) => {
                console.info("更新换手榜");
                //换手榜
                let request = {
                    query: {
                        id: "7"
                    },
                    cacheOpen: false
                }
                let result = {
                    json: function (param) {
                        if (param.code == 200) {
                            r1(param);
                        } else {
                            j1(param.msg)
                        }
                    }
                }
                hotTop(request, result);
            }));
            // promise.push(new Promise((r1, j1) => {//暂时屏蔽
            //     //"新币榜"
            //     let request = {
            //         query: {
            //             id: "11"
            //         }
            //     }
            //     hotTopTask(request, r1);
            // }));
            /**
             * 更新main_chips
             * @type {unknown}
             */
            let mainChips = [];
            for (let i = 0; i < count; i++) {
                let json = await redis.lPop("webmagic_api:Main_chips_consumer");
                if (!json) {
                    mainChipsLock = true;//批次上锁，等待当前批次完成再置为true
                    break;
                }
                mainChips.push(json);
            }
            if (mainChips && mainChips.length > 0) {
                console.info("更新main_chips:" + mainChips.length);
                let key;
                for (let i = 0; i < mainChips.length; i++) {
                    key = mainChips[i];
                    promise.push(new Promise((r1, j1) => {
                        let request = {
                            body: {
                                token_address: key
                            },
                            cacheOpen: false
                        }
                        let result = {
                            json: function (param) {
                                if (param.code == 200) {
                                    r1(param);
                                } else {
                                    j1(param.msg)
                                }
                            }
                        }
                        Main_chips(request, result);
                        // Main_chipsTask(request, r1);
                    }));
                }
            }
            /**
             * 更新day_k
             * @type {unknown}
             */
            let dayK = [];
            for (let i = 0; i < count; i++) {
                let json = await redis.lPop("webmagic_api:day_k_consumer");
                if (!json) {
                    dayKLock = true;//批次上锁，等待当前批次完成再置为true
                    break;
                }
                dayK.push(json);
            }
            if (dayK && dayK.length > 0) {
                console.info("更新day_k:" + dayK.length);
                let key;
                for (let i = 0; i < dayK.length; i++) {
                    key = dayK[i];
                    promise.push(new Promise((r1, j1) => {
                        let result = function (param) {
                            if (param.code == 200) {
                                r1(param);
                            } else {
                                j1(param.msg)
                            }
                        }
                        day_k(key, result, false);
                        // day_kTask(key, r1);
                    }));
                }
            }
            /**
             * 更新getTokenBase
             * @type {unknown}
             */
            let TokenBase = [];
            for (let i = 0; i < count; i++) {
                let json = await redis.lPop("webmagic_api:getTokenBase_consumer");
                if (!json) {
                    tokenBaseLock = true;
                    break;
                }
                TokenBase.push(json);
            }
            if (TokenBase && TokenBase.length) {
                console.info("更新getTokenBase:" + TokenBase.length);
                let key;
                for (let i = 0; i < TokenBase.length; i++) {
                    key = TokenBase[i];
                    promise.push(new Promise((r1, j1) => {
                        let result = function (param) {
                            if (param.code == 200) {
                                r1(param);
                            } else {
                                j1(param.msg)
                            }
                        }
                        getTokenBase(key, result, false);
                    }));
                }
            }
            //等待所有任务完成
            await Promise.allSettled(promise).then(obj => {
                console.info("redis缓存更新完成time:%d ms", Date.now() - time);
                dataUpdateTaskLock = true;
            }, e => {
                dataUpdateTaskLock = true;
                console.error("serviceTask:error:" + e);
            });
        }
    } catch (e) {
        console.error(e)
    }
}

async function imgUrlTask() {
    let address;
    let promise = [];
    let time = Date.now();
    try {
        for (let i = 0; i < 50; i++) {
            address = await redis.lPop("webmagic_api:imgUrlTask");
            if (address) {
                promise.push(new Promise((r1, j1) => {
                    getImgUrl(address, function (data) {
                        if (data.code == 200) {
                            // console.info("图片请求成功："+address);
                            r1();
                        } else {
                            redis.lPush("webmagic_api:imgUrlTask", address);
                            j1();
                        }
                    });
                }));//下载图片
            } else {
                break;
            }
        }
        //等待所有任务完成
        console.info("下载图片" + promise.length);
        await Promise.allSettled(promise).then(obj => {
            console.info("下载图片完成time:%d ms", Date.now() - time);
        }, e => {
            console.error("imgUrlTask:error:" + e);
        });
    } catch (e) {
        console.error("imgUrlTask:Error:", e);
        await redis.lPush("webmagic_api:imgUrlTask", address);
    } finally {

    }
}

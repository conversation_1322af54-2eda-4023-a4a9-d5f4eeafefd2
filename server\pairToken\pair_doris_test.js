const { ethers } = require('ethers');
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const { queryProvider } = require('../avi/factory_provider');
const provider = queryProvider();
//const signer = provider.getSigner(); // 获取签名者，可能是钱包或私钥
const { multicall, allAbi } = require("../abi");
const { slAvePairsOne } = require("../utils/doris/avePairsSL");
const { delay, isUsdt, isBusd, formatAddress } = require("../utils/functions");
const { } = require("../utils")
const key = "pair_address_data";
const key_doris = "pair_json_hdata";
async function batchCallContracts() {
    
        try {
            //console.log(number);
           await redis.rPush("pair_address_data","******************************************");
            // let pair_address = await redis.hGet(key_doris,"******************************************")

            // if (pair_address) {
               
            //     console.log(JSONbig.stringify(pair_address))
            //     //await redis.rPush(key_doris,row);
                
            // } else {

            // }


        } catch (e) {
            console.log(e);
         
        }
}

batchCallContracts();
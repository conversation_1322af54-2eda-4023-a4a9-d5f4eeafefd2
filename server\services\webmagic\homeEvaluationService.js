let https = require("https");
let http = require("http");
const querystring = require('querystring');
const userEvaluationModel = require("../../model/mysql/userEvaluationModel");
const {getHeaders} = require("./config/httpHead")
const FirstAnalyzeEnum = require("../../utils/enums/AnalyzeDorisKeyEnum");
const {redis} = require("../../utils/redisUtils");
const {img_url_upload} = require("./task/imgUpload");
const {formatAddress} = require("../../utils/functions");
const {request} = require("https");
const axios = require("axios");
const ave_url = "api.eskegs.com";
function decode(encode_data) {
    return JSON.parse(global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")));
}

async function chips_time(request, result) {
    let params = request.body
    if (!params.token_address) {
        result.json({
            code: 400, msg: 'token_address参数不能为空', data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/stats/top100range?token_id=' + params.token_address + '-bsc',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    let data = []
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let map = {
                    time: [], data: {
                        top10: [], top20: [], top50: [], top100: []
                    }, totalAmount: 0
                };
                let jsonData = JSON.parse(html);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result.json({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['data_type'] == 2) {
                    data = decode(jsonData['encode_data'])
                    for (let i = 0; i < data.length; i++) {
                        let obj = data[i];
                        map.time.push(obj.date);
                        map.data.top10.push(obj.top10_amount);
                        map.data.top20.push(obj.top20_amount);
                        map.data.top50.push(obj.top50_amount);
                        map.data.top100.push(obj.top100_amount);
                        map.totalAmount = obj.total_amount;
                    }
                    result.json({
                        code: 200, msg: "成功！", data: map
                    });
                } else {
                    result.json({
                        code: 200, msg: '排名时间未知！', data: null
                    });
                }
            } catch (e) {
                result.json({
                    code: 500, msg: e, data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500, msg: e, data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500, msg: e, data: null
        })
    });
    req.end();

}

// decode("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");
async function Main_chips(request, result) {
    try {
        let params = request.body
        let cacheOpen = request.cacheOpen;
        if (cacheOpen !== false) {
            cacheOpen = true;
        }
        if (!params.token_address) {
            result.json({
                code: 400, msg: 'token_address参数不能为空', data: null
            })
            return;
        }
        if (cacheOpen) {
            let objStr = await redis.hGet("webmagic_api:Main_chips", params.token_address);
            if (objStr) {
                result.json({
                    code: 200, msg: '请求成功！', data: JSON.parse(objStr)
                });
                return;
            }
        }
        const options = {
            hostname: 'febweb002mail.com',
            path: '/v1api/v3/stats/top100balance?token_id=' + params.token_address + '-bsc&size=100&address=',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let data;
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("Main_chips:认证过期！");
                        result.json({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['data_type'] == 2) {
                        let decoded = decode(jsonData['encode_data'])
                        redis.hSet("webmagic_api:Main_chips", formatAddress(params.token_address), JSON.stringify(decoded));
                        result.json({
                            code: 200, msg: "成功！", data: decoded
                        })
                    } else {
                        result.json({
                            code: 200, msg: '持币量未知！', data: null
                        })
                    }
                } catch (e) {
                    result.json({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                result.json({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', function (e) {
            result.json({
                code: 500, msg: e, data: null
            })
        })
        req.on('timeout', () => {
            console.error('请求超时:Main_chips');
            result.json({
                code: 500,
                msg: null,
                data: null
            })
        });
        req.setTimeout(60000);
        req.end();
    } catch (e) {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    }
}

/**
 * 一天kline
 * @param request
 * @param result
 */
async function day_k(pairAddress, result, cacheOpen) {
    try {
        if (cacheOpen !== false) {
            cacheOpen = true;
        }
        if (!pairAddress) {
            result({
                code: 400, msg: 'address参数不能为空', data: null
            })
            return;
        }
        if (cacheOpen) {
            let objStr = await redis.hGet("webmagic_api:day_k", pairAddress);
            if (objStr) {
                result({
                    code: 200, msg: '请求成功！', data: JSON.parse(objStr)
                });
                return;
            }
        }
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/pairs/' + pairAddress + '-bsc' + '/kline?interval=10080&category=u&count=800',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let data = [];
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("day_k:认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    if (!encode_data || encode_data == "") {
                        console.log("day_k:encode_data为空");
                        result({
                            code: 200, msg: "数据为空！", data: null
                        });
                        return;
                    }
                    let de_data = decode(encode_data)
                    let kline_data = de_data['kline_data'];
                    let item;
                    if (!kline_data) {
                        result({
                            code: 200, msg: "数据为空！", data: null
                        });
                        return;
                    }
                    for (let i = 0; i < kline_data.length; i++) {
                        item = kline_data[i];
                        data.push(item.close);
                        delete item.open;
                        delete item.high;
                        delete item.low;
                        delete item.volume;
                    }
                    redis.hSet("webmagic_api:day_k", formatAddress(pairAddress), JSON.stringify(data));
                    result({
                        code: 200, msg: "请求成功！", data: data
                    })
                } catch (e) {
                    result({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', function (e) {
            result({
                code: 500, msg: e, data: null
            })
        })
        req.on('timeout', () => {
            console.error('请求超时:day_k');
            result({
                code: 500,
                msg: null,
                data: null
            })
        });
        req.setTimeout(60000);
        req.end();
    } catch (e) {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    }
}

async function getNftList(request, result) {
    try {
        let params = request.body
        if (!params.address) {
            result.json({
                code: 400, msg: '地址不能为空', data: null
            })
            return;
        }
        axios.post('http://*************:8001/getNftList',{'address':params.address}).then((res)=>{
            console.log("返回数据",res.data)
            result.json({
                code: 200, msg: '', data: res.data
            })
        })
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

async function getComplexProtocolList(request, result) {
    try {
        let params = request.body
        if (!params.address) {
            result.json({
                code: 400, msg: '地址不能为空', data: null
            })
            return;
        }
        axios.post('http://*************:8001/getComplexProtocolList',{'address':params.address}).then((res)=>{
            console.log("返回数据",res.data)
            result.json({
                code: 200, msg: '', data: res.data
            })
        })
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

async function getHistoryList(request, result) {
    try {
        let params = request.body
        if (!params.address) {
            result.json(null)
            return;
        }
        axios.post('http://*************:8001/getHistoryList',{'address':params.address,'page_count':20}).then((res)=>{
            console.log("返回数据",res.data)
            result.json(res.data);
        })
    } catch (e) {
        result.json(null);
    }
}

async function getBalance(request, result) {
    try {
        let params = request.body
        if (!params.address) {
            result.json({
                code: 400, msg: '地址不能为空', data: null
            })
            return;
        }
        axios.post('http://*************:8001/getBalance',{'address':params.address}).then((res)=>{
            console.log("返回数据",res.data)
            result.json({
                code: 200, msg: '', data: res.data
            })
        })
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}
async function initAddress(request, result) {
    try {
        let params = request.body
        if (!params.address) {
            result.json({
                code: 400, msg: '地址不能为空', data: null
            })
            return;
        }
        if (!params.remark) {
            result.json({
                code: 400, msg: '备注不能为空', data: null
            })
            return;
        }
        axios.post('http://*************:8001/initAddress',{'address':params.address,'remark':params.remark}).then((res)=>{
            console.log("返回数据",res.data)
            result.json({
                code: 200, msg: '', data: res.data
            })
        })
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

async function getCurrency(request, result) {
    try {
        let params = request.body
        if (!params.address) {
            result.json({
                code: 400, msg: '地址不能为空', data: null
            })
            return;
        }

        axios.post('http://*************:8001/getCurrency',{'address':params.address}).then((res)=>{
            console.log("返回数据",res.data)
            result.json({
                code: 200, msg: '', data: res.data
            })
        })
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}
/**
 * 首页数据评测查询
 * @param request
 * @param result
 * @constructor
 */
async function showEvaluation(request, result) {
    try {
        let params = request.body
        if (!params.user_id) {
            result.json({
                code: 400, msg: '用户id参数不能为空', data: null
            })
            return;
        }
        let userEvaluations = await userEvaluationModel().findAll({
            where: {
                user_id: params.user_id
            }
        })
        if (!userEvaluations || userEvaluations.length == 0) {
            result.json({
                code: 200, msg: '未添加首页数据评测代币！', data: null
            })
            return;
        }
        let thread = [];
        userEvaluations.forEach((item) => {
            thread.push(new Promise((r, j) => {
                getPairBase(item.token_address, r, j);
            }));
        })
        await Promise.all(thread).then(obj => {
            result.json({
                code: 200, msg: '请求成功！', data: obj
            })
        }, e => {
            result.json({
                code: 500, msg: e, data: null
            })
        });
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 添加代币评测
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function addEvaluation(request, result) {
    try {
        let params = request.body
        if (!params.chain_name || !params.token_address || !params.user_id) {
            result.json({
                code: 400, msg: '公链名称,合约地址,用户id参数不能为空', data: null
            })
            return;
        }
        params.token_address = formatAddress(params.token_address);
        let json = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", params.token_address);
        if (!json) {
            result.json({
                code: 500, msg: '未收录此代币！', data: null
            });
            return;
        }
        let obj = {
            user_id: params.user_id, token_address: params.token_address, chain_name: params.chain_name
        }
        let userEvaluations = await userEvaluationModel().findOrCreate({
            where: {
                user_id: params.user_id,
                token_address: params.token_address,
                chain_name: params.chain_name
            }, defaults: obj // 用于创建记录的默认值
        });
        // let objs = [{
        //     chain_name: params.chain_name,
        //     token_address: params.token_address,
        //     user_id: params.user_id
        // }];
        // await userEvaluationModel().bulkCreate(objs, {ignoreDuplicates: true});
        if (userEvaluations && userEvaluations.length > 0) {
            result.json({
                code: 200, msg: '成功！', data: null
            })
        } else {
            throw Error("添加失败！");
        }

    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 删除首页评测代币
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function deleteEvaluation(request, result) {
    try {
        let params = request.body
        if (!params.token_address || !params.user_id) {
            result.json({
                code: 400, msg: '合约地址,用户id参数不能为空', data: null
            })
            return;
        }
        let userEvaluations = await userEvaluationModel().destroy({
            where: {
                user_id: params.user_id, token_address: params.token_address
            }
        });
        // let objs = [{
        //     chain_name: params.chain_name,
        //     token_address: params.token_address,
        //     user_id: params.user_id
        // }];
        // await userEvaluationModel().bulkCreate(objs, {ignoreDuplicates: true});
        if (userEvaluations && userEvaluations > 0) {
            result.json({
                code: 200, msg: '成功！', data: null
            })
        } else {
            throw Error("删除失败！");
        }

    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}


/**
 * 获取检测分数
 * @param address
 * @param r
 * @param j
 * @returns {Promise<void>}
 */
async function getRiskScore(address, call) {
// 要发送的数据
    const postData = querystring.stringify({
        address: address, chainId: 56, checkType: 'token',
    });
// 设置请求的选项
    const options = {
        hostname: 'http://127.0.0.1', port: 8080, path: '/api/tool/security', method: 'POST', headers: {
            'Content-Type': 'application/x-www-form-urlencoded', 'Content-Length': postData.length
        }
    };

// 创建请求
    let html = ''
    const req = http.request(options, (res) => {
        res.on('data', (d) => {
            html = html + d;
        });
        res.on('end', () => {
            let jsonData = JSON.parse(html);
            call({
                code: 200, msg: "成功！", data: jsonData
            });
        });
        res.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            });
        });
    });

// 错误处理
    req.on('error', (e) => {
        call({
            code: 500, msg: e, data: null
        });
    });

// 写入数据到请求主体
    req.write(postData);

// 结束请求
    req.end();
}

// getRiskScore('******************************************', function (call) {
//
// });

async function getPairBase(address, r, j) {
    // /v1api/v3/tokens/******************************************-bsc
    let url = '/v1api/v3/tokens/' + address + "-bsc"
    const options = {
        hostname: ave_url, path: url, agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }), headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };
    // let threadPromise = [];
    // let promise = new Promise((resolve, reject) => {
    let html = ''
    let response = {};
    const req = https.get(options, function (res) {
        res.on('data', function (chunk) {
            html += chunk;
        });
        res.on('end', function () {
            try {
                let thread = [];
                let jsonData = JSON.parse(html);
                if (!jsonData['data']) {
                    console.log("encode_data为空");
                    r({
                        code: 200, msg: "数据为空！", data: null
                    });
                    return;
                }
                let data;
                if(typeof jsonData['data'] === 'string'){
                    data = JSON.parse(jsonData['data']);
                }else{
                    data = jsonData['data'];
                }
                let token = {
                    symbol: data['token']['symbol'],
                    currentPriceUsd: data['token']['current_price_usd'],
                    address: data['token']['token'],
                    imgUrl: "/image/" + data['token']['token'] + ".png",
                    pairs: []
                }
                if (data['pairs']) {
                    data['pairs'].forEach((item) => {
                        let pair0 = {};
                        // top_title_k(item.pair, pair0);
                        pair0['symbol'] = token.symbol;//币名称
                        pair0['currentPriceUsd'] = token.currentPriceUsd;//当前价格
                        pair0['currentHolder'] = "-";//持有数
                        pair0['closes'] = [];
                        pair0['holders'] = [];
                        thread.push(new Promise((r2, j2) => {
                            day_k(item.pair, function (res) {
                                if (res.code == 200) {
                                    pair0['closes'] = res.data; //池子收盘集合（day）
                                } else {
                                    console.info("池子获取失败", res.msg);
                                }
                                r2();
                            });
                        }));
                        pair0['riskScore'] = "-";//分数
                        // thread.push(new Promise((r2, j2) => {
                        //     getRiskScore(token.address, function (call) {
                        //         if (call.code == 200) {
                        //             pair0['riskScore'] = call.data; //池子收盘集合（day）
                        //             r2();
                        //         } else {
                        //             j2(call.msg);
                        //         }
                        //     });
                        // }));
                        pair0['high24h'] = item.high_u;//24H高
                        if (item.target_token.includes(item.token0_address)) {
                            pair0['reserve0'] = item.reserve0;//储备量对
                            pair0['reserve1'] = item.reserve1;//储备量对
                            pair0['symbol0_1'] = item.token0_symbol + "/" + item.token1_symbol;//池子对名称
                        }else{
                            pair0['reserve0'] = item.reserve1;//储备量对
                            pair0['reserve1'] = item.reserve0;//储备量对
                            pair0['symbol0_1'] = item.token1_symbol + "/" + item.token0_symbol;//池子对名称
                        }

                        pair0['circulation'] = "-";//流通
                        pair0['circulation_100'] = "-";//流通百分比
                        token.pairs.push(pair0);
                    });
                }
                response['token'] = token;
                // thread.push(new Promise((r2, j2) => {
                //     Main_chips(token.address, function (res) {
                //         if (res.code == 200) {
                //             if (res.data) {
                //                 response['token']['holders'] = res.data;//池子收盘集合（day）
                //                 r2();
                //             }
                //         } else {
                //             console.info("获取持币人失败", res.msg);
                //             j2(res.msg);
                //         }
                //     });
                // }));
                thread.push(new Promise((r2, j2) => {
                    let img_url = data['token']['logo_url'];
                    let imgFilePath = "./image/" + data['token']['token'] + ".png";
                    img_url_upload(img_url, imgFilePath);
                    r2();
                }));
                Promise.all(thread).then(obj => {
                    r(response);
                }, e => {
                    j(e);
                });
            } catch (e) {
                j(e);
            }
        });
        res.on('error', function (e) {
            j(e);
        });
    });
    req.on('error', function (e) {
        j(e);
    });
    req.end();
}

module.exports = {
    addEvaluation, showEvaluation, getNftList,getComplexProtocolList,getHistoryList,getBalance,initAddress,getCurrency, deleteEvaluation, Main_chips, day_k
}

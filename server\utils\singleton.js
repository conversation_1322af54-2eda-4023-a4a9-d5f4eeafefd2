
/**
 * 创建或返回一个类的单例实例
 * @template T
 * @param {new (...args: any[]) => T} className - 类的构造函数
 * @returns { T } 类的单例实例
 */
function singleton(className){
    let instance;
    return new Proxy(className, {
        construct(target, args) {
            if (!instance) {
                instance = Reflect.construct(target, args);
            }
            return instance;
        }
    });
}

module.exports = singleton
package com.blockchain.server.common.constants;

/**
 * 应用程序常量定义
 * 
 * 包含系统中使用的各种常量，包括：
 * - HTTP状态码
 * - 缓存键前缀
 * - 业务常量
 * - 区块链相关常量
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public final class AppConstants {

    private AppConstants() {
        // 防止实例化
    }

    /**
     * HTTP响应状态码
     */
    public static final class ResponseCode {
        public static final int SUCCESS = 200;
        public static final int BAD_REQUEST = 400;
        public static final int UNAUTHORIZED = 401;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int INTERNAL_SERVER_ERROR = 500;
    }

    /**
     * 响应消息
     */
    public static final class ResponseMessage {
        public static final String SUCCESS = "请求成功";
        public static final String BAD_REQUEST = "请求参数错误";
        public static final String UNAUTHORIZED = "未授权访问";
        public static final String FORBIDDEN = "访问被禁止";
        public static final String NOT_FOUND = "资源不存在";
        public static final String INTERNAL_SERVER_ERROR = "服务器内部错误";
        public static final String TOKEN_EXPIRED = "token失效，请重新登录";
    }

    /**
     * 缓存键前缀
     */
    public static final class CacheKey {
        public static final String USER_PREFIX = "user:";
        public static final String TOKEN_PREFIX = "token:";
        public static final String PAIR_PREFIX = "pair:";
        public static final String KLINE_PREFIX = "kline:";
        public static final String HOLDER_PREFIX = "holder:";
        public static final String TRADE_PREFIX = "trade:";
        public static final String HTTP_HEADER_PREFIX = "webmagic_api:httpHead:";
        public static final String MAIN_CHIPS_PREFIX = "webmagic_api:Main_chips:";
    }

    /**
     * 区块链相关常量
     */
    public static final class Blockchain {
        // BSC链ID
        public static final long BSC_CHAIN_ID = 56L;
        
        // 合约地址
        public static final String PANCAKE_FACTORY = "0xca143ce32fe78f1f7019d7d551a6402fc5350c73";
        public static final String PANCAKE_FACTORY_V3 = "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865";
        public static final String PANCAKE_ROUTER = "0x10ed43c718714eb63d5aa57b78b54704e256024e";
        public static final String PANCAKE_SMART_ROUTER = "0x13f4ea83d0bd40e75c8222255bc855a974568dd4";
        
        // Uniswap合约
        public static final String UNISWAP_FACTORY_V3 = "0xdB1d10011AD0Ff90774D0C6Bb92e5C5c8b4461F7";
        public static final String UNISWAP_ROUTER_V3 = "0x7b8A01B39D58278b5DE7e48c8449c9f4F5170613";
        
        // 代币地址
        public static final String USDC_ADDRESS = "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d";
        public static final String TUSD_ADDRESS = "0x40af3827f39d0eacbf4a168f8d4ee67c121d11c9";
        public static final String USDT_ADDRESS = "0x55d398326f99059ff775485246999027b3197955";
        public static final String BNB_ADDRESS = "0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c";
        public static final String BUSD_ADDRESS = "0xe9e7cea3dedca5984780bafc599bd69add087d56";
        
        // 交易对
        public static final String BNB_BUSD_POOL = "0x58F876857a02D6762E0101bb5C46A8c1ED44Dc16";
        
        // VIP会员合约
        public static final String BUY_VIP_ADDRESS = "0x90253B545d649268979A44CE09095155a90dfc2b";
    }

    /**
     * 业务常量
     */
    public static final class Business {
        // 用户相关
        public static final int DEFAULT_VIP_FLAG = 0;
        public static final int VIP_FLAG = 1;
        public static final int DEFAULT_LOGIN_NUM = 0;
        public static final int DEFAULT_DEL_FLAG = 0;
        public static final int DELETED_FLAG = 1;
        
        // 分页相关
        public static final int DEFAULT_PAGE_SIZE = 20;
        public static final int MAX_PAGE_SIZE = 100;
        public static final int DEFAULT_PAGE_NUM = 1;
        
        // 交易类型
        public static final String TRADE_TYPE_BUY = "买入";
        public static final String TRADE_TYPE_SELL = "卖出";
        
        // 流动性类型
        public static final String LIQUIDITY_TYPE_ADD = "添加";
        public static final String LIQUIDITY_TYPE_REMOVE = "移除";
    }

    /**
     * 时间相关常量
     */
    public static final class Time {
        public static final long SECOND_MILLIS = 1000L;
        public static final long MINUTE_MILLIS = 60 * SECOND_MILLIS;
        public static final long HOUR_MILLIS = 60 * MINUTE_MILLIS;
        public static final long DAY_MILLIS = 24 * HOUR_MILLIS;
        public static final long WEEK_MILLIS = 7 * DAY_MILLIS;
        
        // 缓存过期时间（秒）
        public static final long DEFAULT_CACHE_TTL = 600; // 10分钟
        public static final long USER_CACHE_TTL = 1800; // 30分钟
        public static final long TOKEN_CACHE_TTL = 3600; // 1小时
        public static final long KLINE_CACHE_TTL = 300; // 5分钟
        public static final long PAIR_CACHE_TTL = 1800; // 30分钟
    }

    /**
     * 外部API相关常量
     */
    public static final class ExternalApi {
        public static final String AVE_API_HOST = "api.eskegs.com";
        public static final String AVE_API_BASE_URL = "https://api.aveapi.com";
        public static final String MARSUN_API_BASE_URL = "https://marsun002.com";
        public static final String FEBWEB_API_HOST = "febweb002mail.com";
        
        // 请求超时时间（毫秒）
        public static final int DEFAULT_TIMEOUT = 60000;
        public static final int CONNECT_TIMEOUT = 30000;
        public static final int READ_TIMEOUT = 60000;
    }

    /**
     * 文件相关常量
     */
    public static final class File {
        public static final String IMAGE_PATH = "/image";
        public static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
        public static final String[] ALLOWED_IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "webp"};
    }

    /**
     * 正则表达式常量
     */
    public static final class Regex {
        public static final String EMAIL = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        public static final String PHONE = "^1[3-9]\\d{9}$";
        public static final String USERNAME = "^[a-zA-Z0-9_]{4,20}$";
        public static final String PASSWORD = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$";
        public static final String ETH_ADDRESS = "^0x[a-fA-F0-9]{40}$";
        public static final String TRANSACTION_HASH = "^0x[a-fA-F0-9]{64}$";
    }

    /**
     * 数据库相关常量
     */
    public static final class Database {
        // MongoDB集合名称
        public static final String COLLECTION_BLOCKS = "blockbasalnew";
        public static final String COLLECTION_TRANSACTIONS = "transactions";
        public static final String COLLECTION_PAIRS = "pairs";
        public static final String COLLECTION_TOKENS = "tokens";
        public static final String COLLECTION_USERS = "users";
        public static final String COLLECTION_COLLECTS = "collects";
        
        // MySQL表名前缀
        public static final String TABLE_PREFIX_AVI = "avi_";
        public static final String TABLE_PREFIX_BASE = "base_";
    }

    /**
     * 任务调度相关常量
     */
    public static final class Scheduler {
        public static final String MONITOR_TASK_GROUP = "monitor";
        public static final String SYNC_TASK_GROUP = "sync";
        public static final String ANALYSIS_TASK_GROUP = "analysis";
        
        // Cron表达式
        public static final String EVERY_MINUTE = "0 * * * * ?";
        public static final String EVERY_5_MINUTES = "0 */5 * * * ?";
        public static final String EVERY_10_MINUTES = "0 */10 * * * ?";
        public static final String EVERY_HOUR = "0 0 * * * ?";
        public static final String EVERY_DAY = "0 0 0 * * ?";
    }

    /**
     * 消息队列相关常量
     */
    public static final class Queue {
        public static final String TRADE_PARSE_QUEUE = "trade:parse";
        public static final String LIQUIDITY_PARSE_QUEUE = "liquidity:parse";
        public static final String TRANSFER_PARSE_QUEUE = "transfer:parse";
        public static final String BLOCK_PARSE_QUEUE = "block:parse";
        public static final String NOTIFICATION_QUEUE = "notification";
    }
}

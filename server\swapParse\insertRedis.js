const { ethers } = require('ethers');
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const { queryGuiProvider } = require('../avi/factory_provider');
const provider = queryGuiProvider();
const { delay } = require('../utils/functions');
const key_listen = "swap-listen_ok";
const key_now = "block_swap_ase_lisinit";
const key_all = "block_swap_list";
async function batchCallContracts() {

    while (true) {
        try {
            let block_listen = await redis.get(key_listen);

            let block_now = await redis.get(key_now);
            if (!block_now) {
                block_now = 6831287;
            }
            //block_now = 6831287;
            if (Number(block_listen) == Number(block_now)) {
                await delay(5000);
            } else {
                let number_ok = Number(block_listen) + 1;
                for (let index = Number(block_now); index < number_ok; index += 10) {
                    let blocks = '';
                    let number = index + 10;
                    if (number > Number(block_listen) + 1) {
                        
                        blocks = index + ',' + number_ok;
                    }else{
                        blocks = index + ',' + number;
                    }
                    //for (let indexi = index; indexi < index+100; indexi++) {
                    //}
                    await redis.rPush(key_all, blocks);
                }
                await redis.set(key_now, Number(block_listen));
            }
            console.log("完成！！！"+block_listen);
            await delay(100);
        } catch (ex) {
            console.log(ex);
        }
    }




}

batchCallContracts();
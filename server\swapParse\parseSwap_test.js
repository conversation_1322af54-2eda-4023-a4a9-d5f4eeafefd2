
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis_in");
const {getTokenAAndBPriceByToken} = require("../utils/klineUtils");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const {dbSwapQuery, dbInsertSwap} = require('../model/mysql/AviBaseSwapModel');
const {slAveTrade} = require('../utils/doris/aveTradeSL')
const key_all = "block_swap_list";
const { formatUnits } = require("ethers");
const { default: BigNumber } = require('bignumber.js');
const mapLimit = require('async/mapLimit');
async function startSwaps() {
    try{
        
        let rows = [];

        let row = `******************************************|6831305|Sat Apr 24 2021 01:55:26 GMT+0800 (China Standard Time)|0x36aeddc43dc148191af1b3bbc35a2a0a6626f6fd13f450ea7ae394ecdd94b89f|356|******************************************|******************************************|buy|******************************************|******************************************|10.0|18.33706174282198|0.513073529099696274|356.57299086421574|183.37061742821982|10.0|******************************************`;
        //console.log(row);
        rows.push(row);

        slAveTrade(rows,1);
        console.log('结束查询',Date.now())

    }catch(ex){
        console.log(ex);
    }
    
    

}

startSwaps();

module.exports={
    startSwaps
}
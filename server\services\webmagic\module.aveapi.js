const api_aveapi_com = "https://api.aveapi.com";
const marsun002_com = "https://marsun002.com";

const get_balance = async (headers, address, chain) => {
  let params = new URLSearchParams();
  params.append("address", address);
  params.append("chain", chain);
  let url =
    `${api_aveapi_com}/v1api/v3/users/balance/token?${params.toString()}`;
  return await fetch(url, {
    headers,
  }).then((res) => res.json());
};

const get_nft_list = async (headers, address, chain) => {
  let params = new URLSearchParams();
  params.append("id", address);
  params.append("chain_id", chain);
  let url =
    `${marsun002_com}/v1api/debank/v1/user/nft_list?${params.toString()}`;
  return await fetch(url, {
    headers,
  }).then((res) => res.json());
};

const get_complex_protocol_list = async (headers, address, chain) => {
  let params = new URLSearchParams();
  params.append("id", address);
  params.append("chain_id", chain);
  let url =
    `${marsun002_com}/v1api/debank/v1/user/complex_protocol_list?${params.toString()}`;
  return await fetch(url, {
    headers,
  }).then((res) => res.json());
};

const get_history_list = async (
  headers,
  address,
  chain,
  page_count,
  start_time,
  token_id,
  transaction,
) => {
  let params = new URLSearchParams();
  params.append("id", address);
  params.append("chain_id", chain);
  params.append("page_count", page_count);
  params.append("start_time", start_time);
  params.append("token_id", token_id);
  params.append("transaction", transaction);
  let url =
    `${marsun002_com}/v1api/debank/v1/user/history_list?${params.toString()}`;
  return await fetch(url, {
    headers,
  }).then((res) => res.json());
};

module.exports = {
  get_balance,
  get_nft_list,
  get_complex_protocol_list,
  get_history_list,
};

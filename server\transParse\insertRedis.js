const {ethers} = require('ethers');
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
//const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const {queryGuiProvider} = require('../avi/factory_provider');
const provider = queryGuiProvider();
const {delay} = require('../utils/functions');
const FirstAnalyzeEnum = require("../utils/enums/AnalyzeDorisKeyEnum");
const transfer_listen_ok = "transfer-listen_ok";
const key_now = "block_trans_ase_lisinit";
const key_all = "block_trans_list";

async function batchCallContracts() {
    while (true) {
        try {
            let no_min = await redis.hGet(FirstAnalyzeEnum.priceTime_lock, "parseTrans");//获取redis上锁的值
            let no_max = await redis.get(FirstAnalyzeEnum.priceTime_start);//获取redis最大缓存值
            if (!no_min || !no_max) {
                console.error("请先初始化redis缓存");
                break;
            }
            no_min = Number(no_min.split("_")[0]);
            no_max = Number(no_max.split("_")[0]);
            let transferListenOk = await redis.get(transfer_listen_ok);
            let priceTimeSLOk = await redis.get("priceTimeSL_ok");
            if (priceTimeSLOk) {
                priceTimeSLOk = priceTimeSLOk.split("_")[0];
            } else {
                priceTimeSLOk = transferListenOk;
            }
            let block_listen = Math.min(Number(transferListenOk), Number(priceTimeSLOk));//(取两者最小值)934850,934860
            if (block_listen > no_max) {//如果最新值超出范围则赋值最大redis缓存位置
                block_listen = no_max;
            }
            let block_now = await redis.get(key_now);
            if (!block_now) {//如果开始位置没有则默认开始位置
                block_now = no_min;
            }

            //block_now = 6831287;
            if (Number(block_listen) == Number(block_now)) {
                console.log("等待数据.." + block_listen);
                await delay(5000);
                continue;
            } else {
                console.log("等待30s,start:" + block_listen + ",end:" + block_now);
                await delay(30000);//等待30s确保已经插入
                let now_block;
                let number_ok = Number(block_listen);
                for (let index = Number(block_now); index <= number_ok; index += 100) {
                    let blocks = '';
                    let number = index + 100;
                    if (number > number_ok) {
                        blocks = index + ',' + number_ok;
                        now_block = number_ok;
                    } else {
                        blocks = index + ',' + number;
                        now_block = number;
                    }
                    await redis.rPush(key_all, blocks);
                }
                if (now_block) {
                    await redis.set(key_now, now_block);
                    console.log("完成！！！" + block_listen);
                }
            }
        } catch (ex) {
            console.log(ex);
        }
    }
}

batchCallContracts();
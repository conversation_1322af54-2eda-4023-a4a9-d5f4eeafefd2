
const redis = require("../database/redis");

async function batchCallContracts() {
    while(true){
        let keys = await redis.lPop("pair_address_data_c");
        if(keys){

        }else{
            break;
        }
        //将token存入到redis
        await redis.rPush("pair_address_data",keys);
        await redis.rPush("pair_address_data_o",keys);
    }
    console.log("插入完成");
}

batchCallContracts();
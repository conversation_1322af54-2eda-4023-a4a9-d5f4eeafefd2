
const RedisClient = require("../database/redis/index1");
const constant = require("../utils/constant");
const StableTokenModel = require("../model/mysql/StableTokenModel");

async function getStableTokens(){

    /** @type { RedisClient } */
    const redis = new RedisClient();
    const exists = await redis.exists(constant.STABLE_TOKEN_KEY);
    let row = false;
    if(exists){
        row = await redis.get(constant.STABLE_TOKEN_KEY).catch( e => {
            console.error(constant.STABLE_TOKEN_KEY+"获取失败",e.message);
        });
        if(row) row = JSON.parse(row);
    }else{
        row = await StableTokenModel().findAll();
        if(row){
            await redis.set(constant.STABLE_TOKEN_KEY,row).catch( e => {
                console.error(constant.STABLE_TOKEN_KEY+"设置失败",e.message);
            });
        }else{
            console.log("没有查询到数据吗");
        }
    }
    return row;
}


module.exports = {
    getStableTokens,
}
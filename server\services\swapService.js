
const RedisClient = require("../database/redis/index3");
const constant = require("../utils/constant");
const SwapModel = require("../model/mysql/SwapModel");
const PairService = require("../services/pairService");
const { default: BigNumber } = require('bignumber.js');
const PairModel = require('../model/mysql/PairModel');
const TradeModel = require('../model/mysql/TradeModel');


/**
 * 
 * @param { String } address 
 * @returns { object | SwapModel } 
 */
async function getSwapByFactory(address){
    /** @type { RedisClient } */
    const redis = new RedisClient();
    const exists = await redis.hExists(constant.SWAP_CACHE_KEY, address);
    let row = false;
    if(exists){
        row = await redis.hGet(constant.SWAP_CACHE_KEY, address).catch(e => {
            console.error(constant.SWAP_CACHE_KEY+"获取失败",e.message);
        });
        if(row) row = JSON.parse(row);
    }else{
        row = await SwapModel().findOne({
            where: {factory_address: address, status: 1}
        });
        if(row){
            await redis.hSet(constant.SWAP_CACHE_KEY,address,row).catch( e => {
                console.error(constant.SWAP_CACHE_KEY+"设置失败",e.message);
            });
        }
    }
    return row;
}

module.exports = {
    getSwapByFactory,
}
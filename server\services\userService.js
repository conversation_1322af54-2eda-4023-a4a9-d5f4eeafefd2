/**
 * 描述: 业务逻辑处理 - 用户相关接口
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/
const {
    PairModel,
    collectModel,
    userModel,
  } = require("../model/model");
  const { 
    PRIVATE_KEY, 
    JWT_EXPIRED 
  } = require('../utils/constant');
  
  const md5 = require('../utils/md5');
  const jwt = require('jsonwebtoken');
  const boom = require('boom');
  const { body, validationResult } = require('express-validator');
  const { decode } = require('../utils/user-jwt');
  const { querySql } = require("../database/config");
  const connect = await querySql();
  const moment =require("moment");
  const { size } = require("lodash");
  //注册
  async function register(req, res ,next){

    const err = validationResult(req);
    // 如果验证错误，empty不为空
    if (!err.isEmpty()) {
      // 获取错误信息
      const [{ msg }] = err.errors;
      // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回 
      next(boom.badRequest(msg));
    } else {
      let { username,password,nickname } = req.body;
      var user_model = userModel(connect);
      var res_user = await user_model.findOne({username:username,del_flag:0});
      if(res_user == null || res_user == undefined){
        var doc_user = {
          address:"",
          login_time:new Date(),
          username:username,
          password:md5(password),
          vip_flag:0,
          nickname:nickname,
          login_num:0,
          del_flag:0
        }
        await user_model.create(doc_user);
        const token = jwt.sign(
          // payload：签发的 token 里面要包含的一些数据。
          { username },
          // 私钥
          PRIVATE_KEY,
          // 设置过期时间
          { expiresIn: JWT_EXPIRED }
        )
        res.json({
          code: 200,
          msg: "注册成功",
          data: {
            token:token,
            user:{
              username:username,
              nickname:nickname,
              vip_flag:0,
              address:"",
            }
          },
        });
      }else{
        res.json({
          code: 500,
          msg: "该账号已经存在",
          data: null,
        });
      }
    }
  }
  //授权
  
  //注销账号
  async function checkoutuser(req, res ,next){
    const err = validationResult(req);
    // 如果验证错误，empty不为空
    if (!err.isEmpty()) {
      // 获取错误信息
      const [{ msg }] = err.errors;
      // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回 
      next(boom.badRequest(msg));
    } else {
      var username  = decode(req);
      console.log(username);
      var user_model = userModel(connect);
      var res_user = await user_model.findOne({username:username,del_flag:0});
      res_user.del_flag = 1;
      await user_model.updateOne({_id:res_user._id},res_user);
      res.json({
        code: 200,
        msg: "注销成功",
        data: username,
      })
    }
  }
  //登录
  async function login(req, res ,next){
    const err = validationResult(req);
    // 如果验证错误，empty不为空
    if (!err.isEmpty()) {
      // 获取错误信息
      const [{ msg }] = err.errors;
      // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回 
      next(boom.badRequest(msg));
    } else {
      let { username,password } = req.body;
      var user_model = userModel(connect);
      
      var res_user = await user_model.findOne({username:username,del_flag:0,password:md5(password)},{
        address:1,
        username:1,
        vip_flag:1,
        nickname:1,
      });
      if(res_user == null || res_user == undefined){
  
        res.json({
          code: 500,
          msg: "账号或者密码错误",
          data:null,
        });
      }else{
        const token = jwt.sign(
          // payload：签发的 token 里面要包含的一些数据。
          { username },
          // 私钥
          PRIVATE_KEY,
          // 设置过期时间
          { expiresIn: JWT_EXPIRED }
        )
        res.json({
          code: 200,
          msg: "登录成功",
          data: 
          {
            token:token,
            user:res_user
          },
        });
      }
    }
  }
  
  //根据用户查询收藏列表
  async function queryCollect(req, res ,next){
    const err = validationResult(req);
    // 如果验证错误，empty不为空
    if (!err.isEmpty()) {
      // 获取错误信息
      const [{ msg }] = err.errors;
      // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回 
      next(boom.badRequest(msg));
    } else {
      var username  = decode(req);
      console.log(username);
      var collect_model = collectModel(connect);
      var collects = collect_model.find({username:username,name_index:name_index});
      res.json({
        code:200,
        msg:"success",
        data:collects
      })
    }
  }
  //收藏币种（取消收藏）
  async function collect(req, res ,next){
    const err = validationResult(req);
    // 如果验证错误，empty不为空
    if (!err.isEmpty()) {
      // 获取错误信息
      const [{ msg }] = err.errors;
      // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回 
      next(boom.badRequest(msg));
    } else {
      var username  = decode(req);
      console.log(username);
      let {name_index} = req.body;
      var collect_model = collectModel(connect);
      var collect = collect_model.findOne({username:username,name_index:name_index});
      var time = moment().valueOf();
      if(collect == null || collect == undefined){
        var docs = {
            user:username,
            name_index:name_index,
            time:time
        }
        await collect_model.create(docs);
      }else{
        await collect_model.deleteOne({_id:collect._id});
      }
      res.json({
        code:200,
        msg:"success",
        data:null
      })
    }
  }

module.exports = {
    register,
    checkoutuser,
    collect,
    login,
    queryCollect
}

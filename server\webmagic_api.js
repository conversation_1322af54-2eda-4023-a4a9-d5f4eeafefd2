/**
 * 描述: 入口文件
 * 作者: <PERSON>
 * 日期: 2020-06-12
 */
const cors = require('cors'); // 引入cors模块
const bodyParser = require('body-parser'); // 引入body-parser模块
const express = require('express'); // 引入express模块
require('express-async-errors');//异常捕获破解包
const routes = require('./webmagic'); //导入自定义路由文件，创建模块化路由
const app = express();
const os = require("os");
const clusterWorkerSize = os.cpus().length;//获取cpu数量
const cluster = require("cluster");//cpu集群工具
const v8=require('v8');
function localhost_Listen() {
    app.use(bodyParser.json()); // 解析json数据格式
    app.use(bodyParser.urlencoded({extended: true})); // 解析
// form表单提交的数据application/x-www-form-urlencoded
    app.use(cors()); // 注入cors模块解决跨域
// 静态资源路径，无需进行 JWT 认证
    app.use('/image', express.static('image'));
    app.use('/', routes);
// 错误处理中间件
    app.use(function (err, req, res, next) {
        if (err.status === 401) {
            console.log("401异常");
            return res.json({code: 401, msg: err.message,data: null});
        }
        if (err.status === 403) {
            console.log("403异常");
            return res.json({code: 403, msg:err.message,data: null});
        }
        if (err.status === 404) {
            console.log("404异常");
            return res.json({code: 404, msg: err.message,data: null});
        }
        if (err.message.indexOf("BadRequestError") !== -1) {
            return res.json({code: 400, msg: err.message,data: null});
        }
        return res.json({code: 500, msg: err.message,data: null});
    });
    process.on('uncaughtException', function(err) {
        console.log(`${process.pid}未捕获异常: ${err.message}`);
    });
    app.listen(8088, () => { // 监听8088端口
        console.log("Node 堆内存限制:", v8.getHeapStatistics().heap_size_limit / (1024 * 1024), "MB");
        console.log(`${process.pid}服务已启动 http://localhost:8088`);
    });
}


//cpu多核启动
if (clusterWorkerSize > 1) {
    if (cluster.isMaster) {//主进程
        // require('./module/BinanceConnector'); // 引入Ticker24hr模块
        // require('./services/webmagic/task/serviceTask'); //定时任务
        // require('./services/webmagic/task/MonitorTask'); //定时任务
        // require('./services/webmagic/task/TokenAndPairTask'); //定时任务
        for (let i=0; i < clusterWorkerSize; i++) {
            cluster.fork();
        }
        cluster.on("exit", function(worker) {
            console.log("工作线程：", worker.id, " has exitted.")
        })
    } else {//子进程
        localhost_Listen();
    }
} else {
    localhost_Listen();
}

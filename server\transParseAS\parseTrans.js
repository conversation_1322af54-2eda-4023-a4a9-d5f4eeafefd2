
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis_in");
const { getTokenAAndBPriceByToken } = require("../utils/klineUtils");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const { dbTransferQuery } = require('../model/mysql/AviBaseTransferModel');
//const {slAveTrade} = require('../utils/doris/aveTradeSL')
const {slAveTransfer} = require('../utils/doris/aveTransferSL')
const {slAveTransferUser} = require('../utils/doris/aveTransferUserSL')
const key_all = "block_trans_list_new";
const { formatUnits } = require("ethers");
const { default: BigNumber } = require('bignumber.js');
const mapLimit = require('async/mapLimit');
const { StringToTimeStamp, formatTimestamp } = require("../utils/DateUtils");
async function startSwaps() {
    try {
        while (true) {
            let block_listen = await redis.lPop(key_all);
            //await redis.lPush(key_all,block_listen);
            if (!block_listen) {
                continue;
            }

            let block_list = block_listen.split(',');
            let start  = Date.now()
            console.log('开始查询', start)
            const transferList = await dbTransferQuery([{
                type: ">=",
                column: "block_number",
                value: Number(block_list[0])
            }, {
                type: "<",
                column: "block_number",
                value: Number(block_list[1])
            }]);
            if (transferList.length > 0) {
                let insertList = [];
                let holdArrList = [];
                let holdMoneyList = {};
                let threadp = [];
                for (let i = 0; i < transferList.length; i++) {
                    if (!transferList[i].quantity) {
                        continue;
                    }
                    threadp.push(new Promise(async (resolve, reject) => {
                        try {
                            let token_addr = JSONbig.parse(await redis.hGet("token_json_hdata", transferList[i].token_address));
                            if (token_addr) {
                                if (token_addr.pair_address) {
                                    let pair_addr = JSONbig.parse(await redis.hGet("pair_qualified_table", token_addr.pair_address));
                                    if (pair_addr) {
                                        try {
                                            let price = 0;//(await getTokenAAndBPrice(pair_addr, transferList[i].block_number, transferList[i].log_index)).priceA;
                                            price = await getTokenAAndBPriceByToken(pair_addr,transferList[i].block_time,transferList[i].log_index);
                                            if(price == undefined){
                                                price = 0;
                                            }
                                            let info = {
                                                from_address: transferList[i].from_address,
                                                to_address: transferList[i].to_address,
                                                transfer_time: formatTimestamp(transferList[i].block_time),
                                                token_address: transferList[i].token_address,
                                                wallet_from: transferList[i].wallet_from,
                                                wallet_to: transferList[i].wallet_to,
                                                trans_hash: transferList[i].trans_hash,
                                                log_index: transferList[i].log_index,
                                                quantity: transferList[i].quantity,
                                                block_number: transferList[i].block_number,
                                                token_price: price,//需要算
                                                amount_usd: 0,//通过token_price*quantity得到金额
                                            }

                                            //获取当前转账数量
                                            let quantity = new BigNumber(formatUnits(transferList[i].quantity, token_addr.decimals))
                                            //得到转账金额
                                            info.amount_usd = (price * Number(quantity)).toString();


                                            /**
                                             * form的钱包地址
                                             * @type {string}
                                             */

                                            let from_str = `${transferList[i].block_number}_${transferList[i].token_address}_${transferList[i].wallet_from}`;
                                            if (holdMoneyList[from_str]) {
                                                holdMoneyList[from_str].current_price += "," + price.toString() + "_" + transferList[i].log_index;
                                            } else {
                                                
                                                let obj = {

                                                    current_price: price.toString() + "_" + transferList[i].log_index,
                                                    address: transferList[i].wallet_from,
                                                    token_address: transferList[i].token_address,
                                                    quantity: transferList[i].quantity,
                                                    block_number: transferList[i].block_number,
                                                    block_time: formatTimestamp(transferList[i].block_time),
                                                    type: null
                                                }
                                                holdMoneyList[from_str] = obj
                                            }

                                            /**
                                             * to的钱包地址
                                             * @type {string}
                                             */

                                            let to_str = `${transferList[i].block_number}_${transferList[i].token_address}_${transferList[i].wallet_to}`;
                                            if (holdMoneyList[to_str]) {
                                                holdMoneyList[to_str].current_price += "," + price.toString() + "_" + transferList[i].log_index;
                                            } else {
                                                let obj = {

                                                    current_price: price.toString() + "_" + transferList[i].log_index,
                                                    address: transferList[i].wallet_to,
                                                    token_address: transferList[i].token_address,
                                                    quantity: transferList[i].quantity,
                                                    block_number: transferList[i].block_number,
                                                    block_time: formatTimestamp(transferList[i].block_time),
                                                    type: null
                                                }
                                                holdMoneyList[to_str] = obj
                                            }
                                            
                                            
                                            
                                            insertList.push(info);
                                        } catch (ex) {
                                            console.log(ex);
                                        }
                                    }
                                }
                            }
                            resolve();
                        } catch (ex) {
                            console.log(ex);
                            reject(false);
                        }
                    }));
                }

                await Promise.allSettled(threadp);
                let rows = [];

                let holdMoneyKeys = Object.keys(holdMoneyList);
                for (let j = 0; j < holdMoneyKeys.length; j++) {
                    holdArrList.push(holdMoneyList[holdMoneyKeys[j]])
                }
                holdMoneyKeys = {};

                for (let index = 0; index < insertList.length; index++) {

                    let element = insertList[index];
                    let row = `${element.token_address}|${element.transfer_time}|${element.log_index}|${element.block_number}|${element.from_address}|${element.to_address}|${element.wallet_from}|${element.wallet_to}|${element.trans_hash}|${element.quantity}|${element.token_price}|${element.amount_usd}`;
                    //console.log(row);
                    rows.push(row);


                }
                slAveTransfer(rows, block_listen);
                let holders = [];
                for (let indexi = 0; indexi < holdArrList.length; indexi++) {

                    let element = holdArrList[indexi];
                    let row = `${element.address}|${element.token_address}|${element.block_time}|${element.block_number}|0|${element.current_price}|0|0|${element.quantity}|${element.type}`;
                    //console.log(row);
                    holders.push(row);
                }

                slAveTransferUser(holders,block_listen);
                console.log('结束查询,耗时', (Date.now()-start)/1000)
            }
        }
    } catch (ex) {
        console.log(ex);
    }



}

//startSwaps();

module.exports = {
    startSwaps
}
const redis = require('../database/redis_in')

const {ethers} = require("ethers");
const {queryGuiProvider} = require('../avi/factory_provider');
const provider = queryGuiProvider();
// const provider = queryProvider();

const factoryAbi = require('../abi/router.json');
const {delay} = require("../utils/functions");
const {formatUnits} = require("ethers");
//存储获取WBNB的区块号的List集合
const WBNB_BLOCK_NUM_LIST = 'WBNB_BLOCK_NUM_LIST';
//存储在链上获取的当前区块号的WBNB的价格HASH
const WBNB_LIST_KEY = 'WBNB_LIST_KEY'
//存储在链上获取失败的列表
const WBNB_LIST_KEY_ERR = 'WBNB_LIST_KEY_ERR'
//存储wbnb当前解析到的价格
const WBNB_CURRENT_PRICE = 'WBNB_CURRENT_PRICE'
//存储wbnb当前解析到的价格区块号
const WBNB_CURRENT_PRICE_NO = 'WBNB_CURRENT_PRICE_NO';

async function getInfo(blockNum) {
    try {
        // let price = await redis.hGet(WBNB_LIST_KEY, blockNum);//获取历史价格
        //第一个交易对
        let contract = new ethers.Contract("******************************************", factoryAbi, provider);
        let out = await contract.getAmountsOut('1000000000000000000', ['******************************************', '******************************************'], {blockTag: blockNum});
        out = out.toString();
        let reserve = out.split(',')[1];//对应的u储备量
        if (out.indexOf(',')>-1) {
            let price = formatUnits(reserve, 18);
              console.log("获取历史价格成功");
            return price;
        }
    } catch (error) {
        console.error("获取历史价格异常："+error);
        let price;
        try {
            price = await redis.get(WBNB_CURRENT_PRICE);//获取历史价格
        } catch (error) {
            console.error("获取历史价格失败："+error);
            price = false;
        }
        return price;
    }


}

async function parseInit() {
    while (true) {
        try {
            let start = Date.now();
            let block = await redis.lPop(WBNB_BLOCK_NUM_LIST);
            // let block = "41833229";
            // await redis.lPush(WBNB_BLOCK_NUM_LIST,block);
            if (!block) {
                await delay(3000);
                console.log("等待数据..");
                continue;
            }
            console.log("start" + block);
            let price = await getInfo(Number(block));
            if (price == false) {
                await redis.rPush(WBNB_LIST_KEY_ERR, block);
            } else {
                await redis.hSet(WBNB_LIST_KEY, block, price);
                await redis.set(WBNB_CURRENT_PRICE, price);//记录历史价格
                await redis.set(WBNB_CURRENT_PRICE_NO, block);//记录目前最新解析的价格区块号
            }
            console.log("完成:%s,time:%d s", block, (Date.now() - start) / 1000);
        } catch (e) {
            console.error("error:" + e);
        }
    }
}


// async function setDbnbListInfo(threadNum){
//     console.log("获取DBNB价格程序启动了")
//     let threadpromise = [];
//     for (let i = 0; i < threadNum; i++) {
//         threadpromise.push(new Promise(async (resolve, reject) => {
//             while (true) {
//                 let currentInfo = await Redis.rpop(WBNB_BLOCK_NUM_LIST);
//                 if (currentInfo) {
//                     try {
//                         let price = await getInfo(parseInt(currentInfo));
//                         if(price){
//                             await Redis.hset(WBNB_LIST_KEY, currentInfo,price);
//                         }
//                     } catch (e) {
//                         await Redis.rpush(WBNB_LIST_KEY_ERR, currentInfo);
//                         reject(currentInfo)
//                     }

//                 } else {
//                     resolve('end')
//                     break;
//                 }
//             }
//             console.log("当前线程结束------------", i)
//         }))
//     }
//     Promise.all(threadpromise).then(res => {
//         console.log("获取价格程序结束")

//     }).catch(err => {
//         console.log("PromiseALL集合报错", err)
//     })
// }
// async function setBlock(strBlock,endBlock) {
//     let list = [];
//     let currentBlock = strBlock;
//     while (true){
//         if(currentBlock>endBlock){
//             break;
//         }
//         list.push(currentBlock);
//         currentBlock++;
//     }
//     Redis.rpush(WBNB_BLOCK_NUM_LIST,list);
// }
// async function parse(){
//     while (true) {
//         parseInit();
//         await delay(10000)

//     }
// }
// async function parseInit(){
//     let currentBlock = parseInt(await Redis.get(WBNB_CURRENT_BLOCK));
//     if(!currentBlock){
//         currentBlock = STR_BLOCK;
//     }
//     let newBlock = await provider.getBlockNumber();
//     if(newBlock-currentBlock>=10){
//         await Redis.set(WBNB_CURRENT_BLOCK, newBlock)
//         await setBlock(currentBlock,newBlock);
//         await setDbnbListInfo(10);
//     }
// }

// setDbnbListInfo(5)

//跑区块号列表
//parseInit()

//配置多线程
async function start() {
    try {
        let threadCount = 20;
        let threadPromise = [];
        //拆分成多个线程promise执行
        for (let i = 0; i < threadCount; i++) {
            threadPromise.push(new Promise(async (resolve, reject) => {
                await parseInit();
                resolve();
            }));
        }
        let p = Promise.all(threadPromise);
        await p.then(arr => {

        }, e => {
            // console.log("失败子线程"+e)
        })
    } catch (ex) {
        console.log(ex);
    }

}

async function fix() {
    while (true) {
        let start = Date.now();
        let item = await redis.lPop("listen-sync-key_err_syncParseMap");//获取历史价格
        if (!item) {
            break;
        }
        let json = JSON.parse(item);
        let blockNumber = Number(json[0]['block_number']);
        let price = await getInfo(blockNumber);
        if (price == false) {
            await redis.rPush(WBNB_LIST_KEY_ERR, blockNumber);
        } else {
            await redis.hSet(WBNB_LIST_KEY, blockNumber, price);
        }
        console.log("完成:%s,time:%d ms", blockNumber, (Date.now() - start));
    }
}

// fix();
start();

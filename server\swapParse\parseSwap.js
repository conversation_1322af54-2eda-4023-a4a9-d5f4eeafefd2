
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis_in");
const {getTokenAAndBPriceByTime} = require("../utils/klineUtils");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const {dbSwapQuery, dbInsertSwap} = require('../model/mysql/AviBaseSwapModel');

const {slAveTrade} = require('../utils/doris/aveTradeSL');
const {delay} = require("../utils/functions");
const key_all = "block_swap_list";
const { formatUnits } = require("ethers");
const { default: BigNumber } = require('bignumber.js');
const mapLimit = require('async/mapLimit');
const {StringToTimeStamp, formatTimestamp} = require("../utils/DateUtils");
async function startRun() {
    try{
        while(true){
            let block_listen = await redis.lPop(key_all);
            // await redis.lPush(key_all,block_listen);
            if(!block_listen){
                console.log("等待数据..");
                await delay(5000);
                continue;
            }

            let block_list = block_listen.split(',');
            
            console.log('开始查询'+block_list[0]+"_"+block_list[1])
            const json = await dbSwapQuery([{
                    type: ">=",
                    column: "block_number",
                    value: Number(block_list[0])
                },{
                    type: "<",
                    column: "block_number",
                    value: Number(block_list[1])
                }]);
            if(json.length >0){
                let map_all  = [];
                let threadp =[];
                for (let i=0;i< json.length; i++){
                    threadp.push(new Promise( async (resolve, reject) => {
                        try{
                            let pair = await redis.hGet("pair_qualified_table", json[i].address);
                            if(pair){
                                pair = JSONbig.parse(pair);
                                let token0 = await redis.hGet("token_json_hdata", pair.token0_addr);
                                if (!token0) {
                                    throw Error("未找到token0");
                                }else{
                                    token0 = JSON.parse(token0);
                                }
                                let token1 = await redis.hGet("token_json_hdata", pair.token1_addr);
                                if (!token1) {
                                    throw Error("未找到token1");
                                }else{
                                    token1 = JSON.parse(token1);
                                }
                                
                                let type = TradeTypeEnum.BUY;
                                let fromAmount = 0;
                                let toAmount = 0;
                                let amount = 0;
                                let to_address = '';
                                let from_address = '';
                                let wallet_address = '';
                                let toPriceUsd = 0;
                                let fromPriceUsd = 0;
                                let amountUsd = new BigNumber(0);
                                let amount0_In = new BigNumber(0);
                                let amount1_Out = new BigNumber(0);
                                let amount0In = '0';
                                let amount0Out = '0';
                                let amount1In = '0';
                                let amount1Out = '0';
                                if(json[i].amount0_in == null || json[i].amount0_in==undefined || json[i].amount0_in ==''){

                                }else{
                                    amount0_In = new BigNumber(json[i].amount0_in);
                                    amount0In = json[i].amount0_in;
                                }
                                if(json[i].amount1_in == null || json[i].amount1_in==undefined || json[i].amount1_in ==''){

                                }else{
                                    amount1In = json[i].amount1_in;
                                }
                                if(json[i].amount0_out == null || json[i].amount0_out==undefined || json[i].amount0_out ==''){

                                }else{
                                    amount0Out = json[i].amount0_out;
                                }
                                if(json[i].amount1_out == null || json[i].amount1_out==undefined || json[i].amount1_out ==''){

                                }else{
                                    amount1_Out = new BigNumber(json[i].amount1_out);
                                    amount1Out = json[i].amount1_out;
                                }
                                
                                if(pair.token_index == 0){
                                    
                                    if(amount0_In.eq(0) || amount1_Out.eq(0)){
                                        //卖出（卖出当前币=买入多少U，所以这里是type=BUY）
                                        type = TradeTypeEnum.BUY;
                                        fromAmount = formatUnits(amount1In,token1.decimals);
                                        from_address = pair.token1_addr;
                                        toAmount = formatUnits(amount0Out,token0.decimals);
                                        to_address = pair.token0_addr;
                                        let tokenPriceUsd = await getTokenAAndBPriceByTime(pair,json[i].block_number,json[i].log_index,toAmount,fromAmount);
                                        fromPriceUsd = tokenPriceUsd.priceB;
                                        toPriceUsd = tokenPriceUsd.priceA;
                                        amountUsd = toPriceUsd.multipliedBy(Number(toAmount));
                                        amount = toAmount;
                                    }else{
                                        //买入（买入当前币=卖出多少U，所以这里是type=SELL）
                                        type = TradeTypeEnum.SELL;
                                        fromAmount = formatUnits(amount0In,token0.decimals);
                                        from_address = pair.token0_addr;
                                        toAmount = formatUnits(amount1Out,token0.decimals);
                                        to_address = pair.token1_addr;
                                        let tokenPriceUsd = await getTokenAAndBPriceByTime(pair,json[i].block_number,json[i].log_index,fromAmount,toAmount);
                                        fromPriceUsd = tokenPriceUsd.priceA;
                                        toPriceUsd = tokenPriceUsd.priceB;
                                        amountUsd = fromPriceUsd.multipliedBy(Number(fromAmount));
                                        amount = fromAmount;
                                    }
                                }else{
                                    if(amount0_In.eq(0) || amount1_Out.eq(0)){
                                        //卖出
                                        type = TradeTypeEnum.SELL;
                                        fromAmount = formatUnits(amount1In,token1.decimals);
                                        from_address = pair.token1_addr;
                                        toAmount = formatUnits(amount0Out,token0.decimals);
                                        to_address = pair.token0_addr;
                                        let tokenPriceUsd = await getTokenAAndBPriceByTime(pair,json[i].block_number,json[i].log_index,toAmount,fromAmount);
                                        
                                        fromPriceUsd = tokenPriceUsd.priceA;
                                        toPriceUsd = tokenPriceUsd.priceB;
                                        amountUsd = fromPriceUsd.multipliedBy(Number(fromAmount));
                                        amount = fromAmount;
                                    }else{
                                        //买入
                                        type = TradeTypeEnum.BUY;
                                        fromAmount = formatUnits(amount0In,token0.decimals);
                                        from_address = pair.token0_addr;
                                        toAmount = formatUnits(amount1Out,token1.decimals);
                                        to_address = pair.token1_addr;
                                        let tokenPriceUsd = await getTokenAAndBPriceByTime(pair,json[i].block_number,json[i].log_index,fromAmount,toAmount);
                                        
                                        fromPriceUsd = tokenPriceUsd.priceB;
                                        toPriceUsd = tokenPriceUsd.priceA;
                                        amountUsd = toPriceUsd.multipliedBy(Number(toAmount));
                                        amount =toAmount;
                                    }
                                }
                            
                                const saveData = {
                                    address:json[i].address,
                                    block_number:json[i].block_number,
                                    block_time:formatTimestamp(json[i].block_time),
                                    trans_hash:json[i].trans_hash,
                                    to_address:to_address,
                                    from_address:from_address,
                                    type:type,
                                    tx_sender:json[i].from_address,
                                    token_address:pair.token_addr,
                                    tx_to:json[i].to_address,
                                    from_amount:fromAmount,
                                    from_price_usd:fromPriceUsd.toFixed(30),
                                    to_amount:toAmount,
                                    to_price_usd:toPriceUsd.toFixed(30),
                                    amount_usd:amountUsd.toFixed(30),
                                    amount:amount,
                                    log_index: json[i].log_index,
                                    wallet_address:json[i].to
                                };
                                map_all.push(saveData);
                                
                            }
                            resolve();
                        }catch(ex){
                            console.log(ex);
                            reject(false);
                        }
                    }));
                }
                
                await Promise.allSettled(threadp);
                let rows = [];
                console.log("解析结束")
                for (let index = 0; index < map_all.length; index++) {
                    
                    const element = map_all[index];
                    let row = `${element.address}|${element.block_number}|${element.block_time}|${element.trans_hash}|${element.log_index}|${element.token_address}|${element.to_address}|${element.from_address}|${element.type}|${element.tx_sender}|${element.tx_to}|${element.from_amount}|${element.from_price_usd}|${element.to_amount}|${element.to_price_usd}|${element.amount_usd}|${element.amount}|${element.wallet_address}`;
                    //console.log(row);
                    rows.push(row);
                    
                    
                }
                slAveTrade(rows,block_listen);
                console.log('结束查询',Date.now())
            }
        }
    }catch(ex){
        console.log(ex);
    }
    
    

}

// startRun();

module.exports={
    startRun
}
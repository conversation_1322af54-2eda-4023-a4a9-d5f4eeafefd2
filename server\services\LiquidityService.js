const PairService = require("./pairService");
// const RedisClient = require("../database/redis/index1");
const SyncService = require("./SyncService");
const {default: BigNumber} = require("bignumber.js");
const LiquidityTypeEnum = require("../utils/enums/LiquidityTypeEnum");
const LiquidityModel = require("../model/mysql/LiquidityModel");
const LpHolderModel = require("../model/mysql/LpHolderModel");
const LpHolderLogModel = require("../model/mysql/LpHolderLogModel");
const {provider} = require('../avi/factory_provider');
const {pairAbi} = require("../abi");
const {formatUnits, Contract} = require("ethers");
const {getAddressType} = require("../utils/EtherUtils");
const PairModel = require("../model/mysql/PairModel");
const TokenModel = require("../model/mysql/TokenModel");
const redis = require("../database/redis");
const key = "parse_whitelist";
/**
 *
 * @param { JSON } saveData
 */
async function saveLiquidity(saveData, blockTime) {
    //查询ave_liquidity表的内容，created返回true表示没有需要创建并且返回，false表示已经存在直接返回
    let [liquidity, created] = await LiquidityModel().findOrCreate({
        where: {
            transaction_hash: saveData.transaction_hash,
            log_index: saveData.log_index,
        },
        defaults: saveData
    });

    if (created) {
        let pairUpdateData = {};
        let isUpdate = false;

        //获取交易对的信息ave_pairs表
        const pairInfo = await PairModel().findByPk(saveData.pair_id, {
            attributes: ['id', 'address', 'decimals', 'holders', 'total_supply']
        });
        if (!pairInfo) {
            return 0;
        }
        //从链上获取lp的数量
        const quantity = await getLpQuantity(pairInfo.address, saveData.wallet_address, pairInfo.decimals);
        const totalSupply = await getLpTotalSupply(pairInfo.address, pairInfo.decimals);

        const todayStartTimeStamp = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);
        const notTime = Math.floor(new Date().getTime() / 1000);
        //ave_lp_holder_logs表
        let lpHolderLog = await LpHolderLogModel().findOne({
            where: {
                pair_id: saveData.pair_id,
                date_time: todayStartTimeStamp,
                address: saveData.wallet_address
            }
        });
        //获取当前地址类型
        const addressType = await getAddressType(saveData.wallet_address);
        //如果当前的开始时间<区块时间
        if (todayStartTimeStamp < blockTime) {
            if (!lpHolderLog) {
                //创建
                const changeQuantity = saveData.type == LiquidityTypeEnum.ADD ? new BigNumber(0).plus(saveData.quantity) : new BigNumber(0).minus(saveData.quantity);
                const amountUsd = saveData.type == LiquidityTypeEnum.ADD ? new BigNumber(0).plus(saveData.amount_usd) : new BigNumber(0).minus(saveData.amount_usd);
                //存入ave_lp_holder_logs表
                await LpHolderLogModel().create({
                    date_time: todayStartTimeStamp,
                    quantity: quantity,
                    change_quantity: changeQuantity.toFixed(18),
                    change_usd: amountUsd.toFixed(30),
                    pair_id: saveData.pair_id,
                    address: saveData.wallet_address,
                    is_contract: addressType,
                    publish_time: notTime
                });
            } else {
                const changeQuantity = saveData.type == LiquidityTypeEnum.ADD ?
                    new BigNumber(lpHolderLog.quantity).plus(saveData.quantity) :
                    new BigNumber(lpHolderLog.quantity).minus(saveData.quantity);
                const amountUsd = saveData.type == LiquidityTypeEnum.ADD ?
                    new BigNumber(lpHolderLog.amount_usd).plus(saveData.amount_usd) :
                    new BigNumber(lpHolderLog.amount_usd).minus(saveData.amount_usd);
                await lpHolderLog.update({
                    quantity: quantity,
                    change_quantity: changeQuantity.toFixed(18),
                    change_usd: amountUsd.toFixed(30),
                    publish_time: notTime,
                });
            }
        }
        //lpHolder查询ave_lp_holders表
        let lpHolder = await LpHolderModel().findOne({
            where: {
                pair_id: saveData.pair_id,
                address: saveData.wallet_address,
            },
            attributes: ['id', 'pair_id', 'address', 'publish_time', 'quantity'],
        });
        if (!lpHolder) {
            await LpHolderModel().create({
                quantity: quantity,
                pair_id: saveData.pair_id,
                address: saveData.wallet_address,
                is_contract: addressType,
                publish_time: notTime,
            });

            const holders = await LpHolderModel().count({
                where: {
                    pair_id: saveData.pair_id,
                }
            });
            isUpdate = true;

            if (holders != pairInfo.holders) {
                pairUpdateData.holders = holders;
                const token = await TokenModel().findOne({
                    where: {
                        pair_id: saveData.pair_id,
                    },
                    attributes: ['id', 'holders', 'lp_holder_count', 'pair_id'],
                });
                if (token) {
                    // const redis = new RedisClient();
                    // let proportionRate = holders / token.holders * 100;
                    // await redis.zAdd(RankCacheEnum.PROPORTION_RANK, proportionRate.toFixed(18), saveData.pair_id);
                    if (token.lp_holder_count != holders) {
                        await token.update({
                            lp_holder_count: holders
                        });
                    }
                }
            }
        } else if (lpHolder.publish_time < blockTime) {
            lpHolder.update({
                quantity: quantity,
                publish_time: notTime,
            });
            isUpdate = true;
        }
        pairUpdateData.total_supply = totalSupply;
        if (isUpdate) {
            await pairInfo.update(pairUpdateData);
        }
    }
}

/**
 *  获取LP数量
 * @param { String } pairAddress
 * @param { Number } decimals
 * @returns
 */
async function getLpTotalSupply(pairAddress, decimals) {
    let totalSupply;
    try {
        const pairContract = new Contract(pairAddress, pairAbi, provider);
        let totalSupply = await pairContract.totalSupply();
        totalSupply = totalSupply ? totalSupply : '0';
    } catch (error) {
        console.error("获取LP数量失败", error);
    }
    return formatUnits(totalSupply, decimals);
}

/**
 *  获取LP数量
 * @param { String } pairAddress
 * @param { String } walletAddress
 * @param { Number } decimals
 * @returns
 */
async function getLpQuantity(pairAddress, walletAddress, decimals) {
    let quantity = '0';
    try {
        const pairContract = new Contract(pairAddress, pairAbi, provider);
        quantity = await pairContract.balanceOf(walletAddress);
        quantity = formatUnits(quantity, decimals);
    } catch (error) {
        console.error("获取LP数量失败", error);
    }
    return quantity;
}

/**
 * 解析流动性 得到价格
 * @param { BaseMintModel | BaseBurnModel } log
 * @param { LiquidityTypeEnum } log
 * @returns { boolean | JSON }
 */
async function parseLiquidity(log, type) {  //查询白名单中是否有这个地址，没有就不查询kline
    // const pair = await PairModel().findOne({
    //     where:{ address: log.address },
    //     attributes: ['id','address', 'token0_id','token1_id', 'decimals','token_id','token_index','status', 'path'],
    // });
    const pair = await PairService.getCachePairByAddress(log.address);

    if ((!pair || pair.status == 0) || (!pair.id || pair.id == '') || (!pair.address || pair.address == '') || (!pair.token0_id || pair.token0_id == '') || (!pair.token1_id || pair.token1_id == '') || (!pair.path || pair.path == '')) {
        return false;
    }
    let token0 = await TokenModel().findOne({
        where: {id: pair.token0_id},
        attributes: ['id', 'address', 'token0_id', 'token1_id', 'decimals', 'token_id', 'token_index', 'status', 'path'],
    });

    let token1 = await TokenModel().findOne({
        where: {id: pair.token1_id},
        attributes: ['id', 'address', 'token0_id', 'token1_id', 'decimals', 'token_id', 'token_index', 'status', 'path'],
    });
    if(!token0||!token1){return false;}
    log.amount0 = formatUnits(log.amount0, token0.decimals);
    log.amount1 = formatUnits(log.amount1, token1.decimals);
    const quantity = new BigNumber(log.amount0).times(log.amount1).sqrt().toFixed(30);
    let saveData = {
        pair_id: pair.id,
        token0_id: pair.token0_id,
        token1_id: pair.token1_id,
        sender: log.sender,
        wallet_address: log.from_address,
        amount0: log.amount0,
        amount1: log.amount1,
        quantity: quantity,
        token0_price_usd: "0",
        token1_price_usd: "0",
        amount_usd: "0",
        type: type,
        transaction_hash: log.trans_hash,
        transaction_time: log.block_time,
        log_index: log.log_index,
    };
    const tokenBPriceUsd = await SyncService.getTokenBPriceUsdByPath(pair.path, log.block_number);
    let amount0 = new BigNumber(log.amount0);
    let amount1 = new BigNumber(log.amount1);
    const isZero = amount0.eq(0) || amount1.eq(0);
    if (pair.token_index === 0) {
        const quote = !isZero ? amount1.div(amount0) : new BigNumber(0);
        saveData.token0_price_usd = quote.multipliedBy(tokenBPriceUsd).toFixed(30);
        saveData.token1_price_usd = tokenBPriceUsd.toFixed(30);
        saveData.amount_usd = tokenBPriceUsd.multipliedBy(amount1).toFixed(30);
    } else {
        const quote = !isZero ? amount0.div(amount1) : new BigNumber(0);
        saveData.token0_price_usd = tokenBPriceUsd.toFixed(30);
        saveData.token1_price_usd = quote.multipliedBy(tokenBPriceUsd).toFixed(30);
        saveData.amount_usd = tokenBPriceUsd.multipliedBy(amount0).toFixed(30);
    }
    return saveData;
}

module.exports = {
    parseLiquidity,
    saveLiquidity
}
const {default: BigNumber} = require("bignumber.js");
BigNumber.config({DECIMAL_PLACES: 100});
const {ethers, formatUnits} = require("ethers");
const axios = require('axios');
const {PqueryQL_sonar, HQL_sonar} = require("../../database/PqueryQL/PostgresSQL");
const {getTokenPsqlUsdt, USD_LIST} = require("./duneService");
const {formatStrDecimal, getTokenCache} = require("../../utils/BaseDataUtils");
const apiKey = 'cqt_rQcYHqxCftyqPJRWtrfxrJdmFtWR';
const chainId = 56;
const id4321860 = 4321860;
const id4328300 = 4328300;

async function getTrackInfo(request, result) {

    let params = request.body
    if (!params.token_address || !params.wallet_address) {
        result.json({
            code: 500,
            msg: 'token_address,wallet_address参数不能为空',
            data: null
        })
        return;
    }

    params.token_address = params.token_address.toLowerCase();
    let token_address = params.token_address;
    let wallet_address = params.wallet_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        wallet_address = wallet_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address)
        tokenPrice = new BigNumber(tokenPrice)
        //查询交易对信息
       let data = {
            token:token,
            balance:'',//用户持有
            amountUsd:'',//价值
            currentPriceUsd:tokenPrice,//当前价格
            // costPrice:'',//购买成本
            // profitLoss:'',//预警盈亏金额
            // profitLossRatio:'',//预警盈亏百分比
       }
        let arg = `
            SELECT 
                *
            FROM 
                MV_LAST_balance_changes
            WHERE 
                contract='${token_address}'
                and owner = '${wallet_address}' 
            ORDER BY datetime DESC
            `;
        let tokenBalance = await HQL_sonar(arg);
        console.log(tokenBalance);
        if(tokenBalance && tokenBalance.length>0) {

            let jsons = tokenBalance[0];
            //判断是token0还是1
            data.balance = formatStrDecimal(jsons.new_balance, tokenDecimal)
            data.amountUsd = tokenPrice.times(new BigNumber(data.balance));
        }
        result.json({
            code: 200,
            msg: "请求成功！",
            data: data
        })
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}
async function getTransferList(request, result) {

    let params = request.body
    if (!params.token_address || !params.wallet_address) {
        result.json({
            code: 500,
            msg: 'token_address,wallet_address参数不能为空',
            data: null
        })
        return;
    }

    params.token_address = params.token_address.toLowerCase();
    let token_address = params.token_address;
    let wallet_address = params.wallet_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        wallet_address = wallet_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address)
         tokenPrice = new BigNumber(tokenPrice)
        //查询交易对信息
        let arg = `
            SELECT 
                *
            FROM
                transfers_v2_encoded
            WHERE 
                address='${token_address}'
                and (from = '${wallet_address}' OR to = '${wallet_address}')
            ORDER BY f_time DESC 
            `;
        let data = await HQL_sonar(arg);
        // console.log(data)
        if(data && data.length>0){
            let totalOut = new BigNumber(0);
            let totalIn = new BigNumber(0);
            let totlaOutAmount = new BigNumber(0);
            let totlaInAmount = new BigNumber(0);

            let dataList = [];
            for (let i = 0; i < data.length; i++) {
                let jsons = data[i];

                //判断是token0还是1
                if(jsons.from==wallet_address){
                    //当前代币进入到pair所以是卖出
                    totalIn = totalIn.plus(new BigNumber(jsons.value));
                    jsons.type=0;
                    jsons.num = formatStrDecimal(jsons.value, tokenDecimal)
                    jsons.wallet_address = jsons.to

                }
                else if(jsons.to==wallet_address){
                        //当前代币进入到pair所以是卖出
                    totalOut = totalOut.plus(new BigNumber(jsons.value));
                    jsons.type=1;
                    jsons.num = formatStrDecimal(jsons.value, tokenDecimal)
                    jsons.wallet_address = jsons.from
                }
                console.log(tokenPrice.toString())
                jsons.priceUsd = (new BigNumber(jsons.num).times(tokenPrice)).toString()
                if(i<20){
                    dataList.push(jsons)
                }
            }

            totlaInAmount = new BigNumber(formatStrDecimal(totalIn, tokenDecimal)).times(tokenPrice).toString();
            totlaOutAmount = new BigNumber(formatStrDecimal(totalOut, tokenDecimal)).times(tokenPrice).toString();
            totalIn = new BigNumber(formatStrDecimal(totalIn, tokenDecimal)).toString();
            totalOut = new BigNumber(formatStrDecimal(totalOut, tokenDecimal)).toString();
            let data_return = {
                list: dataList,
                totalIn: totalIn.toString(),
                totalOut: totalOut.toString(),
                totlaInAmount: totlaInAmount,
                totlaOutAmount: totlaOutAmount
            }
            result.json({
                code: 200,
                msg: "请求成功！",
                data: data_return
            })
        }
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}
async function getTradeList(request, result) {

    let params = request.body
    if (!params.token_address || !params.wallet_address) {
        result.json({
            code: 500,
            msg: 'token_address,wallet_address参数不能为空',
            data: null
        })
        return;
    }

    let token_address = params.token_address;
    let wallet_address = params.wallet_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        wallet_address = wallet_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address);
        tokenPrice = new BigNumber(tokenPrice);
        //查询交易对信息
        let pairsArg = `
            WITH combined_pairs AS (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY address  ORDER BY datetime DESC) AS rn
                FROM (
                    SELECT *
                    FROM MV_pairs_token0
                    WHERE token0 = '${token_address}'
            
                    UNION ALL
            
                    SELECT *
                    FROM MV_pairs_token1
                    WHERE token1 = '${token_address}'
                )
            )
            SELECT *
            FROM combined_pairs
            WHERE rn = 1

        `;


        let data = await HQL_sonar(pairsArg);
        if (data && data.length > 0) {
            let rows = data;
            let pairsArr=""
            for (let i = 0; i < rows.length; i++) {
                let jsons = rows[i];
                if(i==rows.length-1){
                    pairsArr+=`'${jsons.address}'` ;
                }
                else {
                    pairsArr+=`'${jsons.address}',` ;
                }
            }
            let trackSql = `
            SELECT 
                *
            FROM 
                MV_swaps
            WHERE 
                contract in (${pairsArr})
                and (sender = '${wallet_address}' OR to = '${wallet_address}')
            ORDER BY datetime DESC 
            `;
            let hqlData = await HQL_sonar(trackSql);
            console.log(hqlData)
            let otherToken = {};
            if(hqlData && hqlData.length>0){

                let totalOut = new BigNumber(0);
                let totalIn = new BigNumber(0);
                let totlaOutAmount = new BigNumber(0);
                let totlaInAmount = new BigNumber(0);

                let dataList = [];
                for (let i = 0; i < hqlData.length; i++) {
                    let jsons = hqlData[i];
                    let currentPair;
                    for (let j = 0; j < rows.length; j++) {
                        let pair = rows[j];
                        if(pair.address == jsons.contract ){
                            currentPair = pair;
                            break;
                        }
                    }

                    //如果确定是和U的稳定币交易对，那么当时就可以算出当前价格
                    if ( USD_LIST.indexOf(currentPair.token0) != -1) {
                        //获得稳定币的实际交易量
                        let token0Amount;
                        if(jsons.amount0_in !='0'){
                            token0Amount = formatStrDecimal(jsons.amount0_in, 18);
                        }
                        else {
                            token0Amount = formatStrDecimal(jsons.amount0_out, 18);
                        }
                        //获得当前币的实际交易量

                        let token1Amount;
                        if(jsons.amount1_in != '0'){
                            token1Amount = formatStrDecimal(jsons.amount1_in, tokenDecimal);
                        }
                        else {
                            token1Amount = formatStrDecimal(jsons.amount1_out, tokenDecimal);
                        }

                        //获得当前币对稳定币的实际数量（金额）
                        let currentUsd = new BigNumber(token0Amount).div(new BigNumber(token1Amount))
                        jsons.tokenUsdt = currentUsd;
                    }
                    else if (USD_LIST.indexOf(currentPair.token1) != -1) {
                        //获得稳定币的实际交易量
                        let token0Amount;
                        if(jsons.amount0_in !='0'){
                            token0Amount = formatStrDecimal(jsons.amount0_in, tokenDecimal);
                        }
                        else {
                            token0Amount = formatStrDecimal(jsons.amount0_out, tokenDecimal);
                        }
                        //获得当前币的实际交易量

                        let token1Amount;
                        if(jsons.amount1_in != '0'){
                            token1Amount = formatStrDecimal(jsons.amount1_in, 18);
                        }
                        else {
                            token1Amount = formatStrDecimal(jsons.amount1_out, 18);
                        }

                        //获得当前币对稳定币的实际数量（金额）
                        let currentUsd = new BigNumber(token1Amount).div(new BigNumber(token0Amount))
                        jsons.tokenUsdt = currentUsd
                    }
                    else {
                        jsons.tokenUsdt = tokenPrice
                    }

                    //判断是token0还是1
                    if(currentPair.token0==token_address){
                        if(jsons.amount0_in !='0' ){
                            //当前代币进入到pair所以是卖出
                            totalIn = totalIn.plus(new BigNumber(jsons.amount0_in));
                            jsons.type=0;
                            jsons.value = formatStrDecimal(jsons.amount0_in, tokenDecimal)
                            totlaInAmount = totlaInAmount.plus(new BigNumber(jsons.value).times(jsons.tokenUsdt))
                        }
                        else {
                            //否则是买入
                            totalOut = totalOut.plus(new BigNumber(jsons.amount0_out));
                            jsons.type=1;
                            jsons.value = formatStrDecimal(jsons.amount0_out, tokenDecimal)
                            totlaOutAmount = totlaOutAmount.plus(new BigNumber(jsons.value).times(jsons.tokenUsdt))
                        }
                    }
                    else if(currentPair.token1==token_address){
                        if(jsons.amount1_in !='0' ){
                            //当前代币进入到pair所以是卖出
                            totalIn = totalIn.plus(new BigNumber(jsons.amount1_in));
                            jsons.type=0;
                            jsons.value = formatStrDecimal(jsons.amount1_in, tokenDecimal)
                            totlaInAmount =totlaInAmount.plus(new BigNumber(jsons.value).times(jsons.tokenUsdt))
                        }
                        else {
                            //否则是买入
                            totalOut = totalOut.plus(new BigNumber(jsons.amount1_out));
                            jsons.value = formatStrDecimal(jsons.amount1_out, tokenDecimal)
                            jsons.type=1;
                            totlaOutAmount= totlaOutAmount.plus(new BigNumber(jsons.value).times(jsons.tokenUsdt))
                        }
                    }

                    if(i<20){
                        dataList.push(jsons)
                    }

                    jsons.priceUsd = (new BigNumber(jsons.value).times(jsons.tokenUsdt)).toString()
                }
                // totlaInAmount = new BigNumber(formatStrDecimal(totalIn, tokenDecimal)).times(tokenPrice).toString();
                // totlaOutAmount = new BigNumber(formatStrDecimal(totalOut, tokenDecimal)).times(tokenPrice).toString();
                totalIn = new BigNumber(formatStrDecimal(totalIn, tokenDecimal)).toString();
                totalOut = new BigNumber(formatStrDecimal(totalOut, tokenDecimal)).toString();
                let data_return = {
                    list: dataList,
                    totalIn: totalIn.toString(),
                    totalOut: totalOut.toString(),
                    totlaInAmount: totlaInAmount,
                    totlaOutAmount: totlaOutAmount
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: data_return
                })
            }
            else {
                result.json({
                    code: 200,
                    msg: "交易数据为空！",
                    data: null
                })

            }
        }
        else {
            result.json({
                code: 500,
                msg: "未查询到交易对！",
                data: null
            })
        }
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}

async function getLiquidityList(request, result) {

    let params = request.body
    if (!params.token_address || !params.wallet_address) {
        result.json({
            code: 500,
            msg: 'token_address,wallet_address参数不能为空',
            data: null
        })
        return;
    }

    let token_address = params.token_address;
    let wallet_address = params.wallet_address;

    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    }
    if (token) {
        token_address = token_address.toLowerCase().substring(2);
        wallet_address = wallet_address.toLowerCase().substring(2);
        let tokenPrice = await getTokenPsqlUsdt(token_address);
        tokenPrice = new BigNumber(tokenPrice);
        //查询交易对信息
        let pairsArg = `
          WITH combined_pairs AS (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY address  ORDER BY datetime DESC) AS rn
                FROM (
                    SELECT *
                    FROM MV_pairs_token0
                    WHERE token0 = '${token_address}'
            
                    UNION ALL
            
                    SELECT *
                    FROM MV_pairs_token1
                    WHERE token1 = '${token_address}'
                )
            )
            SELECT *
            FROM combined_pairs
            WHERE rn = 1

        `;


        let data = await HQL_sonar(pairsArg);
        if (data && data.length > 0) {
            let rows = data;
            let pairsArr=""
            for (let i = 0; i < rows.length; i++) {
                let jsons = rows[i];
                if(i==rows.length-1){
                    pairsArr+=`'${jsons.address}'` ;
                }
                else {
                    pairsArr+=`'${jsons.address}',` ;
                }
            }
            let trackSql = `
            SELECT 
                *
            FROM 
                MV_liquiditys
            WHERE 
                address in (${pairsArr})
                and to = '${wallet_address}'
            ORDER BY f_time DESC 
            `;
            let hqlData = await HQL_sonar(trackSql);
            console.log(hqlData)
            let otherToken ={};
            if(hqlData && hqlData.length>0){

                let totalRemove = new BigNumber(0);
                let totalAdd = new BigNumber(0);
                let totalRemoveAmount = new BigNumber(0);
                let totalAddAmount = new BigNumber(0);

                let dataList = [];
                for (let i = 0; i < hqlData.length; i++) {
                    let jsons = hqlData[i];
                    let currentPair;
                    for (let j = 0; j < rows.length; j++) {
                        let pair = rows[j];
                        if(pair.address == jsons.address ){
                            currentPair = pair;
                            break;
                        }
                    }
                    //判断是token0还是1
                    if(currentPair.token0==token_address){
                        jsons.type=0;
                        jsons.amount0 = formatStrDecimal(jsons.amount0, tokenDecimal)
                        jsons.amount0_usd = (new BigNumber(jsons.amount0).times(tokenPrice))
                        jsons.value = jsons.amount0
                        //如果这个代币存在列表中，就不用再次请求了
                        if(!otherToken[currentPair.token1]){
                            otherToken[currentPair.token1] = await getTokenCache('0x'+currentPair.token1);
                            otherToken[currentPair.token1]['usdt_price'] = new BigNumber(await getTokenPsqlUsdt(currentPair.token1));
                        }
                        jsons.amount1= formatStrDecimal(jsons.amount1, otherToken[currentPair.token1].decimals);
                        jsons.amount1_usd = (new BigNumber(jsons.amount1).times(otherToken[currentPair.token1]['usdt_price']))


                    }
                    else if(currentPair.token1==token_address){

                        if(jsons.mint){
                            totalAdd = totalAdd.plus(new BigNumber(jsons.amount0));
                        }
                        else {
                            //否则是买入
                            totalRemove = totalRemove.plus(new BigNumber(jsons.amount1));

                        }
                        jsons.type=1;
                        jsons.amount1 = formatStrDecimal(jsons.amount1, tokenDecimal);
                        jsons.amount1_usd = (new BigNumber(jsons.amount1).times(tokenPrice))
                        jsons.value = jsons.amount1
                        //如果这个代币存在列表中，就不用再次请求了
                        if(!otherToken[currentPair.token0]){
                            otherToken[currentPair.token0] = await getTokenCache('0x'+currentPair.token0);
                            otherToken[currentPair.token0]['usdt_price'] = new BigNumber(await getTokenPsqlUsdt(currentPair.token0));
                        }
                        jsons.amount0= formatStrDecimal(jsons.amount0, otherToken[currentPair.token0].decimals);
                        jsons.amount0_usd = (new BigNumber(jsons.amount0).times(otherToken[currentPair.token0]['usdt_price']))
                    }
                    jsons.priceUsd = jsons.amount0_usd.plus(jsons.amount1_usd)
                    if(i<20){
                        dataList.push(jsons)
                    }
                }
                totalAddAmount = new BigNumber(formatStrDecimal(totalAdd, tokenDecimal)).times(tokenPrice).toString();
                totalRemoveAmount = new BigNumber(formatStrDecimal(totalRemove, tokenDecimal)).times(tokenPrice).toString();
                totalAdd = new BigNumber(formatStrDecimal(totalAdd, tokenDecimal)).toString();
                totalRemove = new BigNumber(formatStrDecimal(totalRemove, tokenDecimal)).toString();
                let data_return = {
                    list: dataList,
                    totalAdd: totalAdd.toString(),
                    totalRemove: totalRemove.toString(),
                    totalAddAmount: totalAddAmount,
                    totalRemoveAmount: totalRemoveAmount
                }
                result.json({
                    code: 200,
                    msg: "请求成功！",
                    data: data_return
                })
            }
            else {
                result.json({
                    code: 200,
                    msg: "数据为空！",
                    data: null
                })

            }
        }
        else {
            result.json({
                code: 500,
                msg: "未查询到交易对！",
                data: null
            })
        }
    } else {
        result.json({
            code: 500,
            msg: "查询token失败！",
            data: null
        })
    }
}


//获取所有的币
async function getAllByUser(request, result) {
    let params = request.body;
    let wallet_address = params.wallet_address;
    const url = `https://api.covalenthq.com/v1/${chainId}/address/${wallet_address}/balances_v2/`;

    axios.get(url, {
        params: {
            'key': apiKey
        }
    }).then(response => {
        const data = response.data.data;
        const items = data.items;
        console.log(items);
        // let data_items = [];
        for (let index = 0; index < items.length; index++) {
            const element = items[index];
            element['balance'] = formatUnits(element['balance'], element['contract_decimals']);
        }
        result.json({
            code: 200,
            msg: "请求成功！",
            data: items
        })
    })
        .catch(error => {
            console.error('Error fetching data:', error);
        });


}

module.exports = {
    getTransferList,
    getTradeList,
    getLiquidityList,
    getTrackInfo,
    getAllByUser
}

const axios = require('axios');
const url = 'https://api.studio.thegraph.com/query/94692/pairandtoken2/2';
async function GqueryQL(arg) {
    let query = {
        query: arg
    };
    let res_data = await axios.post(url, query);
    return res_data;
}
async function queryMintOrBurn(arg) {
        let query = {
            query: arg
        };
        // let query = {
        //     query: `{
        //               liquidities(where: {type: 1, blockNumber_gt: "0", blockNumber_lte: "43996895"}) {
        //                 type
        //                 token
        //                 to
        //                 timestamp
        //                 id
        //                 blockNumber
        //                 amount1
        //                 amount0
        //               }
        //             }`
        // };
        let res_data = await axios.post(url, query);
        return res_data;
}
async function queryTransfer(arg) {
    let query = {
        query: arg
    };
    let res_data = await axios.post(url, query);
    return res_data;
}
async function querySwap(arg) {
    let query = {
        query: arg
    };
    let res_data = await axios.post(url, query);
    return res_data;
}
async function queryTest() {
    let query1 = {
        query: `
        {
          tokens(first: 5) {
            id
            name
            symbol
            totalSupply
          }
          pairs(first: 5) {
            id
            token0
            token1
            pair
          }
        }`
    };
    let query2 = {
        query: `
        {
            transfers(where: {blockNumber_gt: "44146220", blockNumber_lte: "44156220"}) {
                id
                blockNumber
            }
        }`
    };
    let query3 = {
        query: `
        {
            transfers(where: {blockNumber_gt: "44136220", blockNumber_lte: "44156220"}) {
                id
                blockNumber
            }
        }`
    };
    let res_data1 = await axios.post(url, query1);
    let res_data2 = await axios.post(url, query2);
    let res_data3 = await axios.post(url, query3);
    return {res_data1,res_data2,res_data3};
}

// queryMint();
module.exports = {
    queryMintOrBurn,
    queryTransfer,
    querySwap,
    GqueryQL
};
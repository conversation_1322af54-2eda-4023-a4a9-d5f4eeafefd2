const GiftCardModel = require("../../model/mysql/GiftCardModel");
const BlackListTextModel = require("../../model/mysql/BlackListTextModel");

const {formatDateToString} = require("../../utils/DateUtils");
const {JsonRpcProvider, Interface} = require("ethers");
const constant = require("../../utils/constant");
const provider = new JsonRpcProvider(constant.RPC_NEW, undefined, {polling: true});
const uuid = require("uuid");
const giftAbi = require("../../abi/giftAbi.json");

/**
 * 合法性验证
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function validateCode(request, result) {
    try {
        if (!request.body.codeId || !request.body.codeStr) {
            result.json({
                code: 400, msg: 'codeId,codeStr参数不能为空', data: null
            })
            return;
        }
        let codeId = request.body.codeId;
        let codeStr = request.body.codeStr;
        let ok = await GiftCardModel().findAll({
            where: {
                code_str: codeStr,
                status: 0
            }
        });
        if (ok.length > 0) {
            let use_time = formatDateToString(new Date());
            await GiftCardModel().update({status: 1, use_time: use_time, code_id: codeId}, {
                where: {
                    id: ok[0].id,
                }
            });
            result.json({
                code: 200, msg: "校验通过！", data: ok
            })
        } else {
            result.json({
                code: 500, msg: "校验失败！", data: null
            })
        }
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }

}

async function createCode(request, result) {
    try {
        if (!request.body.hash) {
            result.json({
                code: 400, msg: 'hash参数不能为空', data: null
            })
            return;
        }
        let hash = request.body.hash;
        let hashObj = await provider.getTransaction(hash);
        let giftInterface = new Interface(giftAbi);
        let data = giftInterface.parseTransaction(hashObj);
        let from = data.args[0];//from地址
        let toAddress = data.args[1];//接收地址

        if (toAddress != "0x4e9109D4d4726B04816f887Bb63dA661c5658d90") {//如果是礼包充值
            result.json({
                code: 500, msg: '礼包hash不正确！', data: null
            });
            //记录黑名单
            await BlackListTextModel().findOrCreate({
                where: {
                    hash: hash
                },
                defaults: {
                    hash: hash
                }
            });
            return;
        }
        let tokenId = data.args[2][0];//卡片编号
        let code = uuid.v4();//生成不重复码
        let [gc, isOk] = await GiftCardModel().findOrCreate({
            where: {
                wallet_address: from,
                hash: hash
            },
            defaults: {
                wallet_address: from,
                code_id: 0,
                code_str: code,
                use_time: '0',
                add_time: new Date(),
                status: 0,
                hash: hash,
                token_id: parseInt(tokenId)
            },
        });
        if (isOk) {
            result.json({
                code: 200, msg: "创建成功！", data: gc
            })
        } else {
            result.json({
                code: 500, msg: "此hash已存在，无需创建！", data: null
            })
        }
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }

}

module.exports = {
    validateCode, createCode
}

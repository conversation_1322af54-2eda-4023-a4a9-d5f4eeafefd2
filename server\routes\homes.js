/**
 * 描述: 用户路由模块
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/

const express = require('express');
const router = express.Router();
const service = require('../services/homeService');

// 所有的币种
router.post('/index', service.queryAll);
//查询公告
router.post('/notice', service.queryNotices);
//查询主页轮询图
router.post('/rotation', service.queryRotations);

router.post('/queryCurrency', service.queryCurrency);

router.post('/queryKline', service.queryKline);

router.post('/queryByType', service.queryByType);

router.post('/queryTrans', service.queryTrans);

router.post('/queryUserBi', service.queryUserBi);

router.post('/queryTransByUser', service.queryTransByUser);

router.post('/queryMobility', service.queryMobility);

router.post('/mobility', service.queryMobilityAll);

router.post('/queryMobiLp', service.queryMobiLp);

router.post('/currencyInfo', service.currencyInfo);

//查询近24小时活跃地址数量
router.post('/activeUsers',service.queryActiveUsers);

//查询资金的线图
router.post('/flowHistory',service.queryFlowHistory);
//查询常用下载
router.post('/queryDown',service.queryDown);
//查询快讯
router.post('/queryFlash',service.queryFlash);
//查询媒体社区
router.post('/queryMedia',service.queryMedia);
//查询区块浏览器
router.post('/queryBrows',service.queryBrows);
//查询区块通讯
router.post('/queryCommunicate',service.queryCommunicate);

module.exports = router;


const {default: BigNumber} = require("bignumber.js");
const {Decimal} = require("decimal.js");
BigNumber.config({DECIMAL_PLACES: 100});
const {errorToRedis, redis} = require("../../../utils/redisUtils");
const FirstAnalyzeEnum = require("../../../utils/enums/AnalyzeDorisKeyEnum");
/*合约相关*/
const {isContractAddress} = require("../../../utils/EtherUtils");
const constant = require("../../../utils/constant");
const {JsonRpcProvider, Contract, formatUnits, ethers} = require("ethers");
const {tokenAbi, multicall, allAbi} = require("../../../abi");
const {delay} = require("../../../utils/functions");
let token;
const {HQL_sonar} = require("../../../database/PqueryQL/PostgresSQL");
const {formatStrDecimal} = require("../../../utils/BaseDataUtils");
let contract;
let tokenContract;
let provider;
let last_block_number = 49628405;

async function initCache() {
    provider = new JsonRpcProvider(constant.RPC_NEW, undefined, {polling: true});
    // contract = new Contract("******************************************", multicall, provider);//声明中介调用地址合约
    // tokenContract = new Contract("******************************************", tokenAbi, provider);//随便找一个token地址解码都一样的
}

/**
 * 解析kline
 * @returns {Promise<void>}
 */
async function start() {
    await initCache();
    let main_token = [constant.BUSD_ADDRESS, constant.USDT_ADDRESS, constant.TUSD_ADDRESS, constant.USDC_ADDRESS];
    let initTime = Date.now();
    let v_key_pair = {min: 0, max: 100000};
    let v_key_token = {min: 0, max: 100000};
    let threadCount = 10;//开启线程的数量
    let run_time;
    while (true) {
        try {
            run_time = new Date().getTime();
            let v_key_redis_pair = await redis.get(FirstAnalyzeEnum.pair_task_vKey, v_key_pair);
            let pair_max_vKey = 0;
            let token_max_vKey = 0;
            if (v_key_redis_pair) {
                v_key_pair.min = Number(v_key_redis_pair);
                v_key_pair.max = v_key_pair.min + 10000;
            }
            let v_key_redis_token = await redis.get(FirstAnalyzeEnum.token_task_vKey, v_key_token);
            if (v_key_redis_token) {
                v_key_token.min = Number(v_key_redis_token);
                v_key_token.max = v_key_token.min + 10000;
            }
            pair_max_vKey = v_key_pair.max;
            token_max_vKey = v_key_token.max;
            if ((Date.now() - initTime) > 300000) {//如果大于5分钟，重新获取一次initCache（防止tcp超时）
                try {
                    await initCache();
                    initTime = Date.now();
                    console.log("RPC网络isOk");
                } catch (e) {
                    console.log("RPC网络错误：" + e);
                    await delay(5000);
                    continue;
                }
            }
            /**
             * 查询最大的block_number
             * @type {string}
             */

            if (v_key_pair.min > last_block_number) {//如果默认值last_block_number已经过时需要重新赋值
                let max_block_number = `select max(block_number) as last_block_number
                                        from pairs`;
                let max_data = await HQL_sonar(max_block_number);
                if (max_data && max_data.length > 0) {
                    last_block_number = Number(max_data[0]['last_block_number']);
                } else {
                    console.error("pairs表没有数据");
                    break;
                }
            }
            if (v_key_pair.min > last_block_number) {//再次判断是否满足min小于当前最大的block_number
                await delay(10000);
                continue;
            }
            if ((v_key_pair.min == last_block_number) || (v_key_pair.min < last_block_number && last_block_number < v_key_pair.max)) {
                v_key_pair.max = last_block_number;
            }
            if ((v_key_token.min == last_block_number) || (v_key_token.min < last_block_number && last_block_number < v_key_token.max)) {
                v_key_token.max = last_block_number;
            }
            console.log("pair开始" + v_key_pair.min + ",结束" + v_key_pair.max);
            console.log("token开始" + v_key_token.min + ",结束" + v_key_token.max);
            let arg = `WITH 
                            pairs AS ( -- 找出最新添加的pair
                                    select block_number as v_key,
                                           lower(arrayStringConcat(arrayMap(x -> hex(x), address))) as pairAddress,
                                           lower(arrayStringConcat(arrayMap(x -> hex(x), token0))) as token0,
                                           lower(arrayStringConcat(arrayMap(x -> hex(x), token1))) as token1,
                                           datetime
                                    from pairs
                                    where block_number > ${v_key_pair.min}
                                      and block_number <= ${v_key_pair.max}
                            ),
                            pairReserves as (
                                    select address as pairAddress, reserve0, reserve1
                                    from MV_LAST_syncs final
                                    where address in (select pairAddress from pairs)
                            ),
                            pairDecimals as (
                                    select address as tokenAddress,symbol, decimals, v_key
                                    from MV_LAST_contracts final
                                    where address in (select token0 from pairs)
                                        or address in (select token1 from pairs)
                                        or address in (select pairAddress from pairs)
                            ),
                            tokens as (
                                    select lower(arrayStringConcat(arrayMap(x -> hex(x), address))) AS tokenAddress,
                                          name,
                                          symbol,
                                          decimals,
                                          block_number as v_key
                                    from contracts
                                    where block_number > ${v_key_token.min}
                                     and block_number <= ${v_key_token.max}
                            )
                           SELECT toJSONString(map(
                                   'type', 'pairs',
                                   'v_key', toString(v_key),
                                   'pairAddress', toString(pairAddress),
                                   'token0', toString(token0),
                                   'token1', toString(token1),
                                   'time', formatDateTime(datetime, '%Y-%m-%d %H:%i:%s')
                                               )) AS json
                           FROM pairs
                           union all
                           SELECT toJSONString(map(
                                   'type', 'pairResreves',
                                   'pairAddress', toString(pairAddress),
                                   'reserve0', toString(reserve0),
                                   'reserve1', toString(reserve1)
                                               )) AS json
                           FROM pairReserves
                           union all
                           SELECT toJSONString(map(
                                   'type', 'pairDecimals',
                                   'tokenAddress', toString(tokenAddress),
                                   'symbol',toString(symbol),
                                   'decimals', toString(decimals),
                                   'v_key', toString(v_key)
                                               )) AS json
                           FROM pairDecimals
                           union all
                           SELECT toJSONString(map(
                                   'type', 'tokens',
                                   'tokenAddress', toString(tokenAddress),
                                   'name', toString(name),
                                   'symbol', toString(symbol),
                                   'decimals', toString(decimals),
                                   'v_key', toString(v_key)
                                               )) AS json
                           FROM tokens`;
            let data = await HQL_sonar(arg);
            let listPairs = [];
            let listTokens = [];
            let mapReserves = {};
            let mapDecimals = {};
            if (data && data.length > 0) {
                /**
                 * 整合数据
                 */
                for (let i = 0; i < data.length; i++) {
                    let json = data[i].json;
                    let jsonColumn = JSON.parse(json);
                    switch (jsonColumn['type']) {
                        case "pairs":
                            jsonColumn.v_key = Number(jsonColumn.v_key);
                            listPairs.push(jsonColumn);
                            break;
                        case "pairResreves":
                            mapReserves[jsonColumn.pairAddress] = {
                                reserve0: jsonColumn.reserve0,
                                reserve1: jsonColumn.reserve1
                            };
                            break;
                        case "pairDecimals":
                            mapDecimals[jsonColumn.tokenAddress] = {
                                symbol: jsonColumn.symbol,
                                decimals: Number(jsonColumn.decimals),
                                v_key: Number(jsonColumn.v_key)
                            };
                            break;
                        case "tokens":
                            jsonColumn.decimals = Number(jsonColumn.decimals);
                            jsonColumn.v_key = Number(jsonColumn.v_key);
                            if (jsonColumn.name != "" || jsonColumn.symbol != "") {
                                //如果没有名称说明是非token
                                listTokens.push(jsonColumn);
                            }
                            break;
                    }
                }
                /**
                 * pair
                 */
                if (listPairs.length > 0) {
                    let threadp = [];
                    for (let i = 0; i < listPairs.length; i++) {
                        threadp.push(new Promise(async (resolve, reject) => {
                            try {
                                let pair = listPairs[i];
                                pair.address = '0x' + pair.pairAddress;
                                let cache_pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", pair.address);
                                if (!cache_pair||JSON.parse(cache_pair).decimals == 0) {
                                    let reserves = mapReserves[pair.pairAddress];
                                    let decimals_obj = mapDecimals[pair.pairAddress] ? mapDecimals[pair.pairAddress] : {
                                        symbol:"",
                                        decimals: 0,
                                        v_key: 0
                                    };
                                    let decimals0_obj = mapDecimals[pair.token0] ? mapDecimals[pair.token0] : {
                                        symbol:"",
                                        decimals: 0,
                                        v_key: 0
                                    };
                                    let decimals1_obj = mapDecimals[pair.token1] ? mapDecimals[pair.token1] : {
                                        symbol:"",
                                        decimals: 0,
                                        v_key: 0
                                    };
                                    let obj = await contractFunction('0x' + pair.pairAddress, 'pair');
                                    pair.chain_id = 56;
                                    pair.symbol = decimals_obj.symbol;
                                    pair.token0_addr = '0x' + pair.token0;
                                    pair.token1_addr = '0x' + pair.token1;
                                    if (main_token.includes(pair.token0_addr)) {//如果是特殊币打对则取辅币为主币
                                        pair.token_addr = pair.token1_addr;
                                        pair.token_index = 1;
                                    }
                                    if (main_token.includes(pair.token1_addr)) {
                                        pair.token_addr = pair.token0_addr;
                                        pair.token_index = 0;
                                    }
                                    if (!pair.token_addr) {//如果交易对没有特殊币则以时间最新的为主币
                                        if (decimals0_obj.v_key > decimals1_obj.v_key) {
                                            pair.token_addr = pair.token0_addr;
                                            pair.token_index = 0;
                                        } else {
                                            pair.token_addr = pair.token1_addr;
                                            pair.token_index = 1;
                                        }
                                    }
                                    pair.decimals = decimals_obj.decimals;
                                    pair.total_supply = obj.totalSupply;
                                    if (reserves) {
                                        pair.reserve0 = reserves.reserve0;
                                        pair.reserve1 = reserves.reserve1;
                                    } else {
                                        pair.reserve0 = 0;
                                        pair.reserve1 = 0;
                                    }
                                    delete pair.pairAddress;
                                    delete pair.token0;
                                    delete pair.token1;
                                    resolve(pair);
                                } else {
                                    resolve(undefined);
                                }
                            } catch (ex) {
                                reject(ex);
                            }
                        }));
                        if (threadp.length >= threadCount) {
                            let results = await Promise.allSettled(threadp);
                            for (let item of results) {
                                if (item.status == "fulfilled") {
                                    if (item.value) {
                                        await redis.hSet(FirstAnalyzeEnum.pair_json_hdata + "_v2", item.value.address, JSON.stringify(item.value));
                                    }
                                }
                            }
                            threadp.length = 0;
                        }
                    }
                    if (threadp.length > 0) {
                        let results = await Promise.allSettled(threadp);
                        for (let item of results) {
                            if (item.status == "fulfilled") {
                                if (item.value) {
                                    await redis.hSet(FirstAnalyzeEnum.pair_json_hdata + "_v2", item.value.address, JSON.stringify(item.value));
                                }
                            }
                        }
                        threadp.length = 0;
                    }
                    console.log("TokenAndPairTask:pair:" + v_key_pair.min + ":time=" + (new Date().getTime() - run_time));
                }

                /**
                 * token
                 */
                if (listTokens.length > 0) {
                    let threadp_2 = [];
                    for (let i = 0; i < listTokens.length; i++) {
                        threadp_2.push(new Promise(async (resolve, reject) => {
                            try {
                                let token = listTokens[i];
                                token.address = '0x' + token.tokenAddress;
                                let cache_token = await redis.hGet(FirstAnalyzeEnum.token_json_hdata + "_v2", token.address);
                                if (!cache_token) {
                                    let obj = await contractFunction('0x' + token.tokenAddress, 'token');
                                    token.chain_id = 56;
                                    // token.name
                                    if (!token.symbol || token.symbol == "") {
                                        token.symbol = token.name;
                                    }
                                    token.logo_url = "/image/" + token.address + ".png";
                                    token.total_supply = obj.totalSupply;
                                    //token.decimals
                                    delete token.tokenAddress;
                                    resolve(token);
                                } else {
                                    resolve(undefined);
                                }
                            } catch (ex) {
                                reject();
                            }
                        }));
                        if (threadp_2.length >= threadCount) {
                            let results = await Promise.allSettled(threadp_2);
                            for (let item of results) {
                                if (item.status == "fulfilled") {
                                    if (item.value) {
                                        await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", item.value.address, JSON.stringify(item.value));
                                    }
                                }
                            }
                            threadp_2.length = 0;
                        }
                    }
                    //处理剩余数据
                    if (threadp_2.length > 0) {
                        let results = await Promise.allSettled(threadp_2);
                        for (let item of results) {
                            if (item.status == "fulfilled") {
                                if (item.value) {
                                    await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", item.value.address, JSON.stringify(item.value));
                                }
                            }
                        }
                        threadp_2.length = 0;
                    }
                    console.log("TokenAndPairTask:token:" + v_key_token.min + ":time=" + (new Date().getTime() - run_time));
                }
            }
            //重新赋值vkey
            await redis.set(FirstAnalyzeEnum.pair_task_vKey, pair_max_vKey);//写入最新监控的pair位置
            //重新赋值vkey
            await redis.set(FirstAnalyzeEnum.token_task_vKey, token_max_vKey);//写入最新监控的token位置
            await delay(10000);
            continue;
        } catch (e) {
            console.error("解析报错" + e);
        }
    }
}

/**
 * 调用合约方法
 * @param tokenAddrList
 * @returns {Promise<{}>}
 */
async function contractFunction(address, type) {
    let obj = {
        name: '',
        symbol: '',
        totalSupply: '0',
        decimals: '0'
    };
    let tokenContract = new Contract(address, tokenAbi, provider);
    if (type == "token") {
        try {
            obj.totalSupply = new Decimal(await tokenContract.totalSupply()).toFixed(); //总共发行量
        } catch (e) {
        }
    } else if (type == "pair") {
        try {
            obj.totalSupply = new Decimal(await tokenContract.totalSupply()).toFixed(); //总共发行量
        } catch (e) {
        }
    }
    return obj;
}

/**
 * 获取token
 * @param token
 * @param provider
 * @returns {Promise<null>}
 */
async function fix_json() {
    let cursor = 0;
    let cursor_now = await redis.get("task:fix_json");
    if (cursor_now) {
        cursor = Number(cursor_now);
    }
    console.log("开始修复fix_json:"+cursor);
    // let json_v2 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", "0xac37e12f00e0ce18a2cbdf574cb19017bb5e8108");
    // let json_v2_str = JSON.stringify(json_v2);
    // await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", "0xac37e12f00e0ce18a2cbdf574cb19017bb5e8108", json_v2);
    do {
        try {
            let reply = await redis.hScan(FirstAnalyzeEnum.token_json_hdata, cursor);
            let new_cursor = Number(reply[0]);
            let values = reply[1];
            for (let i = 1; i < values.length; i = i + 2) {
                let obj = JSON.parse(values[i]);
                if (obj['pair_address']) {
                    let json_v2 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata + "_v2", obj.address);
                    if (json_v2) {
                        json_v2 = JSON.parse(json_v2);
                        json_v2['pair_address'] = obj['pair_address'];
                        let json_v2_str = JSON.stringify(json_v2);
                        await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", obj.address, json_v2_str);
                    } else {
                        let json_v2_str = JSON.stringify(obj);
                        await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", obj.address, json_v2_str);
                    }
                }
            }
            if (cursor <= 0) {
                break;
            }
            cursor = new_cursor
            await redis.set("task:fix_json",cursor);
        } catch (e) {
            console.log("异常终止" + e);
            break;
        }
    } while (true);
    console.log("完成修复fix_json");
}

// fix_json();
start();
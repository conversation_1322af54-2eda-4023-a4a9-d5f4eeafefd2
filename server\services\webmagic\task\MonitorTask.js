const FirstAnalyzeEnum = require("../../../utils/enums/AnalyzeDorisKeyEnum");
const {redis, errorToRedis} = require("../../../utils/redisUtils");
const {default: BigNumber} = require("bignumber.js");
const TradeTypeEnum = require("../../../utils/enums/TradeTypeEnum");
const MONITOR_insertList = "monitor:insertList";
const {PqueryQL_sonar, HQL_sonar} = require("../../../database/PqueryQL/PostgresSQL");
const {formatStrDecimal} = require("../../../utils/BaseDataUtils");
const init_vKey = 486034620010799;//省缺值
const MONITOR_lastBlock = "monitor:lastBlock";//记录最后监控到的区块位置
const cron = require('node-cron');
/**
 * 定时任务
 */
cron.schedule('*/1 * * * *', function () {
    swapMonitor();
    transferMonitor();
    liquiditysMonitor();
});

/**
 * 获取当前监控的最后的key
 */
async function getNowKey(tag) {
    let vKey = await redis.hGet(MONITOR_lastBlock, tag);
    if (!vKey) {
        vKey = init_vKey;//省缺值
    }
    vKey = Number(vKey) - 10000000000;//往前退100个区块(v_key是通过(block_number * 10000000) + block_index组合而成)
    if (vKey < 0) {
        vKey = 0;
    }
    return vKey;
}

/**
 * 写入当前监控的最后的key
 */
async function setNowKey(tag, key) {
    await redis.hSet(MONITOR_lastBlock, tag, key);
}

let lock_transfer = true;

async function transferMonitor() {
    if (lock_transfer) {
        lock_transfer = false;
        try {
            let nowKey = await getNowKey("transfer");
            let arg = `WITH
                monitor_transfers AS ( 
                     select address,from,to,value,toUnixTimestamp(datetime) as time,v_key
                     from MV_transfers_monitor FINAL
                     where v_key > ${nowKey}
                     limit 10000
                )
                SELECT toJSONString(map(
                    'type','transfers',
                    'address',toString(address),
                    'from',toString(from),
                    'to',toString(to),
                    'value',toString(value),
                    'time',toString(time),
                    'v_key',toString(v_key)
                )) AS json FROM monitor_transfers`;
            let data = await HQL_sonar(arg);
            let transfersList = [];
            let v_key_max = 0;
            if (data && data.length > 0) {
                console.log("transfersMonitor:" + nowKey);
                for (let i = 0; i < data.length; i++) {
                    let json = data[i].json;
                    let jsonColumn = JSON.parse(json);

                    switch (jsonColumn['type']) {
                        case "transfers":
                            transfersList.push(jsonColumn);
                            let v_key = Number(jsonColumn['v_key']);
                            if (v_key_max < v_key) {
                                v_key_max = v_key;
                            }
                            break;
                    }
                }
            } else {
                lock_transfer = true;
                return;
            }
            let threadp = [];
            let insertList = [];
            for (let i = 0; i < transfersList.length; i++) {
                threadp.push(new Promise(async (resolve, reject) => {
                    try {
                        let newInfo;
                        let tokenAddress = "0x" + transfersList[i].address;
                        let token = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", tokenAddress);
                        if (!token) {
                            throw Error("未找到token");
                        } else {
                            token = JSON.parse(token);
                        }
                        //获取价格
                        // let price = await getTokenPsqlUsdt(token.address);
                        //计算转账的实际数量
                        let quantityNum = formatStrDecimal(transfersList[i].value, token.decimals);
                        //计算币本位
                        newInfo = {
                            block_time: parseInt(transfersList[i].time),
                            quantity: quantityNum,
                            price: 0,
                            monitor_type: "trans",
                            token_address: token.address,
                        }
                        resolve(JSON.stringify(newInfo));
                    } catch (ex) {
                        reject(ex);
                    }
                }));
                if (threadp.length >= 100) {
                    let results = await Promise.allSettled(threadp);
                    for (let item of results) {
                        if (item.status == "fulfilled") {
                            if (item.value) {
                                insertList.push(item.value);
                            }
                        }
                    }
                    threadp.length = 0;
                }
            }
            if (threadp.length > 0) {
                let results = await Promise.allSettled(threadp);
                for (let item of results) {
                    if (item.status == "fulfilled") {
                        if (item.value) {
                            insertList.push(item.value);
                        }
                    }
                }
                threadp.length = 0;
            }
            //如果数据量过大，在最后统一插入
            if (insertList.length > 0) {
                await redis.rPush(MONITOR_insertList, insertList)
            }
            if (v_key_max > 0) {
                //更新最新监控的区块号
                await setNowKey("transfer", v_key_max);
                //删除历史数据
                // await HQL_sonar(`ALTER TABLE MV_transfers_monitor DELETE WHERE v_key < ${nowKey}`);
                await HQL_sonar(`alter table MV_transfers_monitor delete where v_key < ${nowKey}`);
            }

        } catch (e) {
            console.error("transfersMonitor_error:" + e)
        }
    }
    lock_transfer = true;
}

let lock_swaps = true;

async function swapMonitor() {
    // while (true) {
    if (lock_swaps) {
        lock_swaps = false;
        try {
            let nowKey = await getNowKey("swap");
            let arg = `WITH
                monitor_swaps AS ( 
                     select contract,sender,to,amount0_in,amount0_out,amount1_in,amount1_out,toUnixTimestamp(datetime) as time,v_key
                     from MV_swaps_monitor FINAL
                     where v_key > ${nowKey}
                     limit 10000
                )
                SELECT toJSONString(map(
                    'type','swaps',
                    'contract',toString(contract),
                    'sender',toString(sender),
                    'to',toString(to),
                    'amount0_in',toString(amount0_in),
                    'amount0_out',toString(amount0_out),
                    'amount1_in',toString(amount1_in),
                    'amount1_out',toString(amount1_out),
                    'time',toString(time),
                    'v_key',toString(v_key)
                )) AS json FROM monitor_swaps`;
            let data = await HQL_sonar(arg);
            let swapsList = [];
            let v_key_max = 0;
            if (data && data.length > 0) {
                console.log("swapMonitor:" + nowKey);
                for (let i = 0; i < data.length; i++) {
                    let json = data[i].json;
                    let jsonColumn = JSON.parse(json);

                    switch (jsonColumn['type']) {
                        case "swaps":
                            swapsList.push(jsonColumn);
                            let v_key = Number(jsonColumn['v_key']);
                            if (v_key_max < v_key) {
                                v_key_max = v_key;
                            }
                            break;
                    }
                }
            } else {
                lock_swaps = true;
                return;
            }
            let threadp = [];
            let insertList = [];
            for (let i = 0; i < swapsList.length; i++) {
                threadp.push(new Promise(async (resolve, reject) => {
                    try {
                        let newInfo;
                        // if (monitorTokenList.indexOf(pair.token_addr) >= 0) {
                        //
                        // }
                        // swapsList[i].contract = "0xda0c9c5a3440d2b839d1495a2723e51e1a93039e";
                        let pairAddress = "0x" + swapsList[i].contract;
                        let pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata+"_v2", pairAddress);
                        if (pair) {
                            pair = JSON.parse(pair);

                            let token0 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", pair.token0_addr);
                            if (!token0) {
                                throw Error("未找到token0");
                            } else {
                                token0 = JSON.parse(token0);
                            }
                            let token1 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", pair.token1_addr);
                            if (!token1) {
                                throw Error("未找到token1");
                            } else {
                                token1 = JSON.parse(token1);
                            }
                            let type;
                            let fromAmount = 0;
                            let toAmount = 0;
                            let amount = 0;
                            let amountUsd = 0;
                            let amount0_In = new BigNumber(0);
                            let amount1_Out = new BigNumber(0);
                            let amount0In = '0';
                            let amount0Out = '0';
                            let amount1In = '0';
                            let amount1Out = '0';
                            if (swapsList[i].amount0_in == null || swapsList[i].amount0_in == undefined || swapsList[i].amount0_in == '') {

                            } else {
                                amount0_In = new BigNumber(swapsList[i].amount0_in);
                                amount0In = swapsList[i].amount0_in;
                            }
                            if (swapsList[i].amount1_in == null || swapsList[i].amount1_in == undefined || swapsList[i].amount1_in == '') {

                            } else {
                                amount1In = swapsList[i].amount1_in;
                            }
                            if (swapsList[i].amount0_out == null || swapsList[i].amount0_out == undefined || swapsList[i].amount0_out == '') {

                            } else {
                                amount0Out = swapsList[i].amount0_out;
                            }
                            if (swapsList[i].amount1_out == null || swapsList[i].amount1_out == undefined || swapsList[i].amount1_out == '') {

                            } else {
                                amount1_Out = new BigNumber(swapsList[i].amount1_out);
                                amount1Out = swapsList[i].amount1_out;
                            }

                            if (pair.token_index == 0) {
                                //获取价格
                                // amountUsd = await getTokenPsqlUsdt(token0.address);
                                if (amount0_In.eq(0) || amount1_Out.eq(0)) {
                                    //当前币卖出
                                    type = TradeTypeEnum.BUY;//站在当前币的角度是卖出，但站在别人角度就是买
                                    toAmount = formatStrDecimal(amount0Out, token0.decimals);
                                    amount = toAmount;
                                } else {
                                    //当前币买入
                                    type = TradeTypeEnum.SELL;//站在当前币的角度是卖出，但站在别人角度就是卖
                                    fromAmount = formatStrDecimal(amount0In, token0.decimals);
                                    amount = fromAmount;
                                }
                            } else {
                                //获取价格
                                // amountUsd = await getTokenPsqlUsdt(token1.address);
                                if (amount0_In.eq(0) || amount1_Out.eq(0)) {
                                    //当前币买入
                                    type = TradeTypeEnum.SELL;//站在当前币的角度是卖出，但站在别人角度就是卖
                                    fromAmount = formatStrDecimal(amount1In, token1.decimals);
                                    amount = fromAmount;
                                } else {
                                    //当前币卖出
                                    type = TradeTypeEnum.BUY;//站在当前币的角度是卖出，但站在别人角度就是买
                                    toAmount = formatStrDecimal(amount1Out, token1.decimals);
                                    amount = toAmount;
                                }
                            }
                            newInfo = {
                                quantity: amount,
                                price: 0,//通过实际数量*金额
                                monitor_type: "trade_" + type,
                                block_time: parseInt(swapsList[i].time),
                                token_address: pair.token_addr //pair_address
                            }

                        }
                        resolve(JSON.stringify(newInfo));
                    } catch (ex) {
                        reject();
                    }
                }));
                if (threadp.length >= 100) {
                    let results = await Promise.allSettled(threadp);
                    for (let item of results) {
                        if (item.status == "fulfilled") {
                            if (item.value) {
                                insertList.push(item.value);
                            }
                        }
                    }
                    threadp.length = 0;
                }
            }
            if (threadp.length > 0) {
                let results = await Promise.allSettled(threadp);
                for (let item of results) {
                    if (item.status == "fulfilled") {
                        if (item.value) {
                            insertList.push(item.value);
                        }
                    }
                }
                threadp.length = 0;
            }
            //如果数据量过大，在最后统一插入
            if (insertList.length > 0) {
                await redis.rPush(MONITOR_insertList, insertList)
            }
            if (v_key_max > 0) {
                //更新最新监控的区块号
                await setNowKey("swap", v_key_max);
                //删除历史数据
                // await HQL_sonar(`ALTER TABLE MV_swaps_monitor DELETE WHERE v_key < ${nowKey}`);
                await HQL_sonar(`alter table MV_swaps_monitor delete where v_key < ${nowKey}`);
            }

        } catch (e) {
            console.error("swapMonitor_error:" + e)
        }
    }
    lock_swaps = true;
    // }
}


let lock_liquiditys = true;

async function liquiditysMonitor() {
    if (lock_liquiditys) {
        lock_liquiditys = false;
        try {
            let nowKey = await getNowKey("liquidity");
            let arg = `WITH
                monitor_liquiditys AS ( 
                     select address,sender,to,toUnixTimestamp(datetime) as time,mint,amount0,amount1,v_key
                     from MV_liquiditys_monitor FINAL
                     where v_key > ${nowKey}
                     limit 10000
                )
                SELECT toJSONString(map(
                    'type','liquiditys',
                    'address',toString(address),
                    'sender',toString(sender),
                    'to',toString(to),
                    'time',toString(time),
                    'mint',toString(mint),
                    'amount0',toString(amount0),
                    'amount1',toString(amount1),
                    'v_key',toString(v_key)
                )) AS json FROM monitor_liquiditys`;
            let data = await HQL_sonar(arg);
            let liquidityList = [];
            let v_key_max = 0;
            if (data && data.length > 0) {
                console.log("liquiditysMonitor:" + nowKey);
                for (let i = 0; i < data.length; i++) {
                    let json = data[i].json;
                    let jsonColumn = JSON.parse(json);

                    switch (jsonColumn['type']) {
                        case "liquiditys":
                            liquidityList.push(jsonColumn);
                            let v_key = Number(jsonColumn['v_key']);
                            if (v_key_max < v_key) {
                                v_key_max = v_key;
                            }
                            break;
                    }
                }
            } else {
                lock_liquiditys = true;
                return;
            }
            let threadp = [];
            let insertList = [];
            for (let i = 0; i < liquidityList.length; i++) {
                threadp.push(new Promise(async (resolve, reject) => {
                    try {
                        let newInfo;
                        // if (monitorTokenList.indexOf(pair.token_addr) >= 0) {
                        //
                        // }
                        // swapsList[i].contract = "0xda0c9c5a3440d2b839d1495a2723e51e1a93039e";
                        let pairAddress = "0x" + liquidityList[i].address;
                        let pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata+"_v2", pairAddress);
                        if (pair) {
                            pair = JSON.parse(pair);
                            let token0 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", pair.token0_addr);
                            if (!token0) {
                                throw Error("未找到token0");
                            } else {
                                token0 = JSON.parse(token0);
                            }
                            let token1 = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", pair.token1_addr);
                            if (!token1) {
                                throw Error("未找到token1");
                            } else {
                                token1 = JSON.parse(token1);
                            }
                            let token_address = pair.token_addr;
                            let amount0 = formatStrDecimal(liquidityList[i].amount0, token0.decimals);
                            let amount1 = formatStrDecimal(liquidityList[i].amount1, token1.decimals);
                            let amount;
                            let amount_usd;
                            if (pair.token_index == 0) {
                                amount = new BigNumber(amount0);
                                // amount_usd = await getTokenPsqlUsdt(token0.address);
                            } else {
                                amount = new BigNumber(amount1);
                                // amount_usd = await getTokenPsqlUsdt(token1.address);
                            }
                            let type;
                            if (liquidityList[i].mint) {//如果是true则mint
                                type = "mint";
                            } else {//否则burn
                                type = "burn";
                            }
                            newInfo = {
                                block_time: parseInt(liquidityList[i].time),
                                quantity: amount.toFixed(),
                                price: 0,//通过实际数量*金额
                                monitor_type: type,
                                token0Amount: amount0,
                                token1Amount: amount1,
                                token0Symbol: token0.symbol,
                                token1Symbol: token1.symbol,
                                token_address: token_address
                            }
                        }
                        resolve(JSON.stringify(newInfo));
                    } catch (ex) {
                        reject();
                    }
                }));
                if (threadp.length >= 100) {
                    let results = await Promise.allSettled(threadp);
                    for (let item of results) {
                        if (item.status == "fulfilled") {
                            if (item.value) {
                                insertList.push(item.value);
                            }
                        }
                    }
                    threadp.length = 0;
                }
            }
            if (threadp.length > 0) {
                let results = await Promise.allSettled(threadp);
                for (let item of results) {
                    if (item.status == "fulfilled") {
                        if (item.value) {
                            insertList.push(item.value);
                        }
                    }
                }
                threadp.length = 0;
            }
            //如果数据量过大，在最后统一插入
            if (insertList.length > 0) {
                await redis.rPush(MONITOR_insertList, insertList)
            }
            //更新最新监控的区块号
            if (v_key_max > 0) {
                await setNowKey("liquidity", v_key_max);
                //删除历史数据
                // await HQL_sonar(`ALTER TABLE MV_liquiditys_monitor DELETE WHERE v_key < ${nowKey}`);
                await HQL_sonar(`alter table MV_liquiditys_monitor delete where v_key < ${nowKey}`);
            }

        } catch (e) {
            console.error("liquiditysMonitor_error:" + e)
        }
    }
    lock_liquiditys = true;
}



let https = require("https");
let fs = require("fs");

/**
 * 判断地址是否存在有效图片
 * @param imgFilePath
 */
function img_isExist(imgFilePath) {
    if (!imgFilePath) {
        return false;
    }
    if (fs.existsSync(imgFilePath)) {
        // 文件存在
        let stats = fs.statSync(imgFilePath);
        if (stats.size < 1) {
            //如果文件字节为0说明是无效文件删除
            fs.unlinkSync(imgFilePath);
            return false;
        } else {
            //如果是有效文件则直接结束无需访问获取
            return true;
        }
    }
    return false;
}
/**
 * ave下载图片
 * @param img_url
 * @param data
 * @param resolve
 * @param reject
 */
function img_url_upload_ave(imgUrl, imgFilePath) {
    if (!imgUrl || imgUrl == "" || !imgFilePath || imgFilePath == "") {
        return;
    }
    // let imgPath = "./image/" + address + ".png";
    if (fs.existsSync(imgFilePath)) {
        // 文件存在
        const stats = fs.statSync(imgFilePath);
        if (stats.size < 1) {
            //如果文件字节为0说明是无效文件删除
            fs.unlinkSync(imgFilePath);
        } else {
            //如果是有效文件则直接结束无需访问获取
            return;
        }
    }
    const options2 = {
        hostname: "www.imagecf.com",
        path: "/" + imgUrl,
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        timeout: 60000 // 设置超时时间为5秒
    };
    const req = https.get(options2, (res) => {
        try {
            const fileStream = fs.createWriteStream(imgFilePath);
            res.pipe(fileStream);
            let html = "";
            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', function () {
            });
            res.on('error', function (e) {
                console.error("imgUrlTask:error:", e);
            });
        } catch (e) {
            console.error("imgUrlTask:error:", e);
        }
    });
    req.on('error', (e) => {
        console.error(e);
    });
    req.end();
}

/**
 * api下载图片
 * @param img_url
 * @param data
 * @param resolve
 * @param reject
 */
function img_url_upload(img_url, imgFilePath) {
    if (!img_url || img_url == "" || !imgFilePath || imgFilePath == "") {
        return;
    }
    imgFilePath = imgFilePath.toLowerCase();
    if (fs.existsSync(imgFilePath)) {
        // 文件存在
        const stats = fs.statSync(imgFilePath);
        if (stats.size < 1) {
            //如果文件字节为0说明是无效文件删除
            fs.unlinkSync(imgFilePath);
        } else {
            //如果是有效文件则直接结束无需访问获取
            return;
        }
    }
    const options2 = {
        hostname: 'www.logofacade.com', path: '/' + img_url, agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        timeout: 60000 // 设置超时时间为5秒
    };
    const req = https.get(options2, (res) => {
        const fileStream = fs.createWriteStream(imgFilePath);
        res.pipe(fileStream);
        res.on('end', function () {
        });
        res.on('error', function (e) {
            console.error(e);
        });
    });
    req.on('error', (e) => {
        console.error(e);
    });
    req.end();
}

/**
 * 获取公链bsc图片
 * @param img_url
 * @param imgFilePath
 */

// img_url_uploadChain("chain/bsc.png","../../../image/chain/bsc.png");
function img_url_uploadChain(img_url, imgFilePath) {
    if (!img_url || img_url == "" || !imgFilePath || imgFilePath == "") {
        return;
    }
    imgFilePath = imgFilePath.toLowerCase();
    if (fs.existsSync(imgFilePath)) {
        // 文件存在
        const stats = fs.statSync(imgFilePath);
        if (stats.size < 1) {
            //如果文件字节为0说明是无效文件删除
            fs.unlinkSync(imgFilePath);
        } else {
            //如果是有效文件则直接结束无需访问获取
            return;
        }
    }
    const options2 = {
        hostname: 'www.loogooff.com', path: '/' + img_url, agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        timeout: 60000 // 设置超时时间为5秒
    };
    const req = https.get(options2, (res) => {
        const fileStream = fs.createWriteStream(imgFilePath);
        res.pipe(fileStream);
        res.on('end', function () {
        });
        res.on('error', function (e) {
            console.error(e);
        });
    });
    req.on('error', (e) => {
        console.error(e);
    });
    req.end();
}

module.exports = {
    img_url_upload, img_url_upload_ave,img_isExist
}




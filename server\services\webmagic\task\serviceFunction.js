let https = require("https");
let fs = require("fs");
const {formatTimestamp, getTimeStamp, formatTimestampUTC} = require("../../../utils/DateUtils")
const {default: BigNumber} = require("bignumber.js");
BigNumber.config({DECIMAL_PLACES: 100});
const {getHeaders} = require("../config/httpHead")
const redis = require("../../../database/redis");
const PeriodEnum = require("../../../utils/enums/PeriodEnum");
const {img_url_upload, img_url_upload_ave} = require("./imgUpload");
const {formatAddress} = require("../../../utils/functions");
const ave_url = "api.eskegs.com";
function decode(encode_data) {
    return JSON.parse(global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")));
}

async function day_kTask(pairAddress, result) {
    if (!pairAddress) {
        result({
            code: 400, msg: 'address参数不能为空', data: null
        })
        return;
    }
    const options = {
        hostname: ave_url,
        path: '/v1api/v3/pairs/' + pairAddress + '-bsc' + '/kline?interval=1440&category=u&count=800',
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        headers: await getHeaders(),
        timeout: 60000 // 设置超时时间为5秒
    };

    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let data = [];
                let jsonData = JSON.parse(html);
                let encode_data = jsonData['encode_data'];
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    result({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (!encode_data || encode_data == "") {
                    console.log("encode_data为空");
                    result({
                        code: 200, msg: "数据为空！", data: null
                    });
                    return;
                }
                let de_data = decode(encode_data)
                let kline_data = de_data['kline_data'];
                let item;

                for (let i = 0; i < kline_data.length; i++) {
                    item = kline_data[i];
                    data.push(item.close);
                    delete item.open;
                    delete item.high;
                    delete item.low;
                    delete item.volume;
                }
                redis.hSet("webmagic_api:day_k", pairAddress, JSON.stringify(data));
                result({
                    code: 200, msg: "请求成功！", data: data
                })
            } catch (e) {
                result({
                    code: 500, msg: e, data: null
                })
            }
        });
        res.on('error', function (e) {
            result({
                code: 500, msg: e, data: null
            })
        });
    });
    req.on('error', (e) => {
        result({
            code: 500, msg: e, data: null
        })
    });
    req.end();
}

async function getHolderTask(request, result) {
    if (!request.query.address) {
        result({
            code: 400, msg: 'address参数不能为空', data: null
        })
        return;
    }
    try {
        let response = {};
        let thread = [];
        let chipsSort = [];
        let chipsListSort = [];
        thread.push(new Promise((r2, j2) => {
            chips_time(request.query.address, function (call) {
                if (call.code == 200) {
                    if (call.data) {
                        chipsSort = call.data;//持币人集合（day）
                    }
                    r2();
                } else {
                    console.info("获取持币人折线图失败", call.msg);
                    j2(call.msg);
                }
            });
        }));
        //持币人表格排序集合
        thread.push(new Promise((r2, j2) => {
            chips(request.query.address, function (call) {
                if (call.code == 200) {
                    if (call.data) {
                        chipsListSort = call.data;//持币人集合（day）
                        r2();
                    }
                } else {
                    console.info("获取持币人表格失败", call.msg);
                    j2(call.msg);
                }
            });
        }));

        Promise.all(thread).then(() => {
            //持币人
            let totalAmount = 0;
            if (chipsSort) {
                response['chips'] = chipsSort;
                totalAmount = chipsSort.totalAmount;
            }
            //持币人表格
            if (chipsListSort.length > 0) {
                chipsListSort.forEach((item) => {
                    item.proportion = (totalAmount == 0) ? "-" : Number(item.amount_cur / totalAmount * 100).toFixed(2) + "%";
                });
                response['chipsList'] = chipsListSort;
            }
            result({
                code: 200, msg: '请求成功！', data: response
            })
            redis.hSet("webmagic_api:getHolder", request.query.address, JSON.stringify(response));
        }, e => {
            result({
                code: 500, msg: e, data: null
            })
        });
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

async function getPairBaseTask(request, result) {
    try {
        if (!request.query.address) {
            result({
                code: 400, msg: 'address参数不能为空', data: null
            })
            return;
        }
        let url = '/v1api/v3/tokens/' + request.query.address + "-bsc"
        const options = {
            hostname: ave_url, path: url, agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        let response = {};
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', function () {
                try {
                    let mysqlObjs = [];
                    let thread = [];
                    let jsonData = JSON.parse(html);
                    if (!jsonData['data']) {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let data;
                    if(typeof jsonData['data'] === 'string'){
                        data = JSON.parse(jsonData['data']);
                    }else{
                        data = jsonData['data'];
                    }
                    response['topTitle'] = [];//头部标题
                    response['pairs'] = [];//池子表格
                    response['volumes'] = {};//交易表格
                    response['liquidities'] = {};//流动性折线图
                    response['liquiditieList'] = {};//流动性表格
                    response['chips'] = [];//持币人折线图
                    response['chipsList'] = [];//持币人表格
                    let chipsSort;//持币人排序集合
                    let chipsListSort = [];//持币人表格排序集合
                    if (data['pairs']) {
                        for (let i = 0; i < data['pairs'].length; i++) {
                            let item = data['pairs'][i];
                            response['liquidities'][item.pair] = {x: [], y: []};//初始化每个池子的流动性
                            response['liquiditieList'][item.pair] = [];//初始化流动性表格
                            let pair0 = {};
                            // top_title_k(item.pair, pair0);
                            pair0['txVolume24h'] = item.volume_u;//24H额
                            pair0['money'] = "-";//流通市值
                            pair0['txCount24h'] = item.tx_count;//24H交易数
                            pair0['high24h'] = item.high_u;//24H高
                            pair0['turnover24h'] = "-";//24H换手
                            pair0['low24h'] = item.low_u;//24H低
                            pair0['roi'] = "-";//投资回报率
                            pair0['txAmount24h'] = "-";//24H量
                            pair0['chain'] = item.chain;//所属公链
                            pair0['address'] = item.pair;
                            pair0['currentPriceUsd'] = data['token'].current_price_usd;//当前价格
                            pair0['priceChange'] = data['token'].price_change;//价格变动
                            pair0['tokenAddress'] = data['token'].token;//token地址
                            pair0['holders'] = data['token'].holders;//持有人
                            pair0['totalSupply'] = data['token'].total;//总量，可增发
                            pair0['openPrice'] = data['token'].open_price;//开盘价
                            response['topTitle'].push(pair0);

                            let pair1 = {};
                            pair1['address'] = item.pair;
                            let token_price_usd;
                            if (item.target_token.includes(item.token0_address)) {
                                pair1['symbol0'] = item.token0_symbol;//币1名称
                                pair1['symbol1'] = item.token1_symbol;//币2名称
                                pair1['reserve0'] = item.reserve0;//币1数量
                                pair1['reserve1'] = item.reserve1;//币2数量
                                token_price_usd = item.token1_price_usd;//主币U价格
                            } else {
                                pair1['symbol0'] = item.token1_symbol;
                                pair1['symbol1'] = item.token0_symbol;
                                pair1['reserve0'] = item.reserve1;
                                pair1['reserve1'] = item.reserve0;
                                token_price_usd = item.token0_price_usd;//主币U价格
                            }
                            if (token_price_usd) {
                                /**
                                 * 计算当前池子总流动性(取辅币来算更准确，因为数量更多)
                                 */
                                pair1['liquiditie'] = new BigNumber(pair1['reserve1']).multipliedBy(new BigNumber(token_price_usd)).multipliedBy(2).toFixed(3);//流动性总额
                                if (pair0['txVolume24h'] && pair0['currentPriceUsd']) {
                                    /**
                                     * 计算当前池子24H总量
                                     */
                                    // let qute = new BigNumber(pair1['reserve1']).div(pair1['reserve0']);
                                    // let currentPriceUsd = qute.multipliedBy(token_price_usd);//当前价格
                                    pair0['txAmount24h'] = new BigNumber(pair0['txVolume24h']).div(pair0['currentPriceUsd']).toFixed(3);
                                    pair0['txVolume24h'] = new BigNumber(pair0['txVolume24h']).toFixed(2);
                                }
                            }
                            pair1['showName'] = item.show_name;//池子类型名称

                            /**
                             * 流动性获取
                             */
                            thread.push(new Promise((r2, j2) => {
                                let liquiditiesSort = [];//流动性排序集合
                                lq(item.pair, function (call) {
                                    if (call.code == 200) {
                                        if (call.data) {
                                            call.data.forEach((liquiditie) => {//池子流动性集合（day）
                                                let dateformat = formatTimestamp(liquiditie.time)
                                                let dataObj2 = {
                                                    time: dateformat,
                                                    type: liquiditie.type,
                                                    t: liquiditie.time,
                                                    token0Symbol: liquiditie.token0_symbol,
                                                    token1Symbol: liquiditie.token1_symbol,
                                                    amount0: liquiditie.amount0,
                                                    amount1: liquiditie.amount1,
                                                    amountUsd: liquiditie.amount_usd,
                                                    walletAddress: liquiditie.wallet_address
                                                }
                                                dataObj2.amountUsdPair = new BigNumber(dataObj2.amountUsd).multipliedBy(2).toFixed(2);//计算当前时刻池子价格
                                                liquiditiesSort.push(dataObj2);
                                            });
                                            //流动性
                                            if (liquiditiesSort.length > 0) {
                                                liquiditiesSort.sort((a, b) => b.t - a.t);
                                                liquiditiesSort.forEach((lq) => {
                                                    response['liquidities'][item.pair]['x'].push(lq.time);
                                                    response['liquidities'][item.pair]['y'].push(lq.amountUsd);
                                                    response['liquiditieList'][item.pair].push(lq);
                                                });
                                            }
                                        }
                                        r2();
                                    } else {
                                        console.info("流动性获取失败", call.msg);
                                        j2(call.msg);
                                    }
                                });
                            }));
                            /**
                             * 交易记录获取
                             */
                            thread.push(new Promise((r2, j2) => {
                                let transSort = [];//交易排序集合
                                transaction(item.pair, function (call) {
                                    if (call.code == 200) {
                                        if (call.data) {
                                            call.data.forEach((trans) => {//交易集合（day）
                                                let dateformat = formatTimestamp(trans.time)
                                                let dataObj2;
                                                if (data['token'].token.includes(trans.from_address)) {
                                                    dataObj2 = {
                                                        type: "from",
                                                        t: trans.time,
                                                        transactionTime: dateformat,
                                                        fromPriceUsd: trans.from_price_usd,
                                                        amount: trans.from_amount,
                                                        amountUsd: trans.amount_usd,
                                                        walletAddress: trans.wallet_address
                                                    }
                                                } else {
                                                    dataObj2 = {
                                                        type: "to",
                                                        t: trans.time,
                                                        transactionTime: dateformat,
                                                        fromPriceUsd: trans.to_price_usd,
                                                        amount: trans.to_amount,
                                                        amountUsd: trans.amount_usd,
                                                        walletAddress: trans.wallet_address
                                                    }
                                                }
                                                transSort.push(dataObj2);
                                            });
                                            //交易
                                            if (transSort.length > 0) {
                                                transSort.sort((a, b) => b.t - a.t);
                                                response['volumes'][item.pair] = transSort;
                                            }
                                        }
                                        r2();
                                    } else {
                                        console.info("交易获取失败", call.msg);
                                        j2(call.msg);
                                    }
                                });
                            }));
                            response['pairs'].push(pair1);
                        }
                        /**
                         * 持币人获取
                         */
                        thread.push(new Promise((r2, j2) => {
                            chips_time(data['token'].token, function (call) {
                                if (call.code == 200) {
                                    if (call.data) {
                                        chipsSort = call.data;//持币人集合（day）
                                    }
                                    r2();
                                } else {
                                    console.info("获取持币人折线图失败", call.msg);
                                    j2(call.msg);
                                }
                            });
                        }));
                        thread.push(new Promise((r2, j2) => {
                            chips(data['token'].token, function (call) {
                                if (call.code == 200) {
                                    if (call.data) {
                                        chipsListSort = call.data;//持币人集合（day）
                                        r2();
                                    }
                                } else {
                                    console.info("获取持币人表格失败", call.msg);
                                    j2(call.msg);
                                }
                            });
                        }));
                    }
                    let imgPath = "/image/" + data['token'].token + ".png";
                    let mysqlObj = {
                        chain_id: 56,
                        address: formatAddress(data["token"].token),
                        logo_url: imgPath,
                        name: data["token"].name,
                        symbol: data["token"].symbol,
                        total_supply: data["token"].total,
                        decimals: data["token"].decimal,
                        holders: data["token"].holders,
                        is_open_source: 1,
                        current_price_usd: data["token"].current_price_usd,
                        price_change: data["token"].price_change,
                        risk_score: data["token"].risk_score,
                        status: 1,
                        publish_time: data["token"].publish_at,
                    };
                    //创建代币
                    // if (!tokenRedis) {
                    //     redis.hSet("token_json_hdata", mysqlObj.address, JSON.stringify(mysqlObj));
                    // }
                    thread.push(new Promise((r, j) => {
                        let logo_url = data['token']['logo_url'];
                        let imgFilePath = "." + imgPath;
                        img_url_upload(logo_url, imgFilePath);
                        response['imgUrl'] = imgPath;
                        r();
                    }));
                    Promise.all(thread).then(() => {
                        //持币人
                        let totalAmount = 0;
                        if (chipsSort) {
                            response['chips'] = chipsSort;
                            totalAmount = chipsSort.totalAmount;
                        }
                        //持币人表格
                        if (chipsListSort.length > 0) {
                            chipsListSort.forEach((item) => {
                                item.proportion = (totalAmount == 0) ? "-" : Number(item.amount_cur / totalAmount * 100).toFixed(2) + "%";
                            });
                            response['chipsList'] = chipsListSort;
                        }
                        redis.hSet("webmagic_api:getPairBase", request.query.address, JSON.stringify(response));
                        result({
                            code: 200, msg: '请求成功！', data: response
                        });
                    }, e => {
                        result({
                            code: 500, msg: e, data: null
                        })
                    });
                } catch (e) {
                    result({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

async function Main_chipsTask(request, result) {
    try {
        let params = request.body
        if (!params.token_address) {
            result({
                code: 400, msg: 'token_address参数不能为空', data: null
            })
            return;
        }
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/stats/top100balance?token_id=' + params.token_address + '-bsc&size=100&address=',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['data_type'] == 2) {
                        let decoded = decode(jsonData['encode_data'])
                        redis.hSet("webmagic_api:Main_chips", params.token_address, JSON.stringify(decoded));
                        result({
                            code: 200, msg: "成功！", data: decoded
                        })
                    } else {
                        result({
                            code: 200, msg: '持币量未知！', data: null
                        })
                    }
                } catch (e) {
                    result({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

async function chips_time(token, call) {
    try {
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/stats/top100range?token_id=' + token + '-bsc',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        let data = []
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let map = {
                        time: [], data: {
                            top10: [], top20: [], top50: [], top100: []
                        },
                        totalAmount: 0
                    };
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        call({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['data_type'] == 2) {
                        data = decode(jsonData['encode_data'])
                        for (let i = 0; i < data.length; i++) {
                            let obj = data[i];
                            map.time.push(obj.date);
                            map.data.top10.push(obj.top10_amount);
                            map.data.top20.push(obj.top20_amount);
                            map.data.top50.push(obj.top50_amount);
                            map.data.top100.push(obj.top100_amount);
                            map.totalAmount = obj.total_amount;
                        }
                        call({
                            code: 200, msg: "成功！", data: map
                        });
                    } else {
                        call({
                            code: 200, msg: '排名时间未知！', data: null
                        });
                    }
                } catch (e) {
                    call({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                call({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch (e) {
        call({
            code: 500, msg: e, data: null
        })
    }
}

async function chips(token, call) {
    try {
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/stats/top100balance?token_id=' + token + '-bsc&size=100&address=',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let data = {
                        time: [], data: {
                            top10: [], top20: [], top50: [], top100: []
                        }
                    };
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        call({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['data_type'] == 2) {
                        let decoded = decode(jsonData['encode_data'])
                        for (let i = 0; i < decoded.length; i++) {
                            let obj = decoded[i];
                            if (!obj["tag"]) {
                                obj["tag"] = "";
                            }
                            obj["tag"] = obj["wallet_tag_v2"].replace(/,[0-9]*/, '');
                            delete obj["cost_cur"];
                            delete obj["cost_diff_3days"];
                            delete obj["sell_amount_cur"];
                            delete obj["sell_amount_diff_3days"];
                            delete obj["sell_volume_cur"];
                            delete obj["sell_volume_diff_3days"];
                            delete obj["buy_volume_cur"];
                            delete obj["buy_volume_diff_3days"];
                            delete obj["buy_amount_cur"];
                            delete obj["buy_amount_diff_3days"];
                            delete obj["buy_tx_count_cur"];
                            delete obj["sell_tx_count_cur"];
                            delete obj["trade_first_at"];
                            delete obj["trade_last_at"];
                            delete obj["wallet_tag_v2"];
                            delete obj["wallet_tag_extra"];
                            delete obj["is_wallet_address_fav"];
                        }
                        call({
                            code: 200, msg: "成功！", data: decoded
                        })
                    } else {
                        call({
                            code: 200, msg: '持币量未知！', data: null
                        })
                    }
                } catch (e) {
                    call({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                call({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch (e) {
        call({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 获取交易记录
 * @param pair
 * @param call
 */
async function transaction(pair, call) {
    try {
        let html2 = ''
        let data
        const options2 = {
            hostname: ave_url,
            path: '/v1api/v3/pairs/' + pair + '-bsc/txs?address=',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        const req = https.get(options2, function (res) {

            res.on('data', function (chunk) {
                html2 += chunk;
            });

            res.on('end', function () {
                let jsonData = JSON.parse(html2);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    call({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['data_type'] == 2) {
                    data = decode(jsonData['encode_data'])
                    call({
                        code: 200, msg: '成功！', data: data
                    })
                } else {
                    call({
                        code: 200, msg: '交易未知！', data: null
                    })
                }
            });
            res.on('error', function (e) {
                call({
                    code: 500, msg: e, data: null
                });
            });
        });
        req.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch (e) {
        call({
            code: 500, msg: e, data: null
        })
    }

}

/**
 * 查询流动性
 * @param pair
 * @returns {Promise<unknown>}
 */
async function lq(pair, call) {
    try {
        let html2 = ''
        let data
        const options2 = {
            hostname: ave_url, path: '/v1api/v3/pairs/' + pair + '-bsc/liq', agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        const req = https.get(options2, function (res) {

            res.on('data', function (chunk) {
                html2 += chunk;
            });

            res.on('end', function () {
                let jsonData = JSON.parse(html2);
                if (jsonData['msg'].indexOf("Authorization") > -1) {
                    console.log("认证过期！");
                    call({
                        code: 200,
                        msg: "认证过期！",
                        data: null
                    });
                    return;
                }
                if (jsonData['data_type'] == 2) {
                    data = decode(jsonData['encode_data'])
                    call({
                        code: 200, msg: '成功！', data: data
                    })
                } else {
                    call({
                        code: 200, msg: '流动性未知！', data: null
                    })
                }
            });
            res.on('error', function (e) {
                call({
                    code: 500, msg: e, data: null
                });
            });
        });
        req.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            })
        });
        req.end();
    } catch (e) {
        call({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 获取K线基本信息
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getklineWebTask(request, result) {
    try {
        let params = request.body
        if (!params.pairAddress || !params.period) {
            result({
                code: 400,
                msg: 'pairAddress,period参数不能为空',
                data: null
            })
            return;
        }
        let interval = 1;//默认一分钟
        switch (params.period) {
            case "1M":
                interval = 1;
                break;
            case "5M":
                interval = 5;
                break;
            case "15M":
                interval = 15;
                break;
            case "30M":
                interval = 30;
                break;
            case "1H":
                interval = 60;
                break;
            case "2H":
                interval = 120;
                break;
            case "4H":
                interval = 240;
                break;
        }
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=' + interval + '&category=u&count=800',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data'];
                    if (!encode_data || encode_data == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let de_data = decode(encode_data)
                    let kline_data = de_data['kline_data'];
                    let item;
                    for (let i = 0; i < kline_data.length; i++) {
                        // item.time = formatTimestamp(item.time);
                        item = kline_data[i];
                        item["id"] = i;
                        item["pairId"] = i;
                        item["open"] = item.open;
                        item["high"] = item.high;
                        item["low"] = item.low;
                        item["close"] = item.close;
                        item["volume"] = item.volume;
                        item["klineTime"] = item.time;
                        item["period"] = "1M";
                        item["openTag"] = "";
                        item["closeTag"] = "";
                        item["amplitude"] = 0;
                        item["tradeAmount"] = 0;
                        item["priceChange"] = 0;
                        item["changePct"] = 0;
                        item["tradeNum"] = 0;
                        delete item.time;
                    }
                    result({
                        code: 200,
                        msg: "请求成功！",
                        data: kline_data
                    })
                    redis.hSet("webmagic_api:getklineWeb", params.pairAddress + "_" + params.period, JSON.stringify(kline_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

async function getklineWebDTask(request, result) {
    try {
        let params = request.body
        if (!params.pairAddress || !params.period) {
            result({
                code: 400,
                msg: 'pairAddress,period参数不能为空',
                data: null
            })
            return;
        }
        let interval = 1440;//默认一天
        switch (params.period) {
            case "1D":
                interval = 1440;
                break;
            case "1W":
                interval = 10080;
                break;
        }
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/pairs/' + params.pairAddress + '-bsc' + '/kline?interval=' + interval + '&category=u&count=800',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };

        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    if (!encode_data || encode_data == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                    }
                    let de_data = decode(encode_data)
                    let kline_data = de_data['kline_data'];
                    let item;
                    for (let i = 0; i < kline_data.length; i++) {
                        // item.time = formatTimestamp(item.time);
                        item = kline_data[i];
                        item["id"] = i;
                        item["pairId"] = i;
                        item["open"] = item.open;
                        item["high"] = item.high;
                        item["low"] = item.low;
                        item["close"] = item.close;
                        item["volume"] = item.volume;
                        item["klineTime"] = item.time;
                        item["period"] = "1M";
                        item["openTag"] = "";
                        item["closeTag"] = "";
                        item["amplitude"] = 0;
                        item["tradeAmount"] = 0;
                        item["priceChange"] = 0;
                        item["changePct"] = 0;
                        item["tradeNum"] = 0;
                        delete item.time;
                    }
                    result({
                        code: 200,
                        msg: "请求成功！",
                        data: kline_data
                    })
                    redis.hSet("webmagic_api:getklineWebD", params.pairAddress + "_" + params.period, JSON.stringify(kline_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}


// 新币榜
async function New_Currency_ListTask(request, result) {
    try {
        let options = {
            hostname: ave_url,
            path: '/v1api/v3/tokens/token_list?chain=bsc&category=new&pageSize=100&sort=&direction=desc&group=0',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };


        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['encode_data'] == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    let de_data = decode(encode_data);
                    de_data.forEach((item) => {
                        item.targetToken = item['target_token'];
                        if (item.targetToken.includes(item.token0_address)) {
                            item.tokenAddress = item['token0_address'];
                            item.tokenSymbol = item['token0_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token0_logo_url'];
                        } else {
                            item.tokenAddress = item['token1_address'];
                            item.tokenSymbol = item['token1_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token1_logo_url'];
                        }
                        item.currentPriceUsd = item['current_price_usd'];
                        item.priceChange24h = item['price_change_24h'];
                        item.txVolumeU24h = item['tx_volume_u_24h'];
                        item.txCount24h = item['tx_count_24h'];
                        item.openTime = item['opening_at'];
                        item.marketCap = item['market_cap'];
                        item.reserveUsd = item['reserve_usd'];
                        item.tokenIndex = item['token_index'];
                        delete item['target_token'];
                        delete item['token0_address'];
                        delete item['token0_symbol'];
                        delete item['token0_logo_url'];
                        delete item['token1_address'];
                        delete item['token1_symbol'];
                        delete item['token1_logo_url'];
                        delete item['current_price_usd'];
                        delete item['price_change_24h'];
                        delete item['tx_volume_u_24h'];
                        delete item['tx_count_24h'];
                        delete item['opening_at'];
                        delete item['market_cap'];
                        delete item['reserve_usd'];
                        delete item['token_index'];
                        //处理图片
                        // console.log("tokenaddress" + item.tokenAddress + ":" + item.imgUrl);
                        let imgFilePath = "." + item.tokenLogoUrl;
                        img_url_upload_ave(item.imgUrl, imgFilePath);
                    });
                    result({
                        code: 200,
                        msg: '请求成功！',
                        data: de_data
                    });
                    redis.hSet("webmagic_api:New_Currency_List", request.query.id, JSON.stringify(de_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

// 涨幅榜
async function Increase_ListTask(request, result) {
    try {
        let options = {
            hostname: ave_url,
            path: '/v1api/v3/tokens/token_list?chain=bsc&category=gainer&pageSize=100&sort=&direction=desc&group=0',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };


        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['encode_data'] == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        ;
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    let de_data = decode(encode_data);
                    de_data.forEach((item) => {
                        item.targetToken = item['target_token'];
                        if (item.targetToken.includes(item.token0_address)) {
                            item.tokenAddress = item['token0_address'];
                            item.tokenSymbol = item['token0_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token0_logo_url'];
                        } else {
                            item.tokenAddress = item['token1_address'];
                            item.tokenSymbol = item['token1_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token1_logo_url'];
                        }
                        item.currentPriceUsd = item['current_price_usd'];
                        item.priceChange24h = item['price_change_24h'];
                        item.txVolumeU24h = item['tx_volume_u_24h'];
                        item.txCount24h = item['tx_count_24h'];
                        item.openTime = item['opening_at'];
                        item.marketCap = item['market_cap'];
                        item.reserveUsd = item['reserve_usd'];
                        item.tokenIndex = item['token_index'];
                        delete item['target_token'];
                        delete item['token0_address'];
                        delete item['token0_symbol'];
                        delete item['token0_logo_url'];
                        delete item['token1_address'];
                        delete item['token1_symbol'];
                        delete item['token1_logo_url'];
                        delete item['current_price_usd'];
                        delete item['price_change_24h'];
                        delete item['tx_volume_u_24h'];
                        delete item['tx_count_24h'];
                        delete item['opening_at'];
                        delete item['market_cap'];
                        delete item['reserve_usd'];
                        delete item['token_index'];
                        //处理图片
                        let imgFilePath = "." + item.tokenLogoUrl;
                        img_url_upload_ave(item.imgUrl, imgFilePath);
                    });
                    result({
                        code: 200,
                        msg: '请求成功！',
                        data: de_data
                    });
                    redis.hSet("webmagic_api:Increase_List", request.query.id, JSON.stringify(de_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

// 跌幅榜
async function Decrease_ListTask(request, result) {
    try {
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/tokens/token_list?chain=bsc&category=losers&pageSize=100&sort=&direction=desc&group=0',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };


        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['encode_data'] == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        ;
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    let de_data = decode(encode_data);
                    de_data.forEach((item) => {
                        item.targetToken = item['target_token'];
                        if (item.targetToken.includes(item.token0_address)) {
                            item.tokenAddress = item['token0_address'];
                            item.tokenSymbol = item['token0_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token0_logo_url'];
                        } else {
                            item.tokenAddress = item['token1_address'];
                            item.tokenSymbol = item['token1_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token1_logo_url'];
                        }
                        item.currentPriceUsd = item['current_price_usd'];
                        item.priceChange24h = item['price_change_24h'];
                        item.txVolumeU24h = item['tx_volume_u_24h'];
                        item.txCount24h = item['tx_count_24h'];
                        item.openTime = item['opening_at'];
                        item.marketCap = item['market_cap'];
                        item.reserveUsd = item['reserve_usd'];
                        item.tokenIndex = item['token_index'];
                        delete item['target_token'];
                        delete item['token0_address'];
                        delete item['token0_symbol'];
                        delete item['token0_logo_url'];
                        delete item['token1_address'];
                        delete item['token1_symbol'];
                        delete item['token1_logo_url'];
                        delete item['current_price_usd'];
                        delete item['price_change_24h'];
                        delete item['tx_volume_u_24h'];
                        delete item['tx_count_24h'];
                        delete item['opening_at'];
                        delete item['market_cap'];
                        delete item['reserve_usd'];
                        delete item['token_index'];
                        //处理图片
                        let imgFilePath = "." + item.tokenLogoUrl;
                        img_url_upload_ave(item.imgUrl, imgFilePath);
                    });
                    result({
                        code: 200,
                        msg: '请求成功！',
                        data: de_data
                    });
                    redis.hSet("webmagic_api:Decrease_List", request.query.id, JSON.stringify(de_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

// 池子榜
async function Pool_ListTask(request, result) {
    try {
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/tokens/token_list?chain=bsc&category=pool&pageSize=100&sort=&direction=desc&group=0',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let thread = [];
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['encode_data'] == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    let de_data = decode(encode_data);
                    de_data.forEach((item) => {
                        item.targetToken = item['target_token'];
                        if (item.targetToken.includes(item.token0_address)) {
                            item.tokenAddress = item['token0_address'];
                            item.tokenSymbol = item['token0_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token0_logo_url'];
                        } else {
                            item.tokenAddress = item['token1_address'];
                            item.tokenSymbol = item['token1_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token1_logo_url'];
                        }
                        item.currentPriceUsd = item['current_price_usd'];
                        item.priceChange24h = item['price_change_24h'];
                        item.txVolumeU24h = item['tx_volume_u_24h'];
                        item.txCount24h = item['tx_count_24h'];
                        item.openTime = item['opening_at'];
                        item.marketCap = item['market_cap'];
                        item.reserveUsd = item['reserve_usd'];
                        item.tokenIndex = item['token_index'];
                        delete item['target_token'];
                        delete item['token0_address'];
                        delete item['token0_symbol'];
                        delete item['token0_logo_url'];
                        delete item['token1_address'];
                        delete item['token1_symbol'];
                        delete item['token1_logo_url'];
                        delete item['current_price_usd'];
                        delete item['price_change_24h'];
                        delete item['tx_volume_u_24h'];
                        delete item['tx_count_24h'];
                        delete item['opening_at'];
                        delete item['market_cap'];
                        delete item['reserve_usd'];
                        delete item['token_index'];
                        //处理图片
                        let imgFilePath = "." + item.tokenLogoUrl;
                        img_url_upload_ave(item.imgUrl, imgFilePath);
                    });
                    result({
                        code: 200,
                        msg: '请求成功！',
                        data: de_data
                    });
                    redis.hSet("webmagic_api:Pool_List", request.query.id, JSON.stringify(de_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

// 成交榜
async function Transaction_ListTask(request, result) {
    try {
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/tokens/token_list?chain=bsc&category=volume&pageSize=100&sort=&direction=desc&group=0',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };


        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['encode_data'] == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        ;
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    let de_data = decode(encode_data);
                    de_data.forEach((item) => {
                        item.targetToken = item['target_token'];
                        if (item.targetToken.includes(item.token0_address)) {
                            item.tokenAddress = item['token0_address'];
                            item.tokenSymbol = item['token0_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token0_logo_url'];
                        } else {
                            item.tokenAddress = item['token1_address'];
                            item.tokenSymbol = item['token1_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token1_logo_url'];
                        }
                        item.currentPriceUsd = item['current_price_usd'];
                        item.priceChange24h = item['price_change_24h'];
                        item.txVolumeU24h = item['tx_volume_u_24h'];
                        item.txCount24h = item['tx_count_24h'];
                        item.openTime = item['opening_at'];
                        item.marketCap = item['market_cap'];
                        item.reserveUsd = item['reserve_usd'];
                        item.tokenIndex = item['token_index'];
                        delete item['target_token'];
                        delete item['token0_address'];
                        delete item['token0_symbol'];
                        delete item['token0_logo_url'];
                        delete item['token1_address'];
                        delete item['token1_symbol'];
                        delete item['token1_logo_url'];
                        delete item['current_price_usd'];
                        delete item['price_change_24h'];
                        delete item['tx_volume_u_24h'];
                        delete item['tx_count_24h'];
                        delete item['opening_at'];
                        delete item['market_cap'];
                        delete item['reserve_usd'];
                        delete item['token_index'];
                        //处理图片
                        let imgFilePath = "." + item.tokenLogoUrl;
                        img_url_upload_ave(item.imgUrl, imgFilePath);
                    });
                    result({
                        code: 200,
                        msg: '请求成功！',
                        data: de_data
                    });
                    redis.hSet("webmagic_api:Transaction_List", request.query.id, JSON.stringify(de_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

// 换手榜
async function Change_Hands_ListTask(request, result) {
    try {
        const options = {
            hostname: ave_url,
            path: '/v1api/v3/tokens/token_list?chain=bsc&category=exchange&pageSize=100&sort=&direction=desc&group=0',
            agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }),
            headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };


        let html = ''
        const req = https.get(options, function (res) {

            res.on('data', function (chunk) {
                html += chunk;
            });

            res.on('end', function () {
                try {
                    let jsonData = JSON.parse(html);
                    if (jsonData['msg'].indexOf("Authorization") > -1) {
                        console.log("认证过期！");
                        result({
                            code: 200,
                            msg: "认证过期！",
                            data: null
                        });
                        return;
                    }
                    if (jsonData['encode_data'] == "") {
                        console.log("encode_data为空");
                        result({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let encode_data = jsonData['encode_data']
                    let de_data = decode(encode_data);
                    de_data.forEach((item) => {
                        item.targetToken = item['target_token'];
                        if (item.targetToken.includes(item.token0_address)) {
                            item.tokenAddress = item['token0_address'];
                            item.tokenSymbol = item['token0_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token0_logo_url'];
                        } else {
                            item.tokenAddress = item['token1_address'];
                            item.tokenSymbol = item['token1_symbol'];
                            item.tokenLogoUrl = "/image/" + item.tokenAddress + ".png";
                            item.imgUrl = item['token1_logo_url'];
                        }
                        item.currentPriceUsd = item['current_price_usd'];
                        item.priceChange24h = item['price_change_24h'];
                        item.txVolumeU24h = item['tx_volume_u_24h'];
                        item.txCount24h = item['tx_count_24h'];
                        item.openTime = item['opening_at'];
                        item.marketCap = item['market_cap'];
                        item.reserveUsd = item['reserve_usd'];
                        item.tokenIndex = item['token_index'];
                        delete item['target_token'];
                        delete item['token0_address'];
                        delete item['token0_symbol'];
                        delete item['token0_logo_url'];
                        delete item['token1_address'];
                        delete item['token1_symbol'];
                        delete item['token1_logo_url'];
                        delete item['current_price_usd'];
                        delete item['price_change_24h'];
                        delete item['tx_volume_u_24h'];
                        delete item['tx_count_24h'];
                        delete item['opening_at'];
                        delete item['market_cap'];
                        delete item['reserve_usd'];
                        delete item['token_index'];
                        //处理图片
                        let imgFilePath = "." + item.tokenLogoUrl;
                        img_url_upload_ave(item.imgUrl, imgFilePath);
                    });
                    result({
                        code: 200,
                        msg: '请求成功！',
                        data: de_data
                    });
                    redis.hSet("webmagic_api:Change_Hands_List", request.query.id, JSON.stringify(de_data));
                } catch (e) {
                    result({
                        code: 500,
                        msg: e,
                        data: null
                    })
                }
            });
            res.on('error', function (e) {
                result({
                    code: 500,
                    msg: e,
                    data: null
                })
            });
        });
        req.on('error', (e) => {
            result({
                code: 500,
                msg: e,
                data: null
            })
        });
        req.end();
    } catch (e) {
        result({
            code: 500, msg: e, data: null
        })
    }
}

async function hotTopTask(request, result) {
    if (!request.query.id) {
        result({
            code: 400,
            msg: 'id参数不能为空',
            data: null
        })
        return;
    }
    switch (request.query.id) {
        case "2":
            //池子榜
            Pool_ListTask(request, result);
            break;
        case "3":
            //涨幅榜
            Increase_ListTask(request, result);
            break;
        case "4":
            //跌幅榜
            Decrease_ListTask(request, result);
            break;
        case "5":
            //成交榜
            Transaction_ListTask(request, result);
            break;
        case "6":
            //回报率榜
            break;
        case "7":
            //换手榜
            Change_Hands_ListTask(request, result);
            break;
        case "8":
            //"LP占比榜"
            break;
        case "9":
            //"市值榜"
            break;
        case "10":
            //"持币榜"
            break;
        case "11":
            //"新币榜"
            New_Currency_ListTask(request, result);
            break;
        // case "12":
        //     //"自选"
        //     await myself_choose(request, result);
        //     break;
    }

}



module.exports = {
    getPairBaseTask, hotTopTask, getklineWebDTask, getklineWebTask, Main_chipsTask, day_kTask
}

// const io = require("socket.io")(3000);
// io.on("connection", (socket) => {
//     io.emit("join",socket.id+"链接了");
// });



const express = require('express'); // 引入express模块
const app = express();
let io_all ;
var sockets = [];
var socket_all = [];

function init(){
    const server = require('http').createServer(app);
    var io = require('socket.io')(server);
    io.on('connection', socket => {
        //console.log("connection");
        socket.on("admin",data=>{
            sockets.push(socket);
        });
        socket.on("change_bai",data=>{
            //console.log("change_bai");
            for(var i = 0;i<sockets.length;i++){
                sockets[i].emit("change_bai",data);
            }
        });
        socket_all.push(socket);
    });
    server.listen(3000);
    io_all = io;
}

function getIo(){
    return socket_all;
} 
module.exports = {
    init,
    getIo
}




const v8 = require('v8');
const maxHeapSize = 20480; // 2GB
const {startSwaps} = require('./parseTrans');

v8.setFlagsFromString(`--max-old-space-size=${maxHeapSize}`);

    //配置多线程（100个线程）
    async function start(){
        try{
            let threadCount = 1;
            let threadPromise = [];
            //拆分成多个线程promise执行
            for (let i = 0; i < threadCount; i++) {
                threadPromise.push(new Promise(async (resolve, reject) => {
                    await startSwaps();
                    resolve();
                }));
            }
            let p = Promise.all(threadPromise);
            await p.then(arr => {

            }, e => {
                // console.log("失败子线程"+e)
            })
        }catch(ex){
            console.log(ex);
        }
        
    }
    start();
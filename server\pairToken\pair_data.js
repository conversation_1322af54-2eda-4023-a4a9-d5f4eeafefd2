
const redis = require("../database/redis");
const {dbPairQuery } = require("../model/mysql/AviPairModel");

async function batchCallContracts() {
    let keys = await redis.hKeys("pair_json_hdata");
    let pairs = [];
    //let pair_doros = await dbPairQuery([]);
    //console.log(pair_doros.length)
    for (let index = 0; index < keys.length; index++) {
        console.log(index);
        const element = keys[index];
        // let pair_doros = await dbPairQuery([
        //     {
        //         type: "string",
        //         column: "address",
        //         value: element
        //     }
        // ]);
        // if(pair_doros.length == 0){
        pairs.push(element);
        //}
        
    }
    console.log("开始插入",pairs.length);
    //将token存入到redis
    await redis.rPush("pair_data_address",pairs);
    console.log("插入完成");
}

batchCallContracts();
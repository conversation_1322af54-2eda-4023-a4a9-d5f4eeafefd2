const {Contract, JsonRp<PERSON><PERSON><PERSON><PERSON>} = require("ethers");
const {allAbi} = require("./abi");
const {RPC_NEW} = require("./utils/constant");
const {Worker} = require("worker_threads");
const provider = new JsonRpcProvider(RPC_NEW, undefined, {polling: true});
const {formatStrDecimal, getTokenToPair, hasSpecial} = require("./utils/BaseDataUtils");
const {HQL_sonar} = require("./database/PqueryQL/PostgresSQL");
const {redis} = require("./utils/redisUtils");
const FirstAnalyzeEnum = require("./utils/enums/AnalyzeDorisKeyEnum");
const axios = require("axios");
const WebSocket = require('ws');
async function getProvider(){
    let tokenContract = new Contract("******************************************", allAbi, provider);
    let total_supply = await tokenContract.totalSupply();//总共发行量
}

function decode(encode_data){
    return JSON.parse(global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")));
}

async function getCode(call){
    // let objStr = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", '******************************************');
    // if (objStr) {
    //    let result = JSON.parse(objStr);
    //     if (result.pair_address) {
    //
    //     }
    // }
    // let objStr2 = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata+"_v2", '******************************************');
    // if (objStr2) {
    //     let result = JSON.parse(objStr2);
    //     if (result.pair_address) {
    //
    //     }
    // }

    // await axios.get("https://api.binance.com/api/v3/ticker?symbol=BTCUSDT").then((res)=>{
    //     if(res && res.status == 200){
    //         console.log(res)
    //         // let updateData = response.data;
    //         // delete updateData.symbol;
    //         // ticker.update(updateData);
    //     }
    // }).catch(error => {
    //     console.error("Ticker24hr获取数据失败",error);
    // });


    const ws = new WebSocket('wss://stream.binance.com:9443/stream?streams=btcusdt@ticker/ethusdt@ticker');

    ws.onmessage = (event) => {
        const data = JSON.parse(event.data).data;  // 注意多流返回的数据结构

        /**
         * 基础信息
         * 字段	含义	示例值	说明
         * e	事件类型	"24hrTicker"	固定值，表示24小时行情更新
         * E	事件时间（毫秒级时间戳）	1751276294018	数据更新的服务器时间（UTC）
         * s	交易对	"BTCUSDT"	基础币种/报价币种（BTC兑USDT）
         * 价格相关
         * 字段	含义	示例值	说明
         * o	开盘价	"107944.18000000"	24小时前的开盘价格
         * c	最新价 (Close)	"107619.43000000"	当前最新成交价（最重要的实时价格）
         * h	最高价	"108789.99000000"	24小时内的最高成交价
         * l	最低价	"107250.00000000"	24小时内的最低成交价
         * p	价格变化（绝对值）	"-324.75000000"	当前价与开盘价的差额（c - o）
         * P	价格变化（百分比）	"-0.301"	涨跌幅百分比（(p / o) * 100）
         * w	加权平均价	"108023.68294191"	24小时成交量加权平均价
         * 买卖盘（订单簿）
         * 字段	含义	示例值	说明
         * b	最佳买价 (Bid Price)	"107619.43000000"	当前最高买单价
         * B	最佳买量 (Bid Quantity)	"2.86766000"	最佳买价对应的挂单量
         * a	最佳卖价 (Ask Price)	"107619.44000000"	当前最低卖单价
         * A	最佳卖量 (Ask Quantity)	"4.88649000"	最佳卖价对应的挂单量
         * 成交量
         * 字段	含义	示例值	说明
         * v	基础币种成交量	"9015.59074000"	24小时内BTC的成交量（单位：BTC）
         * q	报价币种成交量	"973897315.63175970"	24小时内USDT的成交量（单位：USDT）
         * Q	最新成交数量	"0.00145000"	最新一笔交易的BTC数量
         * 其他
         * 字段	含义	示例值	说明
         * x	上一次收盘价	"107944.18000000"	通常与开盘价（o）相同
         * O	统计开盘时间	1751189894001	24小时周期开始的UTC时间戳
         */
    };
    ws.onerror = (error) => {
        console.error("Ticker24hr获取数据失败",error);
    };

}

getCode();
// getCode(function (w) {
//     console.log(w)
// });
module.exports = {getCode};
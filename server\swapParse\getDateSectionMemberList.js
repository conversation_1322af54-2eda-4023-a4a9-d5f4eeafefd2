const dayjs = require("dayjs");
const {Contract} = require('ethers')
const {ethers} = require('ethers')
const marketAbi = require("../abi/market.json")
const netbodyAbi = require("../abi/netbody.json")
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
const rpc_listen = 'http://*************:8545'; //"https://bsc.blockpi.network/v1/rpc/156f516494b3a4024171a8045490d2d622e3a3b8"
const provider = new ethers.JsonRpcProvider(rpc_listen)
const marketAddress = "******************************************"
const netbodyAddresss = "******************************************"
let addressArray = ["******************************************"];
const resultsList = [];

const netbody = new Contract(netbodyAddresss, netbodyAbi, provider);
async function main(netbody, market, address) {
    let result = await netbody.getNetBodyInfo(address)
    let list = result[2];
    list.forEach(async item => {
        let res = await market.identity(item);
        if (res[0] == 2 || res[0] == 1) {
            let infos = {
                address: item,
                supperAddress: address,
                identity: res[0],
                startTime:res[3]
            }
            let strTime;
            if(res[2]>0){
                strTime = res[2];
            }
            else {
                strTime = res[3];
            }
            if(strTime>********** && strTime<********** && res[0]==1){

                if(resultsList.filter(el=>el.address==item).length>0){
                    console.log("找到重复的了")
                }
                else {

                    resultsList.push(infos);
                }
            }

        }
        await main(netbody, market, item)
    })
}

async function getMemberList() {
    console.log("开始执行解析会员网体")
    const netbody = new Contract(netbodyAddresss, netbodyAbi, provider)
    const market = new Contract(marketAddress, marketAbi, provider)
    // 直接上父级地址
    await main(netbody, market, "******************************************");
    setTimeout(() => {
        try {
            console.log("总长度",resultsList.length)
            let allStr = JSONbig.stringify(resultsList)
            redis.set("member_date_section_identity_list", allStr);
            resultsList.length = 0;
            console.log("存储结束")
        } catch (e) {
            console.error("加载会员网体报错:", e);
        }
    }, 40000)
}




async function getMemberOperatorList() {

    //
    // let listStr = await redis.get("member_date_section_identity_list");
    // let listAll = JSONbig.parse(listStr);
    // console.log("运营商数量",listAll.filter(item=>item.identity==1).length)
    // console.log("VIP数量",listAll.filter(item=>item.identity==2).length)
    let memberList= [
        {
            address:'0x6afFBb12A4E4bbaE07bB696d65B4d931Eb34e2fa'
        },
        {
            address:'0xe216a66FbC6bd41B5541402ac5B23e8F20706321'
        },
        {
            address:'0xDBb48C016c752094ECbC526c4Fea1c096C2D23D4'
        },
    ]
    for (let i = 0; i <memberList.length; i++) {
        let item = memberList[i];
        let superAllAddress = await getSuperAdress(item.address);
        //如果查询结果不止一层上级的话
        if(superAllAddress.indexOf("<")>0){
            let listSuperAddress = superAllAddress.split('<');
            item.directSupperAddress = listSuperAddress[0];
            item.supperAllAddress = listSuperAddress[listSuperAddress.length-2];
        }
        else {
            item.supperAllAddress = "NULL"
            item.directSupperAddress= "NULL";
        }
        superAllAddress = item.address+'<'+superAllAddress;
        let date = await formatTime(item.startTime*1000,'YY-MM-DD HH:mm')
        console.log(`${i},${item.address},${item.supperAllAddress},${item.directSupperAddress},${date},${superAllAddress}`)
    }
    return;
}
async function getSuperAdress(address){
    // console.log("开始调用",address)
    let result = await netbody.getNetBodyInfo(address)
    // console.log("查询结果",result)
    let superAddress = result[1];
    if(!superAddress || superAddress.toLowerCase() =='0x0000000000000000000000000000000000000000' || superAddress.toLowerCase() ==netbodyAddresss.toLowerCase() || superAddress.toLowerCase() ==marketAddress.toLowerCase()){
        return;
    }
    else {
        let newSuperAddress = await getSuperAdress(superAddress);
        if(newSuperAddress){
            return `${superAddress}<${newSuperAddress}`;

        }
        else {

            return superAddress;
        }
    }
}
async function formatTime(date, template) {
    return dayjs(date).format(template);
}
getMemberOperatorList()
// getMemberList()

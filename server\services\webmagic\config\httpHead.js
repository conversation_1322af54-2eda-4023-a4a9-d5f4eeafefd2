const {redis} = require("../../../utils/redisUtils");
const ave_url = "api.eskegs.com";
let index = 0;//用来模拟轮询获取
async function getHeaders() {
    let key="header"+index;
    let headerStr = await redis.hGet("webmagic_api:httpHead", key);
    let header = JSON.parse(headerStr);
    //模拟负载均衡
    index++;
    index = index % 3;
    //取当前header
    return header;
}

/**
 * 写入头文件
 * @returns {Promise<void>}
 */
async function setHeaders() {
    let header0 = {
        'authority': ave_url,
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'ave-udid': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36--1709092321591--fd01a41e-bc32-4a33-815f-f5c9d4b2ddbf',
        'cache-control': 'no-cache',
        'lang': 'en',
        'origin': 'https://ave.ai',
        'pragma': 'no-cache',
        'referer': 'https://ave.ai/',
        'sec-ch-ua': 'Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        'x-auth': '77f646c10e5868115d9a34f4471d4a5c1719055659759536524'
    }
    let header1 = {
        'authority': ave_url,
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'ave-udid': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36--1709346451534--538f945e-8b6e-4191-9a85-bdb23036e392',
        'cache-control': 'no-cache',
        'lang': 'en',
        'origin': 'https://ave.ai',
        'pragma': 'no-cache',
        'referer': 'https://ave.ai/',
        'sec-ch-ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        'x-auth': '77f646c10e5868115d9a34f4471d4a5c1719055659759536524'
    }
    let header2 = {
        'authority': ave_url,
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'ave-udid': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36--1709092321591--fd01a41e-bc32-4a33-815f-f5c9d4b2ddbf',
        'cache-control': 'no-cache',
        'lang': 'en',
        'origin': 'https://ave.ai',
        'pragma': 'no-cache',
        'referer': 'https://ave.ai/',
        'sec-ch-ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        'x-auth': '75178a69ea1ef9b9cf1b39b20dc14e331719198521588554894'
    }
    //ave-udid,sec-ch-ua,x-auth只需修改这3个参数
    await redis.hSet("webmagic_api:httpHead", "header0", JSON.stringify(header0));
    await redis.hSet("webmagic_api:httpHead", "header1", JSON.stringify(header1));
    await redis.hSet("webmagic_api:httpHead", "header2", JSON.stringify(header2));
}

// setHeaders();
module.exports = {
    getHeaders
}

const redis = require("../database/redis");

async function batchCallContracts() {
    while(true){
        let keys = await redis.lPop("avePairsSL_error_o");
        if(keys){

        }else{
            break;
        }
        //将token存入到redis
        await redis.rPush("avePairsSL_error",keys);
        await redis.rPush("avePairsSL_error_c",keys);
    }
    console.log("插入完成");
}

batchCallContracts();
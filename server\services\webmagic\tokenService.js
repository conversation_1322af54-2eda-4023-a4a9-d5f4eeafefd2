let https = require("https");
const headers = {}
window = global;
const {formatTimestampUTC, formatTimestamp} = require("../../utils/DateUtils");
const {redis} = require("../../utils/redisUtils");
const {getHeaders} = require("./config/httpHead");
const {default: BigNumber} = require("bignumber.js");
const {img_url_upload} = require("./task/imgUpload");
const ave_url = "api.eskegs.com";
function decode(encode_data) {
    return JSON.parse(window.decodeURIComponent(window.atob(encode_data).replace(/\+/g, " ")));
}

async function getImgUrl(address, call) {
    try {
        if (!address) {
            call({
                code: 400, msg: 'address参数不能为空', data: null
            })
            return;
        }
        let url = '/v1api/v3/tokens/' + address + "-bsc"
        const options = {
            hostname: ave_url, path: url, agent: new https.Agent({
                //proxy: "http://127.0.0.1:7890"
            }), headers: await getHeaders(),
            timeout: 60000 // 设置超时时间为5秒
        };
        let html = ''
        let response = {};
        const req = https.get(options, function (res) {
            res.on('data', function (chunk) {
                html += chunk;
            });
            res.on('end', function () {
                try {
                    let thread = [];
                    let jsonData = JSON.parse(html);
                    if (!jsonData['data']) {
                        console.log("更新图片encode_data为空");
                        call({
                            code: 200,
                            msg: "数据为空！",
                            data: null
                        });
                        return;
                    }
                    let data ;
                    if(typeof jsonData['data'] === 'string'){
                        data = JSON.parse(jsonData['data']);
                    }else{
                        data = jsonData['data'];
                    }
                    let logo_url = data['token']['logo_url'];
                    let imgFilePath = "./image/" + data['token'].token + ".png";
                    img_url_upload(logo_url, imgFilePath);
                    call({
                        code: 200, msg: '请求成功！', data: response
                    });
                } catch (e) {
                    call({
                        code: 500, msg: e, data: null
                    })
                }
            });
            res.on('error', function (e) {
                call({
                    code: 500, msg: e, data: null
                })
            });
        });
        req.on('error', (e) => {
            call({
                code: 500, msg: e, data: null
            });
        });
        req.end();
    } catch (e) {
        call({
            code: 500, msg: e, data: null
        })
    }
}

async function getTokenBase(request, result) {
    let params = request.query
    if (!params.token_address) {
        result.json({
            code: 400,
            msg: 'token_address参数不能为空',
            data: null
        })
        return;
    }
    let objStr = await redis.hGet("webmagic_api:getTokenBase", params.token_address);
    if (objStr) {
        result.json({
            code: 200, msg: '请求成功！', data: JSON.parse(objStr)
        });
        return;
    }
    let options = {
        hostname: 'bsc.tokenview.io',
        path: '/api/bsc/contract/creator/' + params.token_address,
        agent: new https.Agent({
            //proxy: "http://127.0.0.1:7890"
        }),
        timeout: 60000 // 设置超时时间为5秒
        // headers: headers
    };


    let html = ''
    const req = https.get(options, function (res) {

        res.on('data', function (chunk) {
            html += chunk;
        });

        res.on('end', function () {
            try {
                let jsonData = JSON.parse(html);
                let data = jsonData['data'];
                let obj = {
                    address: params.token_address,
                    block_time: formatTimestampUTC(data.time),
                    created_block: data.block_no
                }
                result.json({
                    code: 200,
                    msg: '请求成功！',
                    data: obj
                });
            } catch (e) {
                result.json({
                    code: 500,
                    msg: e,
                    data: null
                })
            }
        });
        res.on('error', function (e) {
            result.json({
                code: 500,
                msg: e,
                data: null
            })
        });
    });
    req.on('error', (e) => {
        result.json({
            code: 500,
            msg: e,
            data: null
        })
    });
    req.end();

}

module.exports = {
    getTokenBase, getImgUrl
}

const {formatUnits} = require('ethers');
const BigNumber = require("bignumber.js");
// const {QueryParameter, DuneClient, RunQueryArgs} = require("@duneanalytics/client-sdk");
const PeriodEnum = require("../../utils/enums/PeriodEnum");
const FirstAnalyzeEnum = require("../../utils/enums/AnalyzeDorisKeyEnum");
const {PqueryQL_sonar, HQL_sonar} = require("../../database/PqueryQL/PostgresSQL");
const {redis, errorToRedis} = require("../../utils/redisUtils");
const {formatStrDecimal, getTokenToPair, hasSpecial} = require("../../utils/BaseDataUtils");
const {getTokenCache} = require("../../utils/BaseDataUtils");
const wasmSum = require("../../utils/wasm/wasm_sum.js");
const {
    formatDateUTC,
    formatDate_UTCDate,
    formatDate_DayDate,
    formatDate_YearDate,
    formatDate_MonthDate,
    getTimeStamp,
    formatUTCDate,
    formatDateToString,
    formatTimestamp,
    TimestampDiff_to_DateArray
} = require("../../utils/DateUtils");
const {formatAddress} = require("../../utils/functions");
//引入RPC
const {Contract, JsonRpcProvider} = require("ethers");
const {allAbi} = require("../../abi");
const {RPC_NEW} = require("../../utils/constant");
const {Worker} = require("worker_threads");
const provider = new JsonRpcProvider(RPC_NEW, undefined, {polling: true});
//潘玉龙48eMJGloJF5dm5RTliqx3ldph9AUdRvM
//张硕jeOQjBJAJkC4uTcGVdFTPWwHq5LOoyTi
// const client = new DuneClient("48eMJGloJF5dm5RTliqx3ldph9AUdRvM");
// const id4131828 = 4131828;
// const id4131819 = 4131819;
// const id4131816 = 4131816;
// const id4131813 = 4131813;
// const id4131830 = 4131830;
// const id4160396 = 4160396;
// const id4160789 = 4160789;
// const id4162780 = 4162780;
// const id4159984 = 4159984;
// const id4163373 = 4163373;
// const id4163596 = 4163596;
// const id4131257 = 4131257;
// const id4164090 = 4164090;
// const id4167606 = 4167606;
// const id4168480 = 4168480;
// const id4151151 = 4151151;
// const id4270225 = 4270225;
const USD_LIST = ['55d398326f99059ff775485246999027b3197955', 'e9e7cea3dedca5984780bafc599bd69add087d56', '833589fcd6edb6e08f4c7c32d4f71b54bda02913', 'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48', '40af3827f39d0eacbf4a168f8d4ee67c121d11c9']

//判断total_supply是否需要转换精度
async function fixTotal(total_supply, decimals) {
    if (!total_supply.includes(".")) {
        total_supply = formatStrDecimal(new BigNumber(total_supply).toFixed(), decimals);
    }
    return total_supply;
}

/**
 * cache 缓存
 * @type {string}
 */
async function getCache(key, params, opts, exprires, callFunc, ...arg) {
    let cache = await redis.get(key);
    let lock = await redis.get("DuneLock:" + key);//获取锁
    if (cache) {
        let cacheObj = JSON.parse(cache);
        //判断缓存过期
        let agoTime = cacheObj.ago_time;
        if (!agoTime || (Date.now() - agoTime) > (exprires * 1000)) {//如果缓存超时则请求接口刷新
            if (!lock) {//表示未上锁
                if (arg.length > 0) {
                    callFunc(opts, params, key, exprires, arg[0], null, null);
                } else {
                    callFunc(opts, params, key, exprires, null, null);
                }
                await redis.set("DuneLock:" + key, 'true', exprires);//为缓存上锁（避免重复请求）
            }
        }
        return {code: 200, msg: "请求成功！", data: cacheObj};
    }
    if (lock) {
        return {code: 500, msg: "请求较多请5分钟后查看!", data: null};
    } else {//从未请求过则上锁开始请求
        await redis.set("DuneLock:" + key, 'true', exprires);//为缓存上锁（避免重复请求）
    }
    return null;
}

/**
 * 交易排行榜
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getTradeAmount_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let token = await getTokenCache(params.token_address);
    if (token) {
        params.pair_address = token.pair_address;
        if (!params.pair_address) {
            let pair = await getTokenToPair(token);
            params.pair_address = pair.pairAddress;
        }
        if (!params.pair_address || params.pair_address == "") {
            result.json({
                code: 500, msg: '当前token没有交易对！', data: null
            });
            return;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    params.pair_address = params.pair_address.toLowerCase();//数据库全是小写
    let q_0_time;
    let q_1_time;
    if (params.str_time && params.end_time) {//如果有自定义内容
        q_0_time = getTimeStamp(formatDate_UTCDate(new Date(params.str_time)));
        q_1_time = getTimeStamp(formatDate_UTCDate(new Date(params.end_time)));
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 8640000000)));//往前退100天
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.WEEK:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 31536000000)));//往前退365天
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.MONTH:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000)));//往前退2年
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.YEAR:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000)));//往前退2年
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
        }
    }
    let pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", params.pair_address);
    if (pair) {
        pair = JSON.parse(pair);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    //查询pair
    // console.log(pair, pair.address)
    let pairs_address_info = pair.address.toLowerCase().substring(2);
    // q_0_time = 1519222400;
    let arg;
    switch (params.type) {
        case PeriodEnum.DAY:
            // arg = `
            //     SELECT count0                     AS token0_count,
            //            count1                     AS token1_count,
            //            (amount0_in + amount0_out) AS token0_sum,
            //            (amount1_in + amount1_out) AS token1_sum,
            //            date_timestamp AS timestamp,
            //         TO_CHAR(TO_TIMESTAMP(date_timestamp), 'YYYY-MM-DD') as formatted_timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     ORDER BY
            //         timestamp;
            // `;
            arg = `
                SELECT
                    t2.count0 as token0_count,
                    t3.count1 as token1_count,
                    t1.token0_sum,
                    t1.token1_sum,
                    t1.TIMESTAMP,
                    t1.formatted_timestamp
                from (
                         SELECT
                             contract,
                             sum(amount0_in + amount0_out) AS token0_sum,
                             sum(amount1_in + amount1_out) AS token1_sum,
                             toUnixTimestamp (formatDateTime (datetime, '%Y-%m-%d')) AS TIMESTAMP,
                            formatDateTime (datetime, '%Y-%m-%d') AS formatted_timestamp
                         FROM
                             MV_swaps
                         WHERE
                             datetime >= ${q_0_time}
                           AND datetime < ${q_1_time}
                           AND contract = '${pairs_address_info}'
                         GROUP BY
                             contract,formatDateTime (datetime, '%Y-%m-%d')
                     ) as t1
                 left JOIN (SELECT contract, formatDateTime(datetime, '%Y-%m-%d') as dt, count (1) as count0 from "MV_swaps" where amount0_in>0 and amount1_out>0 and contract='${pairs_address_info}' GROUP BY contract, formatDateTime(datetime, '%Y-%m-%d')) as t2
                           on t1.contract=t2.contract and t1.formatted_timestamp=t2.dt
                 left join (SELECT contract,formatDateTime(datetime, '%Y-%m-%d') as dt, count (1) as count1 from "MV_swaps" where amount0_out>0 and amount1_in>0 and contract='${pairs_address_info}' GROUP BY contract,formatDateTime(datetime, '%Y-%m-%d')) as t3
                           on t1.contract=t3.contract and t1.formatted_timestamp=t3.dt
                order by t1.formatted_timestamp;
            `;
            break;
        case PeriodEnum.WEEK:
            // arg = `
            //     SELECT FLOOR((date_timestamp - ${q_0_time}) / (7 * 86400)) AS group_id,
            //            SUM(count0)                                         AS token0_count,
            //            SUM(count1)                                         AS token1_count,
            //            SUM(amount0_in) + SUM(amount0_out)                  AS token0_sum,
            //            SUM(amount1_in) + SUM(amount1_out)                  AS token1_sum,
            //            MAX(date_timestamp) AS timestamp,
            //         TO_CHAR(TO_TIMESTAMP(MAX(date_timestamp)), 'YYYY-MM-DD') as formatted_timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     GROUP BY
            //         group_id
            //     ORDER BY
            //         group_id;
            // `;
            arg = `
                SELECT
                    t1.*,
                    t2.count0,
                    t3.count1
                FROM
                    (
                        SELECT
                            FLOOR((toUnixTimestamp (toDateTime (datetime)) - ${q_0_time}) / (7 * 86400)) AS group_id,
                            SUM(amount0_in) + SUM(amount0_out) AS token0_sum,
                            SUM(amount1_in) + SUM(amount1_out) AS token1_sum,
                            MAX(toUnixTimestamp (toDateTime (datetime))) AS TIMESTAMP,
      formatDateTime (max(datetime), '%Y-%m-%d') AS formatted_timestamp
                        FROM
                            MV_swaps
                        WHERE
                            datetime >= ${q_0_time}
                          AND datetime < ${q_1_time}
                          AND contract ='${pairs_address_info}'
                        GROUP BY
                            group_id
                        ORDER BY
                            group_id
                    ) t1
                        LEFT JOIN (
                        SELECT
                            contract,
                            FLOOR((toUnixTimestamp (toDateTime (datetime)) - ${q_0_time}) / (7 * 86400)) AS group_id,
                            count(1) AS count0
                        FROM
                            "MV_swaps"
                        WHERE
                            amount0_in > 0
                          AND amount1_out > 0
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            contract,
                            group_id
                    ) AS t2 ON t1.group_id = t2.group_id
                        LEFT JOIN (
                        SELECT
                            contract,
                            FLOOR((toUnixTimestamp (toDateTime (datetime)) - ${q_0_time}) / (7 * 86400)) AS group_id,
                            count(1) AS count1
                        FROM
                            "MV_swaps"
                        WHERE
                            amount0_out > 0
                          AND amount1_in > 0
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            contract,
                            group_id
                    ) AS t3 ON t1.group_id = t3.group_id
            `;
            break;
        case PeriodEnum.MONTH:
            // arg = `
            //     SELECT EXTRACT(YEAR FROM TO_TIMESTAMP(date_timestamp)) AS year,
            //     EXTRACT(MONTH FROM TO_TIMESTAMP(date_timestamp)) AS month,
            //     SUM(count0) AS token0_count,
            //     SUM(count1) AS token1_count,
            //     SUM(amount0_in)+SUM(amount0_out) AS token0_sum,
            //     SUM(amount1_in)+SUM(amount1_out) AS token1_sum,
            //     MAX(date_timestamp) AS timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     GROUP BY
            //         year, month
            //     ORDER BY
            //         year, month;
            // `;
            arg = `
                SELECT
                    t1.year as year,
                    t1.month as month,
                    t1.token0_sum as token0_sum,
                    t1.token1_sum as token1_sum,
                    t1.TIMESTAMP as TIMESTAMP,
                    t2.count0 as token0_count,
                    t3.count1 as token1_count
                FROM
                    (
                        SELECT
                            toYear (datetime) AS year,
      toMonth (datetime) AS month,
      SUM(amount0_in) + SUM(amount0_out) AS token0_sum,
      SUM(amount1_in) + SUM(amount1_out) AS token1_sum,
      MAX(datetime) AS TIMESTAMP
                        FROM
                            "MV_swaps"
                        WHERE
                            datetime >= ${q_0_time}
                          AND datetime < ${q_1_time}
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            year,
                            month
                        ORDER BY
                            year,
                            month
                    ) t1
                        LEFT JOIN (
                        SELECT
                            toYear (datetime) AS year,
      toMonth (datetime) AS month,
      count(1) AS count0
                        FROM
                            "MV_swaps"
                        WHERE
                            amount0_in > 0
                          AND amount1_out > 0
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            year,
                            month
                    ) AS t2 ON t1.year = t2.year
                        AND t1.month = t2.
                            month LEFT JOIN (
                        SELECT
                            toYear (datetime) AS year,
      toMonth (datetime) AS month,
      count(1) AS count1
                        FROM
                            "MV_swaps"
                        WHERE
                            amount0_out > 0
                          AND amount1_in > 0
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            year,
                            month
                    ) AS t3 ON t1.year = t3.year
                        AND t1.month = t3.month
            `;
            break;
        case PeriodEnum.YEAR:
            // arg = `
            //     SELECT EXTRACT(YEAR FROM TO_TIMESTAMP(date_timestamp)) AS year,
            //     SUM(count0) AS token0_count,
            //     SUM(count1) AS token1_count,
            //     SUM(amount0_in)+SUM(amount0_out) AS token0_sum,
            //     SUM(amount1_in)+SUM(amount1_out) AS token1_sum,
            //     MAX(date_timestamp) AS timestamp,
            //     TO_CHAR(TO_TIMESTAMP(MAX(date_timestamp)), 'YYYY-MM-DD') as formatted_timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     GROUP BY
            //         year
            //     ORDER BY
            //         year;
            // `;
            arg = `
                SELECT
                    t1.year as year,
                    t1.token0_sum as token0_sum,
                    t1.token1_sum as token1_sum,
                    t1.TIMESTAMP as TIMESTAMP,
                    t2.count0 as token0_count,
                    t3.count1 as token1_count
                FROM
                    (
                    SELECT
                    toYear (datetime) AS year,
                    SUM(amount0_in) + SUM(amount0_out) AS token0_sum,
                    SUM(amount1_in) + SUM(amount1_out) AS token1_sum,
                    MAX(datetime) AS TIMESTAMP
                    FROM
                    "MV_swaps"
                    WHERE
                    datetime >= ${q_0_time}
                    AND datetime < ${q_1_time}
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year
                    ORDER BY
                    year
                    ) t1
                    LEFT JOIN (
                    SELECT
                    toYear (datetime) AS year,
                    count(1) AS count0
                    FROM
                    "MV_swaps"
                    WHERE
                    amount0_in > 0
                    AND amount1_out > 0
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year
                    ) AS t2 ON t1.year = t2.year
                    LEFT JOIN (
                    SELECT
                    toYear (datetime) AS year,
                    count(1) AS count1
                    FROM
                    "MV_swaps"
                    WHERE
                    amount0_out > 0
                    AND amount1_in > 0
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year
                    ) AS t3 ON t1.year = t3.year
            `;
            break;
    }

    // let data = await PqueryQL_sonar(arg);

    let rows = await HQL_sonar(arg);
    let data={
        'rows':rows
    }
    // console.log(data);
    let callData = {time: [], value: {amount: [], count: [], num: []}};
    let isNull = true;//默认查询为空的标记
    if (data && data.rows.length > 0) {
        let divisor;
        //不分情况decimals有为0的情况，默认填充18
        if (pair?.decimals) {
            divisor = new BigNumber(10).pow(pair?.decimals)
        } else {
            divisor = new BigNumber(10).pow(18)
        }
        let priceUsdt = await getTokenPsqlUsdt(params.token_address);
        for (let i = 0; i < data.rows.length; i++) {
            let jsons = data.rows[i];
            // console.log("执行第", i)
            if (jsons) {
                isNull = false;
                switch (params.type) {
                    case PeriodEnum.DAY:
                        //查询1D的数据会生成formatted_timestamp字段，包含格式
                        callData.time.push(jsons?.formatted_timestamp);
                        break;
                    case PeriodEnum.WEEK:
                        //查询1W的数据会生成formatted_timestamp字段，包含格式
                        callData.time.push(jsons?.formatted_timestamp);
                        break;
                    case PeriodEnum.MONTH:
                        callData.time.push(`${jsons?.year}-${jsons?.month}`);
                        break;
                    case PeriodEnum.YEAR:
                        callData.time.push(`${jsons?.year}`);
                        break;
                }
                // console.log(jsons?.token0_sum, jsons?.token1_sum)
                // console.log(divisor.toString());
                let actualToken0 = new BigNumber(jsons?.token0_sum.toString()).div(divisor).toFixed();
                let actualToken1 = new BigNumber(jsons?.token1_sum.toString()).div(divisor).toFixed();
                //当前代币相对于pair来说是token1
                if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                    callData.value.count.push(jsons?.token0_count);
                    callData.value.num.push(actualToken0);
                    //如果有价格的话，就返回价格*数量，否则就直接返回数量
                    if (priceUsdt) {
                        callData.value.amount.push(actualToken0 * priceUsdt);
                    } else {
                        callData.value.amount.push(actualToken0);
                    }
                } else {
                    callData.value.count.push(jsons?.token1_count);
                    callData.value.num.push(actualToken1);
                    //如果有价格的话，就返回价格*数量，否则就直接返回数量
                    if (priceUsdt) {
                        callData.value.amount.push(actualToken1 * priceUsdt);
                    } else {
                        callData.value.amount.push(actualToken1);
                    }
                }
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

async function getTokenPsqlUsdt(address, isOverlay = 2) {
    if (address.indexOf('0x') == 0) {
        address = address.toLowerCase().substring(2);
    }
    if (USD_LIST.indexOf(address) != -1) {//如果当前是特殊地址直接返回1
        return 1
    }
    let arg = `
        WITH combined_pairs AS (SELECT address, token0, token1, datetime
                                FROM (SELECT address, token0, token1, datetime
                                      FROM MV_pairs_token0 FINAL
                                      WHERE token0 = '${address}'
                                      UNION ALL
                                      SELECT address, token0, token1, datetime
                                      FROM MV_pairs_token1 FINAL
                                      WHERE token1 = '${address}'))
        SELECT *
        FROM combined_pairs
    `;
    let data = await HQL_sonar(arg);
    let maxPair;
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let jsons = data[i];
            if (USD_LIST.indexOf(jsons.token1) != -1 || USD_LIST.indexOf(jsons.token0) != -1) {
                let findUsdtSql = `
                    SELECT reserve0,
                           reserve1
                    FROM MV_LAST_syncs FINAL
                    WHERE address = '${jsons.address}' LIMIT 1;
                `
                let infos = await HQL_sonar(findUsdtSql);
                if (infos && infos.length > 0) {
                    let info = infos[0];
                    if (USD_LIST.indexOf(jsons.token1) != -1) {
                        let tokenDecimals = await getTokenCache('0x' + jsons.token0);
                        return getTokenCalcReserve(info.reserve1, 18, info.reserve0, tokenDecimals.decimals);
                    } else {
                        let tokenDecimals = await getTokenCache('0x' + jsons.token1);
                        return getTokenCalcReserve(info.reserve0, 18, info.reserve1, tokenDecimals.decimals);
                    }
                }
            }
        }
        if (isOverlay > 0) {
            isOverlay--;
            for (let i = 0; i < data.length; i++) {
                let jsons = data[i];
                let price;
                if (jsons.token0 == address) {
                    //不继续叠加
                    price = await getTokenPsqlUsdt(jsons.token1, isOverlay)
                } else {
                    //不继续叠加
                    price = await getTokenPsqlUsdt(jsons.token0, isOverlay)
                }
                if (price != 0) {
                    //找到价格了，就根据当前这个币的价格和查询的币的交易对比例进行价格计算返回
                    let findUsdtSql = `
                        SELECT reserve0,
                               reserve1
                        FROM MV_LAST_syncs FINAL
                        WHERE address = '${jsons.address}' LIMIT 1;
                    `
                    let infos = await HQL_sonar(findUsdtSql);
                    if (infos && infos.length > 0) {
                        let info = infos[0];
                        let priceUsdt;
                        let token0Decimals = await getTokenCache('0x' + jsons.token0);
                        let token1Decimals = await getTokenCache('0x' + jsons.token1);
                        let reserve1 = new BigNumber(info.reserve1).div(token1Decimals.decimals);
                        let reserve0 = new BigNumber(info.reserve0).div(token0Decimals.decimals);
                        if (jsons.token0 == address) {
                            priceUsdt = parseFloat(reserve1.div(reserve0));
                            return priceUsdt * price;
                        } else {
                            priceUsdt = parseFloat(reserve0.div(reserve1));
                            return priceUsdt * price;
                        }
                    }
                } else {
                    return 0
                }
            }
        }
        return 0;
    } else {
        return 0;
    }
}

async function getTokenCalcReserve(reserveUSDT, usdtDecimals, reserveToken, tokenDecimals) {
    const rUSDT = new BigNumber(reserveUSDT).dividedBy(`1e${usdtDecimals}`);
    const rToken = new BigNumber(reserveToken).dividedBy(`1e${tokenDecimals}`);
    return rUSDT.dividedBy(rToken).toFixed(18);
}

async function getTradeTop_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.find_type || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let token = await getTokenCache(params.token_address);
    if (token) {
        params.pair_address = token.pair_address;
        if (!params.pair_address) {
            let pair = await getTokenToPair(token);
            params.pair_address = pair.pairAddress;
        }
        if (!params.pair_address || params.pair_address == "") {
            result.json({
                code: 500, msg: '当前token没有交易对！', data: null
            });
            return;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    params.pair_address = params.pair_address.toLowerCase();//数据库全是小写
    let q_0_time;
    let q_1_time;
    if (params.str_time && params.end_time) {//如果有自定义内容
        q_0_time = getTimeStamp(formatDate_UTCDate(new Date(params.str_time)));
        q_1_time = getTimeStamp(formatDate_UTCDate(new Date(params.end_time)));
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 86400000)));//往前退100天
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.WEEK:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 604800000)));//往前退365天
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.MONTH:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 2592000000)));//往前退2年
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.YEAR:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 31536000000)));//往前退2年
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
        }
    }
    let pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", params.pair_address);
    if (pair) {
        pair = JSON.parse(pair);
    } else {
        // let pairData = await GqueryQL(`{
        //                                       pairs(first: 1, where: {id: "${params.pair_address}"}) {
        //                                         id
        //                                         token0
        //                                         token1
        //                                         name
        //                                         symbol
        //                                         totalSupply
        //                                         decimals
        //                                       }
        //                                     }`);
        // let pairs = pairData.data.data.pairs;
        // if (!pairs) {
        //     pair = {
        //         token0_addr: pairData[0].token0,
        //         token1_addr: pairData[0].token1,
        //         address: pairData[0].id,
        //         decimals: pairData[0].decimals,
        //     }
        // } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
        // }
    }
    //查询pair
    // console.log(pair, pair.address)
    let pairs_address_info = pair.address.toLowerCase().substring(2);
    // q_0_time = 1519222400;
    //根据不同的参数确定排序规则
    let orderBuyByName;
    let orderSellByName;
    let orderCountByName;
    switch (params.find_type) {
        case 'sum_usd':
            if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                orderBuyByName = 'amount0_out';
                orderSellByName = 'amount0_in'
            } else {
                orderBuyByName = 'amount1_out';
                orderSellByName = 'amount1_in'
            }
            break;
        case 'sum_num':
            if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                orderBuyByName = 'amount0_out';
                orderSellByName = 'amount0_in'
            } else {
                orderBuyByName = 'amount1_out';
                orderSellByName = 'amount1_in'
            }
            break;
        case 'sum_count':
            if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                orderBuyByName = 'count0';
                orderSellByName = 'count0'
            } else {
                orderBuyByName = 'count1';
                orderSellByName = 'count1'
            }
            break;
    }
    // pgsql
    // let arg = `
    //     WITH buy_list AS (SELECT SUM(amount0_out) AS amount0_out,
    //                              SUM(amount1_out) AS amount1_out,
    //                              SUM(amount0_in)  AS amount0_in,
    //                              SUM(amount1_in)  AS amount1_in,
    //                              SUM(count0)      AS count0,
    //                              SUM(count1)      AS count1,
    //                              sender           AS address_from
    //                       FROM sender_swaps
    //                       WHERE date_timestamp >= ${q_0_time}
    //                         AND date_timestamp < ${q_1_time}
    //                         AND address = '${pairs_address_info}'
    //                       GROUP BY address, sender
    //                       ORDER BY ${orderBuyByName} DESC
    //         LIMIT 20
    //         )
    //        , sell_list AS (
    //     SELECT
    //         SUM (amount0_out) AS amount0_out, SUM (amount1_out) AS amount1_out, SUM (amount0_in) AS amount0_in, SUM (amount1_in) AS amount1_in, SUM (count0) AS count0, SUM (count1) AS count1, sender AS address_from
    //     FROM
    //         sender_swaps
    //     WHERE
    //         date_timestamp >= ${q_0_time}
    //       AND date_timestamp
    //         < ${q_1_time}
    //       AND address = '${pairs_address_info}'
    //     GROUP BY
    //         address, sender
    //     ORDER BY ${orderSellByName} DESC
    //         LIMIT 2000
    //         )
    //
    //     SELECT json_agg(
    //                    json_build_object(
    //                            'amount0_out', amount0_out,
    //                            'amount1_out', amount1_out,
    //                            'amount0_in', amount0_in,
    //                            'amount1_in', amount1_in,
    //                            'count0', count0,
    //                            'count1', count1,
    //                            'address_from', address_from,
    //                            'type', 'buy' -- fixed syntax: using 'type' => 'buy'
    //                    )
    //            ) AS json
    //
    //     FROM buy_list
    //
    //     UNION ALL
    //
    //     SELECT json_agg(
    //                    json_build_object(
    //                            'amount0_out', amount0_out,
    //                            'amount1_out', amount1_out,
    //                            'amount0_in', amount0_in,
    //                            'amount1_in', amount1_in,
    //                            'count0', count0,
    //                            'count1', count1,
    //                            'address_from', address_from,
    //                            'type', 'sell' -- fixed syntax: using 'type' => 'sell'
    //                    )
    //            ) AS json
    //     FROM sell_list;
    // `;

    // clickhouse
    let arg = `
        WITH buy_list AS (SELECT t1.amount0_out,
                                 t1.amount1_out,
                                 t1.amount0_in,
                                 t1.amount1_in,
                                 t1.address_from,
                                 t2.count0,
                                 t3.count1
                          from (SELECT contract,
                                       sender,
                                       formatDateTime(datetime, '%Y-%m-%d') as dt,
                                       SUM(amount0_out)                     AS amount0_out,
                                       SUM(amount1_out)                     AS amount1_out,
                                       SUM(amount0_in)                      AS amount0_in,
                                       SUM(amount1_in)                      AS amount1_in,
                                       sender                               AS address_from
                                FROM "MV_swaps"
                                WHERE datetime >= ${q_0_time}
                                  AND datetime < ${q_1_time}
                                  AND contract = '${pairs_address_info}'
                                GROUP BY contract,
                                         sender,
                                         formatDateTime(datetime, '%Y-%m-%d')) as t1
            any left JOIN (SELECT contract, sender, formatDateTime(datetime, '%Y-%m-%d') as dt, count(1) as count0
                           from "MV_swaps"
                           where amount0_in > 0
                             and amount1_out > 0
                             and contract = '${pairs_address_info}'
                           GROUP BY contract, sender, formatDateTime(datetime, '%Y-%m-%d')) as t2
                on t1.contract = t2.contract and t1.sender = t2.sender and t1.dt = t2.dt
            any left join (SELECT contract,sender,formatDateTime(datetime, '%Y-%m-%d') as dt,count(1) as count1 from "MV_swaps" where amount0_out>0 and amount1_in>0 and contract='${pairs_address_info}' GROUP BY contract,sender,formatDateTime(datetime, '%Y-%m-%d')) as t3
        on t1.contract=t3.contract and t1.sender=t3.sender and t1.dt=t3.dt
        ORDER BY ${orderBuyByName} DESC
            LIMIT 20
            )
               , sell_list AS (

                    SELECT t1.amount0_out, t1.amount1_out, t1.amount0_in, t1.amount1_in, t1.address_from, t2.count0, t3.count1
                    from (
                        SELECT
                        contract, sender, formatDateTime(datetime, '%Y-%m-%d') as dt, SUM (amount0_out) AS amount0_out, SUM (amount1_out) AS amount1_out, SUM (amount0_in) AS amount0_in, SUM (amount1_in) AS amount1_in, sender AS address_from
                        FROM
                        "MV_swaps"
                        WHERE
                        datetime >=${q_0_time}
                        AND datetime < ${q_1_time}
                        AND contract='${pairs_address_info}'
                        GROUP BY
                        contract, sender, formatDateTime(datetime, '%Y-%m-%d')
                        ) as t1
                        any left JOIN (SELECT contract, sender, formatDateTime(datetime, '%Y-%m-%d') as dt, count (1) as count0 from "MV_swaps" where amount0_in>0 and amount1_out>0 and contract='${pairs_address_info}' GROUP BY contract, sender, formatDateTime(datetime, '%Y-%m-%d')) as t2
                    on t1.contract=t2.contract and t1.sender=t2.sender and t1.dt=t2.dt
                        any left join (SELECT contract,sender,formatDateTime(datetime, '%Y-%m-%d') as dt, count (1) as count1 from "MV_swaps" where amount0_out>0 and amount1_in>0 and contract='${pairs_address_info}' GROUP BY contract,sender,formatDateTime(datetime, '%Y-%m-%d')) as t3
                        on t1.contract=t3.contract and t1.sender=t3.sender and t1.dt=t3.dt
                    ORDER BY ${orderSellByName} DESC
                        LIMIT 2000
            )
        SELECT
            amount0_out,
            amount1_out,
            amount0_in,
            amount1_in,
            count0,
            count1,
            address_from,
            'buy' as type -- fixed syntax: using 'type' => 'buy'
        FROM buy_list
        UNION ALL
        SELECT
            amount0_out,
            amount1_out,
            amount0_in,
            amount1_in,
            count0,
            count1,
            address_from,
            'sell' as type -- fixed syntax: using 'type' => 'sell'
        FROM sell_list;
    `;


    // let data = await PqueryQL_sonar(arg);

    let data_list = await HQL_sonar(arg);
    let buy_list = {'json': []};
    let sell_list = {'json': []};
    for (let i = 0; i < data_list.length; i++) {
        let row = data_list[i];
        if (row['type'] === 'buy') {
            buy_list.json.push(row);
        } else {
            sell_list.json.push(row);
        }
    }
    let data = {
        'rows': [buy_list, sell_list]
    }
    // console.log(data)
    let callData = {
        buy: [],
        sell: [],
        buyAmount: new BigNumber(0),
        sellAmount: new BigNumber(0),
        buyNum: new BigNumber(0),
        sellNum: new BigNumber(0)
    };
    let isNull = true;//默认查询为空的标记
    if (data && data.rows.length > 0) {
        let divisor;
        //不分情况decimals有为0的情况，默认填充18
        if (pair?.decimals) {
            divisor = new BigNumber(10).pow(pair?.decimals)
        } else {
            divisor = new BigNumber(10).pow(18)
        }
        let priceUsdt = await getTokenPsqlUsdt(params.token_address);
        let buyList = (data.rows[0]).json;
        let sellList = (data.rows[1]).json;
        // console.log(buyList, sellList)
        if (buyList) {
            for (let i = 0; i < buyList.length; i++) {
                isNull = false;
                let item = buyList[i];
                let info = {
                    address_from: item.address_from, type: 'buy'
                }
                if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                    info.count = item.count0;
                    let amount = new BigNumber(item?.amount0_out).div(divisor).toFixed()
                    info.num = amount;
                    info.amount = amount * priceUsdt;
                } else {
                    info.count = item.count1;
                    info.num = item.amount1_out.toString();
                    let amount = new BigNumber(item?.amount1_out).div(divisor).toFixed()
                    info.num = amount;
                    info.amount = amount * priceUsdt;
                }
                callData.buy.push(info);
                callData.buyAmount = callData.buyAmount.plus(new BigNumber(info.amount));
                callData.buyNum = callData.buyNum.plus(new BigNumber(info.num));
            }
        }
        if (sellList) {
            for (let i = 0; i < sellList.length; i++) {
                isNull = false;
                let item = sellList[i];
                let info = {
                    address_from: item.address_from, type: 'sell'
                }
                if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                    info.count = item.count0;
                    let amount = new BigNumber(item?.amount0_in).div(divisor).toFixed()
                    info.num = amount;
                    info.amount = amount * priceUsdt;
                } else {
                    info.count = item.count1;
                    info.num = item.amount1_out.toString();
                    let amount = new BigNumber(item?.amount1_in).div(divisor).toFixed()
                    info.num = amount;
                    info.amount = amount * priceUsdt;
                }
                callData.sell.push(info);
                callData.sellAmount = callData.sellAmount.plus(new BigNumber(info.amount));
                callData.sellNum = callData.sellNum.plus(new BigNumber(info.num));
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 池子大小图表
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getPool_Chart(request, result) {
    let params = request.body;
    if (!params.type || !params.table_name || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    let q_0_time;
    let q_1_time;
    if (params.str_time && params.end_time) {//如果有自定义内容
        q_0_time = getTimeStamp(formatDate_UTCDate(new Date(params.str_time)).getTime());
        q_1_time = getTimeStamp(formatDate_UTCDate(new Date(params.end_time)).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    }
    let type;
    let type_sql;
    switch (params.type) {
        case PeriodEnum.DAY:
            type = 'day';
            break;
        case PeriodEnum.WEEK:
            type = 'year,week';
            type_sql = 'EXTRACT(year FROM to_timestamp(timestamp)) AS year,EXTRACT(week FROM to_timestamp(timestamp)) AS week';
            break;
        case PeriodEnum.MONTH:
            type = 'year,month';
            type_sql = 'EXTRACT(year FROM to_timestamp(timestamp)) AS year,EXTRACT(month FROM to_timestamp(timestamp)) AS month';
            break;
        case PeriodEnum.YEAR:
            type = 'year';
            type_sql = 'EXTRACT(year FROM to_timestamp(timestamp)) AS year';
            break;
    }
    if (!type) {
        result.json({
            code: 400, msg: `${params.type},无效参数`, data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let token = await getTokenCache(params.token_address);
    if (token) {
        params.pair_address = token.pair_address;
        if (!params.pair_address) {
            let pair = await getTokenToPair(token);
            params.pair_address = pair.pairAddress;
        }
        if (!params.pair_address || params.pair_address == "") {
            result.json({
                code: 500, msg: '当前token没有交易对！', data: null
            });
            return;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    //数据库全是小写
    params.pair_address = params.pair_address.toLowerCase();
//查询pair
    let pairData = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", params.pair_address);
    if (pairData) {
        pairData = JSON.parse(pairData);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let tokenData_0 = await getTokenCache(pairData.token0_addr);
    if (!tokenData_0) {
        result.json({
            code: 500, msg: '当前token0数据错误！', data: null
        });
        return;
    }
    let tokenData_1 = await getTokenCache(pairData.token1_addr);
    if (!tokenData_1) {
        result.json({
            code: 500, msg: '当前token1数据错误！', data: null
        });
        return;
    }
    let tokenIndexMap = new Map();//整理token对应的index
    tokenIndexMap.set(tokenData_0.address, 'reserve0');//这里对应数据库下标位置
    tokenIndexMap.set(tokenData_1.address, 'reserve1');
    let tokenDecimalMap = new Map();//整理token对应的精度decimals
    let tokenNameMap = new Map();//整理token对应的精度name
    tokenDecimalMap.set(tokenData_0.address, Number(tokenData_0.decimals));
    tokenDecimalMap.set(tokenData_1.address, Number(tokenData_1.decimals));
    tokenNameMap.set(tokenData_0.address, tokenData_0.symbol);
    tokenNameMap.set(tokenData_1.address, tokenData_1.symbol);
    /*
      查询趋势图
     */
    params.pair_address = params.pair_address.replace("0x", "");//数据库全是小写没有0x
    let arg;
    if (type == 'day') {
        arg = `select t1.address, t1.reserve0, t1.reserve1, t1.timestamp
               from pool_chart t1
               where t1.address = '${params.pair_address}'`;
        if (q_0_time && q_1_time) {
            arg = arg + ` and t1.timestamp >= ${q_0_time} and t1.timestamp <= ${q_1_time} order by t1.timestamp desc`;
        } else {
            arg = arg + ` order by t1.timestamp desc limit 50`;
        }
    } else {
        arg = `select t1.address, t1.reserve0, t1.reserve1, t1.timestamp
               from pool_chart t1
                        RIGHT join
                    (SELECT max(timestamp) as t_max, ${type_sql}
                     FROM pool_chart
                     where address = '${params.pair_address}'
                     GROUP BY ${type}
                     order by t_max desc LIMIT 50) t2 on t1.timestamp = t2.t_max
               where t1.address = '${params.pair_address}'
               order by t1.timestamp desc limit 50`;
    }
    let data = await PqueryQL_sonar(arg);
    let callData = {chart: {time: [], value: [], filter: []}, info: {}};
    if (data.rows.length > 0) {
        let reserves = [];
        let reserve0 = '';
        let reserve1 = '';
        let times = [];
        let pool = '';
        let tokenIndex = tokenIndexMap.get(params.token_address);
        for (let i = 0; i < data.rows.length; i++) {
            let time = '';
            switch (params.type) {
                case "1D":
                    // 2012-02-02 11:11:11
                    time = formatTimestamp(data.rows[i].timestamp).substring(5, 10);
                    // time = formatTimestamp(data.rows[i][3]);
                    break;
                case "1W":
                    time = formatTimestamp(data.rows[i].timestamp).substring(5, 10);
                    break;
                case "1MO":
                    time = formatTimestamp(data.rows[i].timestamp).substring(0, 7);
                    break;
                case "1Y":
                    time = formatTimestamp(data.rows[i].timestamp).substring(0, 4);
                    break;
            }
            let tokenDecimal = tokenDecimalMap.get(params.token_address);
            reserves.push(new BigNumber(formatUnits(data.rows[i][tokenIndex], tokenDecimal)).multipliedBy(2).toFixed());
            times.push(time);
            if (i == 0) {
                let tokenIndex0 = tokenIndexMap.get(tokenData_0.address);
                let tokenDecimal0 = tokenDecimalMap.get(tokenData_0.address);
                let tokenIndex1 = tokenIndexMap.get(tokenData_1.address);
                let tokenDecimal1 = tokenDecimalMap.get(tokenData_1.address);
                reserve0 = new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                reserve1 = new BigNumber(formatUnits(data.rows[i][tokenIndex1], tokenDecimal1)).toFixed();
                pool = reserves[0];
            }
        }
        callData.chart.time = times;
        callData.chart.value = reserves;
        callData.info.reserve0_name = tokenNameMap.get(tokenData_0.address);
        callData.info.reserve1_name = tokenNameMap.get(tokenData_1.address);
        callData.info.pair_name = callData.info.reserve0_name + "/" + callData.info.reserve1_name;
        callData.info.pool = pool;
        callData.info.reserve0 = reserve0;
        callData.info.reserve1 = reserve1;
        result.json({
            code: 200, msg: '查询成功！', data: callData
        });
    } else {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
    }
}

/**
 * 底池币（占比/走势图）
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getPoolRatio_Chart(request, result) {
    let params = request.body;
    if (!params.type || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    let q_0_time;
    let q_1_time;
    if (params.str_time && params.end_time) {//如果有自定义内容
        q_0_time = getTimeStamp(formatDate_UTCDate(new Date(params.str_time)).getTime());
        q_1_time = getTimeStamp(formatDate_UTCDate(new Date(params.end_time)).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    }
    let type;
    let type_sql;
    switch (params.type) {
        case PeriodEnum.DAY:
            type = 'day';
            break;
        case PeriodEnum.WEEK:
            type = 'year,week';
            type_sql = 'EXTRACT(year FROM to_timestamp(timestamp)) AS year,EXTRACT(week FROM to_timestamp(timestamp)) AS week';
            break;
        case PeriodEnum.MONTH:
            type = 'year,month';
            type_sql = 'EXTRACT(year FROM to_timestamp(timestamp)) AS year,EXTRACT(month FROM to_timestamp(timestamp)) AS month';
            break;
        case PeriodEnum.YEAR:
            type = 'year';
            type_sql = 'EXTRACT(year FROM to_timestamp(timestamp)) AS year';
            break;
    }
    if (!type) {
        result.json({
            code: 400, msg: `${params.type},无效参数`, data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let total_supply;
    let token = await getTokenCache(params.token_address);
    if (token) {
        total_supply = token.total_supply;
        if (!total_supply) {
            let tokenContract = new Contract(params.token_address, allAbi, provider);
            token.total_supply = await tokenContract.totalSupply();//总共发行量
            total_supply = formatStrDecimal(new BigNumber(token.total_supply).toFixed(), token.decimals);
            token.total_supply = total_supply;
            await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", params.token_address, JSON.stringify(token));
        }
        params.pair_address = token.pair_address;
        if (!params.pair_address) {
            let pair = await getTokenToPair(token);
            params.pair_address = pair.pairAddress;
        }
        if (!params.pair_address || params.pair_address == "") {
            result.json({
                code: 500, msg: '当前token没有交易对！', data: null
            });
            return;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    total_supply = await fixTotal(total_supply, token.decimals);
    params.pair_address = params.pair_address.toLowerCase();//数据库全是小写
    //查询pair
    let pairData = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", params.pair_address);
    if (pairData) {
        pairData = JSON.parse(pairData);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let tokenData_0 = await getTokenCache(pairData.token0_addr);
    if (!tokenData_0) {
        result.json({
            code: 500, msg: '当前token0数据错误！', data: null
        });
        return;
    }
    let tokenData_1 = await getTokenCache(pairData.token1_addr);
    if (!tokenData_1) {
        result.json({
            code: 500, msg: '当前token1数据错误！', data: null
        });
        return;
    }
    let tokenIndexMap = new Map();//整理token对应的index
    tokenIndexMap.set(tokenData_0.address, 'reserve0');//这里对应数据库下标位置
    tokenIndexMap.set(tokenData_1.address, 'reserve1');
    let tokenDecimalMap = new Map();//整理token对应的精度decimals
    let tokenNameMap = new Map();//整理token对应的精度name
    tokenDecimalMap.set(tokenData_0.address, Number(tokenData_0.decimals));
    tokenDecimalMap.set(tokenData_1.address, Number(tokenData_1.decimals));
    tokenNameMap.set(tokenData_0.address, tokenData_0.symbol);
    tokenNameMap.set(tokenData_1.address, tokenData_1.symbol);
    let tokenDecimal = tokenDecimalMap.get(params.token_address);
    /*
      查询趋势图
     */
    params.pair_address = params.pair_address.replace("0x", "");//数据库全是小写没有0x
    let arg;
    if (type == 'day') {
        arg = `select t1.address, t1.reserve0, t1.reserve1, t1.timestamp
               from pool_chart t1
               where t1.address = '${params.pair_address}'`;
        if (q_0_time && q_1_time) {
            arg = arg + ` and t1.timestamp >= ${q_0_time} and t1.timestamp <= ${q_1_time} order by t1.timestamp desc`;
        } else {
            arg = arg + ` order by t1.timestamp desc limit 50`;
        }
    } else {
        arg = `select t1.address, t1.reserve0, t1.reserve1, t1.timestamp
               from pool_chart t1
                        RIGHT join
                    (SELECT max(timestamp) as t_max, ${type_sql}
                     FROM pool_chart
                     where address = '${params.pair_address}'
                     GROUP BY ${type}
                     order by t_max desc LIMIT 50) t2
                    on t1.timestamp = t2.t_max
               where t1.address = '${params.pair_address}'
               order by t1.timestamp desc limit 50`;
    }
    let data = await PqueryQL_sonar(arg);
    let callData = {chart: {time: [], value: [], filter: []}, info: {}};
    if (data.rows.length > 0) {
        let reserves = [];
        let reserve0 = '';
        let reserve1 = '';
        let times = [];
        let tokenIndex = tokenIndexMap.get(params.token_address);
        for (let i = 0; i < data.rows.length; i++) {
            let time = '';
            let jsonColumn = data.rows[i];
            switch (params.type) {
                case "1D":
                    // 2012-02-02 11:11:11
                    time = formatTimestamp(jsonColumn.timestamp).substring(5, 10);
                    // time = formatTimestamp(data.rows[i][3]);
                    break;
                case "1W":
                    time = formatTimestamp(jsonColumn.timestamp).substring(5, 10);
                    break;
                case "1MO":
                    time = formatTimestamp(jsonColumn.timestamp).substring(0, 7);
                    break;
                case "1Y":
                    time = formatTimestamp(jsonColumn.timestamp).substring(0, 4);
                    break;
            }
            reserves.push(new BigNumber(formatUnits(jsonColumn[tokenIndex], tokenDecimal)).toFixed());
            times.push(time);
            if (i == 0) {
                let tokenIndex0 = tokenIndexMap.get(tokenData_0.address);
                let tokenDecimal0 = tokenDecimalMap.get(tokenData_0.address);
                let tokenIndex1 = tokenIndexMap.get(tokenData_1.address);
                let tokenDecimal1 = tokenDecimalMap.get(tokenData_1.address);
                reserve0 = new BigNumber(formatUnits(jsonColumn[tokenIndex0], tokenDecimal0)).toFixed();
                reserve1 = new BigNumber(formatUnits(jsonColumn[tokenIndex1], tokenDecimal1)).toFixed();
            }
        }
        callData.chart.time = times;
        callData.chart.value = reserves;
        callData.info.reserve0_name = tokenData_0.address;
        callData.info.reserve1_name = tokenData_1.address;
        callData.info.reserve0 = reserve0;
        callData.info.reserve1 = reserve1;
    }
    /**
     * 查询持有该合约的合约地址
     * @type {string}
     */
    params.token_address = params.token_address.replace("0x", "");
    let arg_contract = `WITH balance_changes_AllAddressNow AS ( -- 获取当前合约持有的所有地址(最后时刻数据)
        select contract, owner, f_time, new_balance
        from MV_LAST_balance_changes_v2 FINAL
        where contract = '${params.token_address}'
          and is_contract = true
          and new_balance > 0),
                             balance_changes_ContractAddressNowSum AS ( -- 获取持有当前合约币的所有合约地址币量(最后时刻数据)
                                 select sum(t1.new_balance) as contract_sum FROM balance_changes_AllAddressNow t1)
                        SELECT toJSONString(map(-- 合约持币总量（当前区块数据）
                                'type', 'all_contract_sum',
                                'balance', toString(contract_sum)
                                            )) AS json
                        FROM balance_changes_ContractAddressNowSum`;
    let data_contract = await HQL_sonar(arg_contract);
    callData.info.all_contract_sum = 0;
    if (data_contract.length > 0) {
        let jsonColumn = JSON.parse(data_contract[0].json);
        if (jsonColumn.balance) {
            callData.info.all_contract_sum = formatStrDecimal(jsonColumn.balance, token.decimals);//记录所有合约的持币
        }
    }
    /**
     * 查询持有该合约的黑洞地址
     * @type {string}
     */
    let arg_black = `WITH to_black_holes_AddressAll AS ( -- 查询所有黑洞持有总和
        select address, sum(sum_value) as value
                     from MV_SUM_LAST_black_transfers
                     where address = '${params.token_address}'
                     group by address
                         )
    SELECT toJSONString(map(-- 合约持币总量（当前区块数据）
            'type', 'all_black_sum',
            'address', toString(address),
            'value', toString(value)
                        )) AS json
    FROM to_black_holes_AddressAll`;
    let data_black = await HQL_sonar(arg_black);
    callData.info.all_black_sum = 0;
    if (data_black.length > 0) {
        let jsonColumn = JSON.parse(data_black[0].json);
        if (jsonColumn.value) {
            callData.info.all_black_sum = formatStrDecimal(jsonColumn.value, token.decimals);//记录所有黑洞的持币
        }
    }
    let all_contract_sum = new BigNumber(callData.info.all_contract_sum);
    let all_black_sum = new BigNumber(callData.info.all_black_sum);
    callData.info.circulation = new BigNumber(total_supply).minus(all_contract_sum).minus(all_black_sum).toFixed();
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 修补null值
 */
async function fixNull(fixArray, fixfilter, condition, type) {
    let timeMap = {};
    let fixfilter_length = fixfilter.length;
    let fixKeys = [];
    //获取fix的key
    for (let key in fixArray) {
        fixKeys.push(key);
    }
    if (fixfilter_length == 0) {//如果需要修补的内容为空则填充7天
        let currentDate = new Date();
        if (fixKeys.length > 0) {
            let dateArray = TimestampDiff_to_DateArray(currentDate.getTime() - 7 * 86400000, currentDate.getTime());
            for (let i = 0; i < dateArray.length; i++) {
                for (let n = 0; n < fixKeys.length; n++) {
                    let item = fixArray[fixKeys[n]];
                    let clone = {
                        contract: item.contract, address: item.address, balance: item.balance, time: dateArray[i]
                    };//创建新的对象避免引用数据被污染
                    fixfilter.push(clone);
                }
            }
        }
    }
    for (let i = 0; i < fixfilter.length; i++) {
        if (!timeMap[fixfilter[i]['time']]) {//如果没有当前address则创建
            timeMap[fixfilter[i]['time']] = {};
        }
        timeMap[fixfilter[i]['time']][fixfilter[i]['address']] = fixfilter[i];
    }
    //正序时间
    let timeMapKeys = [];
    for (let key in timeMap) {
        timeMapKeys.push(key);
    }
    timeMapKeys.sort(function (time0, time1) {
        return new Date(time0).getTime() - new Date(time1).getTime();
    });
    /**
     * 只有当需要修补的数据大于0才可以修补
     */
    if (fixfilter_length > 0) {
        //进行修补
        //获取fix的key
        let fixAddressSet = new Set(fixKeys);//获取所有需要修补的地址集合
        for (let i = 0; i < timeMapKeys.length; i++) {//遍历每个时刻的数据组,从最新时刻往最老时刻推
            let addressMap = timeMap[timeMapKeys[i]];
            let addressList = [];
            for (let key in addressMap) {
                addressList.push(key);
            }
            let hasKeyArray = addressList.filter(x => fixAddressSet.has(x));//获取相同的部分
            // let hasKeyArray = addressList.filter(x => fixArray.has(x));//找出旧数据中相同的key
            hasKeyArray.forEach(key => {//旧数据中相同的key去掉，之后新数据相同的key会补上
                delete fixArray[key];
            });
            addressMap = {...fixArray, ...addressMap};
            // fixArray.forEach(address => {
            //     let clone = {type: 'chart', address: address, balance: fixArray[address].balance,time:timeMapKeys[i]};//创建新的对象避免引用数据被污染
            //     //addressMap填补
            //     addressMap[address] = clone;
            // });
            // let diffArray = fixAddressList.filter(x => !addressList.includes(x));//获取缺失的部分
            // diffArray.forEach(address => {
            //     let clone = {type: 'chart', address: address, balance: fixArray[address].balance};//创建新的对象避免引用数据被污染
            //     clone.time = timeMapKeys[i];//将fix数组的时间改为当前时间
            //     //addressMap填补
            //     addressMap[address] = clone;
            // });
            //把当前addressMap的地址集合作为下一次对比的fixAddressList
            fixAddressSet = new Set();
            for (let key in addressMap) {
                fixAddressSet.add(key);
            }
            //把当前addressMap作为下一次对比的fixArray
            fixArray = {...addressMap};//避免浅拷贝
            timeMap[timeMapKeys[i]] = {...addressMap};//更新原数组
        }
    }
    //筛选符合条件的数据
    /* （为什么sql查询的时候fixfilter是没有加
    last_balance >= ${params.q_0_balance} and last_balance <= ${params.q_1_balance} 条件获取到的，
    是因为必须保留当时时刻所有变动的数据来保证填补数据的完整性，last_balance >=的条件会把变动的部分筛除掉，
    就导致diffArray中会填补以前持有的地址，而相同地址最新变动没有覆盖上去）
     */
    if (condition) {
        for (let i = 0; i < timeMapKeys.length; i++) {//遍历每个时刻的数据组,从最新时刻往最老时刻推
            let addressMap = timeMap[timeMapKeys[i]];
            let dels = [];
            for (let key in addressMap) {
                if (addressMap[key].balance.isZero()) {//如果是0特殊处理
                    dels.push(key);
                    continue;
                }
                if (addressMap[key].balance.lt(condition.b_min) || addressMap[key].balance.gt(condition.b_max)) {//如果满足区间范围
                    dels.push(key);
                }
            }
            for (let l = 0; l < dels.length; l++) {
                delete addressMap[dels[l]];
            }
        }
    }
    let response = {};
    let p_threads = [];
    if (type) {//特殊处理
        switch (type) {
            case "rank_list"://统计排行榜
                /**
                 * 计算趋势图
                 */
                let result = [];
                timeMapKeys.map(timeKey => {//遍历每个时刻的数据组,从最新时刻往最老时刻推
                    let addressMap = timeMap[timeKey];
                    let balances = [];
                    for (let key in addressMap) {
                        let addressItem = addressMap[key];
                        balances.push(addressItem.balance.toFixed());
                    }
                    let balance = wasmSum.sum(balances);
                    result.push({
                        type: 'chart', balance, time: timeKey
                    });
                });
                response.result = result;//趋势图
                /**
                 * 计算排行榜
                 */
                let rank_list = timeMap[timeMapKeys[timeMapKeys.length - 1]];
                response.rank_list = [];//top排行榜图
                for (let key in rank_list) {
                    response.rank_list.push(rank_list[key]);
                }
                response.rank_list.sort(function (a, b) {
                    return b['balance'].minus(a['balance']);
                });
                let size = response.rank_list.length;
                if (size > 10) {
                    response.rank_list = response.rank_list.slice(0, 10);
                }
                /**
                 * 计算区间持有地址
                 */
                let addressKey = [];
                for (let key in rank_list) {
                    addressKey.push(rank_list[key]);
                }
                response.rangAddress = new Set(addressKey);
                break;
            case "address_count"://统计地址数
                let addressMap2;
                let result2 = [];
                for (let i = 0; i < timeMapKeys.length; i++) {//遍历每个时刻的数据组,从最新时刻往最老时刻推
                    let all_obj = {type: 'chart', count: 0, time: timeMapKeys[i]};
                    addressMap2 = timeMap[timeMapKeys[i]];
                    let addressKeys = [];
                    for (let key in addressMap2) {
                        addressKeys.push(addressMap2[key]);
                    }
                    all_obj.address = addressKeys;
                    all_obj.count = addressKeys.length;
                    result2.push(all_obj);
                }
                response = result2;
                break;
        }
    } else {//默认处理
        let result = [];
        timeMapKeys.map(timeKey => {//遍历每个时刻的数据组,从最新时刻往最老时刻推
            let addressMap = timeMap[timeKey];
            let balances = [];
            for (let key in addressMap) {
                let addressItem = addressMap[key];
                balances.push(addressItem.balance.toFixed());
            }
            let balance = wasmSum.sum(balances);
            result.push({
                type: 'chart', balance, time: timeKey
            });
        });
        response = result;
    }
    return response;
}

/**
 * 修补null值v2
 */
async function fixNull_v2(fixArray, fixfilter, condition, type) {
    let chart_last = fixArray['chart_last'].count_owner;//获取ago的人
    let chart_last_old = fixArray['chart_last_old'];//获取区间最早时刻每个owner的old人

    let chart_new = fixfilter.filter_new;

    let chart_old = fixfilter.filter_old;

    let fixfilter_length = chart_new.length;
    //获取fix的key
    if (fixfilter_length == 0) {//如果需要修补的内容为空则填充7天
        let old_count = new BigNumber(0);
        for (let i = 0; i < chart_last_old.length; i++) {//累计old人数
            old_count = old_count.plus(chart_last_old[i].count_owner);
        }
        let count_value = chart_last.plus(old_count);
        let currentDate = new Date();
        let dateArray = TimestampDiff_to_DateArray(currentDate.getTime() - 7 * 86400000, currentDate.getTime());
        for (let i = 0; i < dateArray.length; i++) {
            let clone = {
                value: count_value, time: dateArray[i]
            };//创建新的对象避免引用数据被污染
            chart_new.push(clone);
        }
    }
    if (fixfilter_length > 0) {
        chart_new.sort(function (item0, item1) {
            return new Date(item0.time).getTime() - new Date(item1.time).getTime();
        });
        chart_last_old.sort(function (item0, item1) {
            return new Date(item0.f_time_old).getTime() - new Date(item1.f_time_old).getTime();
        });
        chart_old.sort(function (item0, item1) {
            return new Date(item0.time).getTime() - new Date(item1.time).getTime();
        });
        //获取每个时刻旧数据
        let time_chart_old = {};
        for (let n = 0; n < chart_old.length; n++) {
            time_chart_old[chart_old[n].time] = chart_old[n].count_old;
        }

        let chart_last_old_countAll = new BigNumber(0);//合计大于chart_last时间的所有地址old数量
        let chart_last_old_map = {};
        for (let n = 0; n < chart_last_old.length; n++) {//累计old人数
            chart_last_old_countAll = chart_last_old_countAll.plus(chart_last_old[n].count_owner);
            chart_last_old_map[chart_last_old[n].f_time_old] = chart_last_old[n].count_owner;
        }
        chart_last = chart_last.plus(chart_last_old_countAll);//补全历史数据
        //进行修补
        //获取fix的key
        for (let i = 0; i < chart_new.length; i++) {//遍历每个时刻的数据组,从最新时刻往最老时刻推
            let addressMap = chart_new[i];
            let count_old = time_chart_old[addressMap.time];
            if (count_old) {
                chart_last = chart_last.minus(count_old);//是为了去掉上一时刻同样的owner已经加上的count_new部分
            }
            //把当前addressMap作为下一次对比的ago_last_value
            chart_last = chart_last.plus(addressMap.count_new);
            /*
              -count_old是为了去掉之前同样的owner已经加上的部分
              +count_new是加上当前同样的owner最新的数据
              +time_map_old[addressMap.time]是加上大于当天不包含当天的old数据
             */
            addressMap.value = chart_last;
        }
    }
    let response = {};
    if (type) {//特殊处理
        switch (type) {
            case "rank_list"://统计排行榜
                break;
            case "address_count"://统计地址数
                let result = [];
                for (let i = 0; i < chart_new.length; i++) {
                    result.push({
                        type: 'chart', count: chart_new[i].value, time: chart_new[i].time
                    });
                }
                response = result;
                break;
            case "address_balance"://统计持有币数
                let result_balance = [];
                for (let i = 0; i < chart_new.length; i++) {
                    result_balance.push({
                        type: 'chart', balance: chart_new[i].value, time: chart_new[i].time
                    });
                }
                response = result_balance;
                break;
        }
    }
    return response;
}

/**
 * 修补null值【合约内lp持有特殊修补处理】
 */
async function fixNull_transfer(fixArray, fixfilter_p, fixfilter_c, condition, type) {
    let response = [];
    let fixfilter_length = fixfilter_p.length;
    let fixKeys = [];
    //获取fix的key
    for (let key in fixArray) {
        fixKeys.push(key);
    }
    if (fixfilter_length == 0) {//如果需要修补的内容为空则填充7天
        let currentDate = new Date();
        let dateArray = TimestampDiff_to_DateArray(currentDate.getTime() - 7 * 86400000, currentDate.getTime());
        for (let i = 0; i < dateArray.length; i++) {
            for (let n = 0; n < fixKeys.length; n++) {
                let item = fixArray[fixKeys[n]];
                let clone = {r_from: item.p_from, r_to: item.p_to, value: item.value, time: dateArray[i]};//创建新的对象避免引用数据被污染
                fixfilter_p.push(clone);
            }
        }
    }
    let timeMap_p = {};
    let timeMap_c = {};
    //整合chart_PersonalToContract
    for (let i = 0; i < fixfilter_p.length; i++) {
        if (!timeMap_p[fixfilter_p[i]['time']]) {//如果没有当前address则创建
            timeMap_p[fixfilter_p[i]['time']] = {};
        }
        timeMap_p[fixfilter_p[i]['time']][fixfilter_p[i]['r_from'] + '_' + fixfilter_p[i]['r_to']] = fixfilter_p[i];
    }
    //整合chart_ContractToPersonal
    for (let i = 0; i < fixfilter_c.length; i++) {
        if (!timeMap_c[fixfilter_c[i]['time']]) {//如果没有当前address则创建
            timeMap_c[fixfilter_c[i]['time']] = {};
        }
        //这里r_to+r_from是因为后面需要根据此key对比，顺序需要倒转才可对应
        timeMap_c[fixfilter_c[i]['time']][fixfilter_c[i]['r_to'] + '_' + fixfilter_c[i]['r_from']] = fixfilter_c[i];
    }
    //正序时间
    let timeMapKeys_p = [];
    //获取fix的key
    for (let key in timeMap_p) {
        timeMapKeys_p.push(key);
    }
    timeMapKeys_p.sort(function (time0, time1) {
        return new Date(time0).getTime() - new Date(time1).getTime();
    });
    /**
     * 将时间区间之前的合约lp持有数量合并
     */
    if (fixfilter_length > 0) {
        let fixArray_keys = new Set(fixKeys);
        for (let i = 0; i < timeMapKeys_p.length; i++) {//从最早时间往后补充
            let time = timeMapKeys_p[i];
            let addressMap_p = timeMap_p[time];//当前时间用户转合约
            let addressMap_c = timeMap_c[time];//当前时间合约转用户
            let fixMap_p;
            let obj_p;
            let from_to;
            let addressMap_p_keys = [];
            //获取fix的key
            for (let key in addressMap_p) {
                addressMap_p_keys.push(key);
            }
            for (let i2 = 0; i2 < addressMap_p_keys.length; i2++) {
                try {
                    if (i2 == 157) {
                        console.log(i2);
                    }
                    from_to = addressMap_p_keys[i2];
                    fixMap_p = fixArray[from_to];
                    obj_p = addressMap_p[from_to];
                    if (fixMap_p) {
                        let v0 = obj_p.value.toFixed();
                        let v1 = fixMap_p.value.toFixed()
                        if (v0.includes("-") || v1.includes("-")) {
                            obj_p.value = obj_p.value.plus(fixMap_p.value);
                        } else {
                            let sumValue = wasmSum.sum([v0, v1]);
                            obj_p.value = new BigNumber(sumValue);//将用户转合约的之前的加起来
                        }
                    }
                    if (addressMap_c) {
                        let obj_c = addressMap_c[from_to];
                        if (obj_c) {
                            obj_p.value = obj_p.value.minus(obj_c.value);//将合约转回用户的减去
                        }
                    }
                } catch (e) {
                    console.log(i2);
                }
            }
            let hasKeyArray = addressMap_p_keys.filter(x => fixArray_keys.has(x));//获取相同的部分
            hasKeyArray.forEach(key => {//旧数据中相同的key去掉，之后新数据相同的key会补上
                delete fixArray[key];
            });
            addressMap_p = {...fixArray, ...addressMap_p};
            //把当前addressMap_p的地址集合作为下一次对比的fixArray_keys
            fixArray_keys = new Set();
            for (let key in addressMap_p) {
                fixArray_keys.add(key);
            }
            // diffArray.forEach(address => {
            //     let clone = {type: 'chart', value: fixArray[address].value, time: time};//创建新的对象避免引用数据被污染
            //     //addressMap填补
            //     addressMap_p[address] = clone;
            // });
            //把当前addressMap作为下一次对比的fixArray
            fixArray = {...addressMap_p};
            timeMap_p[time] = {...addressMap_p};
        }
    }
    /**
     * 整理趋势图
     */
    let result = [];
    let p_threads = [];
    switch (type) {
        case 'address_count':
            timeMapKeys_p.map(timeKey => {//遍历每个时刻的数据组,从最新时刻往最老时刻推
                let all_obj = {type: 'chart', address: new Set(), time: timeKey};
                let addressMap = timeMap_p[timeKey];
                for (let address in addressMap) {
                    if (addressMap[address].value.lt(0) || addressMap[address].value.isZero()) {//如果小于0或者等于0跳过不加
                        continue;
                    }
                    if (condition) {//处理筛选条件
                        if (addressMap[address].value.lt(condition.b_min) || addressMap[address].value.gt(condition.b_max)) {
                            continue;
                        }
                    }
                    address = address.split('_');
                    all_obj.address.add(address[0]);
                }
                result.push(all_obj);
            });
            response.result = result;//趋势图
            break;
        default:
            timeMapKeys_p.map(timeKey => {//遍历每个时刻的数据组,从最新时刻往最老时刻推
                let addressMap = timeMap_p[timeKey];
                let dels = [];//记录需要筛除的address
                for (let address in addressMap) {
                    if (addressMap[address].value.lt(0) || addressMap[address].value.isZero()) {//如果小于0或者等于0跳过不加
                        dels.push(address);
                        continue;
                    }
                    if (condition) {//处理筛选条件
                        if (addressMap[address].value.lt(condition.b_min) || addressMap[address].value.gt(condition.b_max)) {
                            dels.push(address);
                            continue;
                        }
                    }
                }
                for (let l = 0; l < dels.length; l++) {
                    delete addressMap[dels[l]];
                }
                let balances = [];
                let item;
                //获取fix的key
                for (let key in addressMap) {
                    item = addressMap[key];
                    balances.push(item.value.toFixed());
                }
                let balance = wasmSum.sum(balances);
                let all_obj = {type: 'chart', balance: balance, time: timeKey};
                result.push(all_obj);
            });
            response.result = result;//趋势图
            break;
    }
    /**
     *
     * 整理排行榜
     */
    switch (type) {
        case 'address_count':
            //不做处理
            break;
        default:
            let rang_count = new Set();//区间lp地址数
            let rank_list = timeMap_p[timeMapKeys_p[timeMapKeys_p.length - 1]];
            let rank_list_merge = {};
            for (let key in rank_list) {
                if (condition) {//处理筛选条件
                    if (rank_list[key].value.lt(0) || rank_list[key].value.isZero()) {//如果小于0或者等于0跳过不加
                        continue;
                    }
                    if (condition) {//处理筛选条件
                        if (rank_list[key].value.lt(condition.b_min) || rank_list[key].value.gt(condition.b_max)) {
                            continue;
                        }
                    }
                }
                let address = key.split("_")[0];//截取用户地址
                if (!rank_list_merge[address]) {
                    rank_list_merge[address] = rank_list[key];
                    rank_list_merge[address].address = address;
                    rank_list_merge[address].list = [rank_list[key].value.toFixed()];
                    delete rank_list_merge[address].r_from;
                    delete rank_list_merge[address].r_to;
                    delete rank_list_merge[address].value;
                } else {
                    rank_list_merge[address].list.push(rank_list[key].value.toFixed());
                }
                rang_count.add(address);
            }
            /**
             * 合计value
             */
            for (let address in rank_list_merge) {
                let sumValue = wasmSum.sum(rank_list_merge[address].list);
                rank_list_merge[address].balance = new BigNumber(sumValue);
                delete rank_list_merge[address].list;
            }
            response.rang_count = rang_count.size;
            response.rank_list = [];//top排行榜图
            for (let key in rank_list_merge) {
                response.rank_list.push(rank_list_merge[key]);
            }
            response.rank_list.sort(function (a, b) {
                return b['balance'].minus(a['balance']);
            });
            let size = response.rank_list.length;
            if (size > 10) {
                response.rank_list = response.rank_list.slice(0, 10);
            }
            break;
    }
    return response;
}

/**
 * 流通量占比
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getCirculationRatio_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    /**
     * 查询当前token
     * @type {string}
     */
    let tokenDecimal;
    let tokenDecimal_n;
    let total_supply;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
        tokenDecimal_n = Math.pow(0.1, token.decimals);
        total_supply = token.total_supply;
        if (!total_supply) {
            let tokenContract = new Contract(params.token_address, allAbi, provider);
            token.total_supply = await tokenContract.totalSupply();//总共发行量
            total_supply = formatStrDecimal(new BigNumber(token.total_supply).toFixed(), token.decimals);
            token.total_supply = total_supply;
            await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", params.token_address, JSON.stringify(token));
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    //如果没有转精度则转
    total_supply = await fixTotal(total_supply, token.decimals);
    total_supply = new BigNumber(total_supply).multipliedBy(new BigNumber(10).pow(token.decimals)).toFixed();
    params.token_address = params.token_address.replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2021-04-26 23:20:08")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.MONTH:
                table_type = 'M';
                q_0_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'Y';
                q_0_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let callData = {
        chart_contract: {time: [], value: [], filter: []},
        chart_black: {time: [], value: [], filter: []},
        chart: {time: [], value: []}
    };
    /**
     * 查询持有该合约的合约地址
     * @type {string}
     */
    let arg = `WITH balance_changes_AllAddressNow AS ( -- 获取当前合约持有的所有地址(最后时刻数据)
        select contract, owner, f_time, new_balance
        from MV_LAST_balance_changes_v2 FINAL
        where contract = '${params.token_address}'
          and is_contract = true
          and new_balance > 0),
                    balance_changes_ContractAddressNowSum AS ( -- 获取持有当前合约币的所有合约地址币量(最后时刻数据)
                        select sum(t1.new_balance) as contract_sum FROM balance_changes_AllAddressNow t1),
                    balance_changes_ContractAddressAgo AS ( -- 筛选查询时间之前的最后的持有当前合约币的合约地址
                        select contract, owner, argMax(new_balance, f_time) as last_balance, is_contract
                        from MV_${table_type}_new_balance_changes_v2 FINAL
               where contract = '${params.token_address}'
                 and f_time <= ${q_0_time}
                 and is_contract = true
               GROUP BY contract, owner, is_contract
               HAVING last_balance
                    > 0
                   )
                    , balance_changes_ContractAddress AS ( -- 筛选持有当前合约币的合约地址
               SELECT contract, owner, new_balance, f_time, is_contract
               FROM MV_${table_type}_new_balance_changes_v2 FINAL
               where contract = '${params.token_address}'
                 and f_time
                   > ${q_0_time}
                 and f_time <= ${q_1_time}
                 and is_contract = true
                   )
                   , balance_changes_ContractAddress_DateH AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(小时)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d %H:00') as time
               from balance_changes_ContractAddress
                   ), balance_changes_ContractAddress_DateD AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(天)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d') as time
               from balance_changes_ContractAddress
                   ), balance_changes_ContractAddress_DateM AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(月)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m') as time
               from balance_changes_ContractAddress
                   ), balance_changes_ContractAddress_DateY AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(年)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y') as time
               from balance_changes_ContractAddress
                   )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(last_balance)
                        )) AS json
    FROM balance_changes_ContractAddressAgo
    union all
    SELECT toJSONString(map(
            'type', 'chart',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(new_balance),
            'time', toString(time)
                        )) AS json
    FROM balance_changes_ContractAddress_Date${table_type}
    union all
    SELECT toJSONString(map(-- 合约持币总量（当前区块数据）
            'type', 'all_contract_sum',
            'sum', toString(contract_sum)
                        )) AS json
    FROM balance_changes_ContractAddressNowSum`;
    let data_contract = await HQL_sonar(arg);
    let fixMap = {};
    let isNull = true;//默认查询为空的标记
    if (data_contract && data_contract.length > 10000000) {
        result.json({
            code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
        });
        return;
    }
    if (data_contract && data_contract.length > 0) {
        for (let i = 0; i < data_contract.length; i++) {
            let json = data_contract[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    fixMap[jsonColumn.address] = jsonColumn;
                    break;
                case "chart":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    callData.chart_contract.filter.push(jsonColumn);
                    break;
                case "all_contract_sum":
                    if (jsonColumn.sum) {
                        jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                        jsonColumn.sum = new BigNumber(jsonColumn.sum).toFixed();
                    }
                    callData.all_contract_sum = jsonColumn.sum;
                    break;
            }
        }
    }
    /**
     * 查询持有该合约的黑洞地址
     * @type {string}
     */
    let arg2 = `WITH to_black_holes_AddressAll AS ( -- 查询所有黑洞持有总和
        select address, SUM(sum_value) as value
                FROM MV_SUM_LAST_black_transfers FINAL
                where address = '${params.token_address}'
                group by address
                    ),
                    to_black_holes_AddressAgo AS ( -- 获取查询时间之前的最后的数据
                select address, SUM (sum_value) as value
                from MV_SUM_${table_type}_black_transfers FINAL
                where address = '${params.token_address}' and f_time <= ${q_0_time}
                group by address
                    ),
                    to_black_holes_Address AS ( -- 筛选持有当前合约币黑洞地址
                select address, sum_value as value, f_time
                FROM MV_SUM_${table_type}_black_transfers FINAL
                where address = '${params.token_address}'
                  and f_time
                    > ${q_0_time}
                  and f_time <= ${q_1_time}
                    )
                    , balance_changes_BlackAddress_DateD AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(小时)
                select address, SUM (value) as value, formatDateTime(f_time, '%Y-%m-%d') as time
                from to_black_holes_Address
                GROUP BY address, time
                    ),
                    balance_changes_BlackAddress_DateM AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(月)
                select address, SUM (value) as value, formatDateTime(f_time, '%Y-%m') as time
                from to_black_holes_Address
                GROUP BY address, time
                    ),
                    balance_changes_BlackAddress_DateY AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(年)
                select address, SUM (value) as value, formatDateTime(f_time, '%Y') as time
                from to_black_holes_Address
                GROUP BY address, time
                    )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'address', toString(address),
            'balance', toString(value)
                        )) AS json
    FROM to_black_holes_AddressAgo
    union all
    SELECT toJSONString(map(
            'type', 'chart',
            'address', toString(address),
            'balance', toString(value),
            'time', time
                        )) AS json
    FROM balance_changes_BlackAddress_Date${table_type}
    union all
    SELECT toJSONString(map(
            'type', 'all_black_sum',
            'address', toString(address),
            'balance', toString(value)
                        )) AS json
    FROM to_black_holes_AddressAll`;
    let data_black = await HQL_sonar(arg2);
    let fixMap2 = {};
    let isNull2 = true;//默认查询为空的标记
    if (data_black && data_black.length > 0) {
        for (let i = 0; i < data_black.length; i++) {
            let json = data_black[i].json;
            isNull2 = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.balance) {
                        // jsonColumn.value = formatStrDecimal(jsonColumn.value, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    fixMap2[jsonColumn.address] = jsonColumn;
                    break;
                case "chart":
                    if (jsonColumn.balance) {
                        // jsonColumn.value = formatStrDecimal(jsonColumn.value, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    callData.chart_black.filter.push(jsonColumn);
                    break;
                case "all_black_sum":
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance).toFixed();
                    }
                    callData.all_black_sum = jsonColumn.balance;
                    break;
            }
        }
    }
    /**
     * 处理两组数据
     */
    let timeKeyIndex_black = {};//记录黑洞地址的time时间对应的value下标
    let timeKeyIndex_contract = {};//记录合约地址的time时间对应的value下标
    if (callData.chart_black.filter.length > 0) {
        //修复黑洞
        for (let i = 0; i < callData.chart_black.filter.length; i++) {
            let fixBlack = callData.chart_black.filter[i];
            fixBlack.balance = fixBlack.balance.plus(fixMap2[fixBlack.address].balance);
            fixMap2[fixBlack.address].balance = fixBlack.balance;
        }
        callData.chart_black.filter.sort(function (item0, item1) {
            return new Date(item0.time).getTime() - new Date(item1.time).getTime();
        });
        let jsonColumn0;
        let jsonColumn0_w;//一周特殊处理变量
        for (let i = 0; i < callData.chart_black.filter.length; i++) {
            jsonColumn0 = callData.chart_black.filter[i];
            switch (params.type) {
                case "1D":
                    // jsonColumn0.time = jsonColumn0.time.substring(5, 10);
                    // callData.chart_black.time.push(jsonColumn0.time);
                    // callData.chart_black.value.push(jsonColumn0.balance);
                    timeKeyIndex_black[jsonColumn0.time] = i;
                    break;
                case "1W":
                    if (i == 0) {//第一个数据必存
                        jsonColumn0.flag = true;
                        jsonColumn0_w = jsonColumn0;
                    } else {//从第二个开始需要判断是否相差一周
                        if (Math.abs(new Date(jsonColumn0_w.time).getTime() - new Date(jsonColumn0.time).getTime()) >= 7 * 86400000) {
                            //如果满足相差一周则标记true
                            jsonColumn0.flag = true;
                            jsonColumn0_w = jsonColumn0;
                        } else {
                            //如果不满足相差一周则标记false
                            jsonColumn0.flag = false;
                        }
                    }
                    if (jsonColumn0.flag) {//如果为true则记录
                        // callData.chart_black.time.push(jsonColumn0.time);
                        // callData.chart_black.value.push(jsonColumn0.balance);
                        timeKeyIndex_black[jsonColumn0.time] = i;
                    }
                    break;
                case "1MO":
                    // jsonColumn0.time = jsonColumn0.time.substring(0, 7);
                    // callData.chart_black.time.push(jsonColumn0.time);
                    // callData.chart_black.value.push(jsonColumn0.balance);
                    timeKeyIndex_black[jsonColumn0.time] = i;
                case "1Y":
                    // jsonColumn0.time = jsonColumn0.time.substring(0, 4);
                    // callData.chart_black.time.push(jsonColumn0.time);
                    // callData.chart_black.value.push(jsonColumn0.balance);
                    timeKeyIndex_black[jsonColumn0.time] = i;
                    break;
            }
        }
    }
    if (callData.chart_contract.filter.length > 0) {
        //修复合约
        callData.chart_contract.filter = await fixNull(fixMap, callData.chart_contract.filter);
        let jsonColumn;
        let jsonColumn_w;//一周特殊处理变量
        for (let i = 0; i < callData.chart_contract.filter.length; i++) {
            jsonColumn = callData.chart_contract.filter[i];
            switch (params.type) {
                case "1D":
                    // jsonColumn.time = jsonColumn.time.substring(5, 10);
                    // callData.chart_contract.time.push(jsonColumn.time);
                    // callData.chart_contract.value.push(jsonColumn.balance);
                    timeKeyIndex_contract[jsonColumn.time] = i;
                    break;
                case "1W":
                    if (i == 0) {//第一个数据必存
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {//从第二个开始需要判断是否相差一周
                        if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                            //如果满足相差一周则标记true
                            jsonColumn.flag = true;
                            jsonColumn_w = jsonColumn;
                        } else {
                            //如果不满足相差一周则标记false
                            jsonColumn.flag = false;
                        }
                    }
                    if (jsonColumn.flag) {//如果为true则记录
                        // callData.chart_contract.time.push(jsonColumn.time);
                        // callData.chart_contract.value.push(jsonColumn.balance);
                        timeKeyIndex_contract[jsonColumn.time] = i;
                    }
                    break;
                case "1MO":
                    // jsonColumn.time = jsonColumn.time.substring(0, 7);
                    // callData.chart_contract.time.push(jsonColumn.time);
                    // callData.chart_contract.value.push(jsonColumn.balance);
                    timeKeyIndex_contract[jsonColumn.time] = i;
                    break;
                case "1Y":
                    // jsonColumn.time = jsonColumn.time.substring(0, 4);
                    // callData.chart_contract.time.push(jsonColumn.time);
                    // callData.chart_contract.value.push(jsonColumn.balance);
                    timeKeyIndex_contract[jsonColumn.time] = i;
                    break;
            }
        }
    }
    /**
     * 黑洞和合约的最终合并
     */
    let timeKeyIndex_contract_keys = Object.keys(timeKeyIndex_contract);
    let timeKeyIndex_black_keys = Object.keys(timeKeyIndex_black);
    let timeKeyIndex_keys = new Set([...timeKeyIndex_contract_keys, ...timeKeyIndex_black_keys]);
    let timeKeyArray = Array.from(timeKeyIndex_keys);
    timeKeyArray.sort(function (item0, item1) {
        return new Date(item0).getTime() - new Date(item1).getTime();
    });
    for (let i = 0; i < timeKeyArray.length; i++) {//遍历时间把每个时间的黑洞合约持币合计
        let time = timeKeyArray[i];
        let index_contract = timeKeyIndex_contract[time];
        //如果当前时间没有合约持币则循环寻找补全
        if (index_contract === undefined) {
            let i_ago = i - 1;
            while (i_ago >= 0) {//往前找
                let time_2 = timeKeyArray[i_ago];
                index_contract = timeKeyIndex_contract[time_2];
                if (index_contract !== undefined) {
                    break;
                }
                i_ago--;
            }
        }
        if (index_contract === undefined) {
            let i_next = i + 1;
            while (i_next < timeKeyArray.length) {//往后找
                let time_2 = timeKeyArray[i_next];
                index_contract = timeKeyIndex_contract[time_2];
                if (index_contract !== undefined) {
                    break;
                }
                i_next++;
            }
        }
        let index_black = timeKeyIndex_black[time];
        //如果当前时间没有黑洞持币则循环寻找补全
        if (index_black === undefined) {
            let i_ago = i - 1;
            while (i_ago >= 0) {//往前找
                let time_2 = timeKeyArray[i_ago];
                index_black = timeKeyIndex_black[time_2];
                if (index_black !== undefined) {
                    break;
                }
                i_ago--;
            }
        }
        if (index_black === undefined) {
            let i_next = i + 1;
            while (i_next < timeKeyArray.length) {//往后找
                let time_2 = timeKeyArray[i_next];
                index_black = timeKeyIndex_black[time_2];
                if (index_black !== undefined) {
                    break;
                }
                i_next++;
            }
        }
        let contract_value = callData.chart_contract.filter[index_contract];
        let black_value = callData.chart_black.filter[index_black];
        if (contract_value === undefined) {
            contract_value = new BigNumber(0);
        } else {
            contract_value = contract_value.balance;
        }
        if (black_value === undefined) {
            black_value = new BigNumber(0);
        } else {
            black_value = black_value.balance;
        }
        switch (params.type) {
            case "1D":
                time = time.substring(5, 10);
                break;
            case "1W":
                time = time.substring(5, 10);
                break;
            case "1MO":
                time = time.substring(0, 7);
                break;
            case "1Y":
                time = time.substring(0, 4);
                break;
        }
        callData.chart.time.push(time);
        let value = new BigNumber(total_supply).minus(contract_value).minus(black_value);
        if (value.isLessThan(0)) {
            callData.chart.value.push(formatStrDecimal(total_supply, tokenDecimal));
        } else {
            callData.chart.value.push(formatStrDecimal(value.toFixed(), tokenDecimal));
        }
    }
    callData.total_supply = formatStrDecimal(total_supply, tokenDecimal);//发行量
    delete callData.chart_black;
    delete callData.chart_contract;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 换手率图表
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getChangeHand_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let token = await getTokenCache(params.token_address);
    if (token) {
        params.pair_address = token.pair_address;
        if (!params.pair_address) {
            let pair = await getTokenToPair(token);
            params.pair_address = pair.pairAddress;
        }
        if (!params.pair_address || params.pair_address == "") {
            result.json({
                code: 500, msg: '当前token没有交易对！', data: null
            });
            return;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    params.pair_address = params.pair_address.toLowerCase();//数据库全是小写
    let q_0_time;
    let q_1_time;
    if (params.str_time && params.end_time) {//如果有自定义内容
        q_0_time = getTimeStamp(formatDate_UTCDate(new Date(params.str_time)));
        q_1_time = getTimeStamp(formatDate_UTCDate(new Date(params.end_time)));
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 8640000000)));//往前退100天
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.WEEK:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 31536000000)));//往前退365天
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.MONTH:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000)));//往前退2年
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
            case PeriodEnum.YEAR:
                q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000)));//往前退2年
                q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
                break;
        }
    }
    let pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", params.pair_address);
    if (pair) {
        pair = JSON.parse(pair);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    //查询pair
    // console.log(pair, pair.address)
    let pairs_address_info = pair.address.toLowerCase().substring(2);
    let arg;
    switch (params.type) {
        case PeriodEnum.DAY:
            // arg = `
            //     SELECT count0                     AS token0_count,
            //            count1                     AS token1_count,
            //            (amount0_in + amount0_out) AS token0_sum,
            //            (amount1_in + amount1_out) AS token1_sum,
            //            date_timestamp AS timestamp,
            //         TO_CHAR(TO_TIMESTAMP(date_timestamp), 'YYYY-MM-DD') as formatted_timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     ORDER BY
            //         timestamp;
            // `;
            arg = `
                SELECT
                    t1.token0_sum as token0_sum,
                    t1.token1_sum as token1_sum,
                    t1.timestamp as timestamp,
                      t1.formatted_timestamp as formatted_timestamp,
                      t2.count0 as count0,
                      t3.count1 as count1
                from
                    (
                    SELECT
                    sum(amount0_in + amount0_out) AS token0_sum,
                    sum(amount1_in + amount1_out) AS token1_sum,
                    toUnixTimestamp (toDateTime (formatDateTime (datetime, '%Y-%m-%d'))) as timestamp,
                    formatDateTime (datetime, '%Y-%m-%d') AS formatted_timestamp
                    FROM
                    "MV_swaps"
                    WHERE
                    datetime >= ${q_0_time}
                    AND datetime < ${q_1_time}
                    and contract = '${pairs_address_info}'
                    GROUP BY
                    formatted_timestamp
                    ) as t1
                    left JOIN (SELECT formatDateTime (datetime, '%Y-%m-%d') as dt, count(1) as count0 from "MV_swaps" where amount0_in > 0 and amount1_out > 0 and contract = '${pairs_address_info}' GROUP BY formatDateTime (datetime, '%Y-%m-%d')) as t2 on t1.formatted_timestamp = t2.dt
                    left join (SELECT formatDateTime (datetime, '%Y-%m-%d') as dt, count(1) as count1 from "MV_swaps" where amount0_out > 0 and amount1_in > 0 and contract = '${pairs_address_info}' GROUP BY formatDateTime (datetime, '%Y-%m-%d')) as t3 on t1.formatted_timestamp = t3.dt
                ORDER BY
                    t1.formatted_timestamp

            `;
            break;
        case PeriodEnum.WEEK:
            // arg = `
            //     SELECT FLOOR((date_timestamp - ${q_0_time}) / (7 * 86400)) AS group_id,
            //            SUM(count0)                                         AS token0_count,
            //            SUM(count1)                                         AS token1_count,
            //            SUM(amount0_in) + SUM(amount0_out)                  AS token0_sum,
            //            SUM(amount1_in) + SUM(amount1_out)                  AS token1_sum,
            //            MAX(date_timestamp) AS timestamp,
            //         TO_CHAR(TO_TIMESTAMP(MAX(date_timestamp)), 'YYYY-MM-DD') as formatted_timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     GROUP BY
            //         group_id
            //     ORDER BY
            //         group_id;
            // `;
            arg = `
                SELECT
                    t1.*,
                    t2.count0,
                    t3.count1
                FROM
                    (
                        SELECT
                            FLOOR((toUnixTimestamp (toDateTime (datetime)) - ${q_0_time}) / (7 * 86400)) AS group_id,
                            SUM(amount0_in) + SUM(amount0_out) AS token0_sum,
                            SUM(amount1_in) + SUM(amount1_out) AS token1_sum,
                            MAX(toUnixTimestamp (toDateTime (datetime))) AS TIMESTAMP,
      formatDateTime (max(datetime), '%Y-%m-%d') AS formatted_timestamp
                        FROM
                            MV_swaps
                        WHERE
                            datetime >= ${q_0_time}
                          AND datetime < ${q_1_time}
                          AND contract ='${pairs_address_info}'
                        GROUP BY
                            group_id
                        ORDER BY
                            group_id
                    ) t1
                        LEFT JOIN (
                        SELECT
                            contract,
                            FLOOR((toUnixTimestamp (toDateTime (datetime)) - ${q_0_time}) / (7 * 86400)) AS group_id,
                            count(1) AS count0
                        FROM
                            "MV_swaps"
                        WHERE
                            amount0_in > 0
                          AND amount1_out > 0
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            contract,
                            group_id
                    ) AS t2 ON t1.group_id = t2.group_id
                        LEFT JOIN (
                        SELECT
                            contract,
                            FLOOR((toUnixTimestamp (toDateTime (datetime)) - ${q_0_time}) / (7 * 86400)) AS group_id,
                            count(1) AS count1
                        FROM
                            "MV_swaps"
                        WHERE
                            amount0_out > 0
                          AND amount1_in > 0
                          AND contract = '${pairs_address_info}'
                        GROUP BY
                            contract,
                            group_id
                    ) AS t3 ON t1.group_id = t3.group_id
            `;
            break;
        case PeriodEnum.MONTH:
            // arg = `
            //     SELECT EXTRACT(YEAR FROM TO_TIMESTAMP(date_timestamp)) AS year,
            //     EXTRACT(MONTH FROM TO_TIMESTAMP(date_timestamp)) AS month,
            //     SUM(count0) AS token0_count,
            //     SUM(count1) AS token1_count,
            //     SUM(amount0_in)+SUM(amount0_out) AS token0_sum,
            //     SUM(amount1_in)+SUM(amount1_out) AS token1_sum,
            //     MAX(date_timestamp) AS timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     GROUP BY
            //         year, month
            //     ORDER BY
            //         year, month;
            // `;
            arg = `
                SELECT
                    t1.year as year,
                    t1.month as month,
                    t1.token0_sum as token0_sum,
                    t1.token1_sum as token1_sum,
                    t1.TIMESTAMP as TIMESTAMP,
                    t2.count0 as token0_count,
                    t3.count1 as token1_count
                FROM
                    (
                    SELECT
                    toYear (datetime) AS year,
                    toMonth (datetime) AS month,
                    SUM(amount0_in) + SUM(amount0_out) AS token0_sum,
                    SUM(amount1_in) + SUM(amount1_out) AS token1_sum,
                    MAX(datetime) AS TIMESTAMP
                    FROM
                    "MV_swaps"
                    WHERE
                    datetime >= ${q_0_time}
                    AND datetime < ${q_1_time}
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year,
                    month
                    ORDER BY
                    year,
                    month
                    ) t1
                    LEFT JOIN (
                    SELECT
                    toYear (datetime) AS year,
                    toMonth (datetime) AS month,
                    count(1) AS count0
                    FROM
                    "MV_swaps"
                    WHERE
                    amount0_in > 0
                    AND amount1_out > 0
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year,
                    month
                    ) AS t2 ON t1.year = t2.year
                    AND t1.month = t2.
                    month LEFT JOIN (
                    SELECT
                    toYear (datetime) AS year,
                    toMonth (datetime) AS month,
                    count(1) AS count1
                    FROM
                    "MV_swaps"
                    WHERE
                    amount0_out > 0
                    AND amount1_in > 0
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year,
                    month
                    ) AS t3 ON t1.year = t3.year
                    AND t1.month = t3.month
            `;
            break;
        case PeriodEnum.YEAR:
            // arg = `
            //     SELECT EXTRACT(YEAR FROM TO_TIMESTAMP(date_timestamp)) AS year,
            //     SUM(count0) AS token0_count,
            //     SUM(count1) AS token1_count,
            //     SUM(amount0_in)+SUM(amount0_out) AS token0_sum,
            //     SUM(amount1_in)+SUM(amount1_out) AS token1_sum,
            //     MAX(date_timestamp) AS timestamp,
            //     TO_CHAR(TO_TIMESTAMP(MAX(date_timestamp)), 'YYYY-MM-DD') as formatted_timestamp
            //     FROM
            //         swaps
            //     WHERE
            //         date_timestamp >= ${q_0_time}
            //       AND date_timestamp
            //         < ${q_1_time}
            //       and address = '${pairs_address_info}'
            //     GROUP BY
            //         year
            //     ORDER BY
            //         year;
            // `;
            arg = `
                SELECT
                    t1.year as year,
                    t1.token0_sum as token0_sum,
                    t1.token1_sum as token1_sum,
                    t1.TIMESTAMP as TIMESTAMP,
                    t2.count0 as token0_count,
                    t3.count1 as token1_count
                FROM
                    (
                    SELECT
                    toYear (datetime) AS year,
                    SUM(amount0_in) + SUM(amount0_out) AS token0_sum,
                    SUM(amount1_in) + SUM(amount1_out) AS token1_sum,
                    MAX(datetime) AS TIMESTAMP
                    FROM
                    "MV_swaps"
                    WHERE
                    datetime >= ${q_0_time}
                    AND datetime < ${q_1_time}
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year
                    ORDER BY
                    year
                    ) t1
                    LEFT JOIN (
                    SELECT
                    toYear (datetime) AS year,
                    count(1) AS count0
                    FROM
                    "MV_swaps"
                    WHERE
                    amount0_in > 0
                    AND amount1_out > 0
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year
                    ) AS t2 ON t1.year = t2.year
                    LEFT JOIN (
                    SELECT
                    toYear (datetime) AS year,
                    count(1) AS count1
                    FROM
                    "MV_swaps"
                    WHERE
                    amount0_out > 0
                    AND amount1_in > 0
                    AND contract = '${pairs_address_info}'
                    GROUP BY
                    year
                    ) AS t3 ON t1.year = t3.year
            `;
            break;
    }
    // let data = await PqueryQL_sonar(arg);
    let rows = await HQL_sonar(arg);
    let data={
        'rows':rows
    }
    // console.log(data);
    let callData = {info: {}, chart: {time: [], value: []}};
    callData.info.token_num = await redis.hGet("token_max_total_supply", params.token_address);
    let isNull = true;//默认查询为空的标记
    if (data && data.rows.length > 0) {
        let divisor;
        //不分情况decimals有为0的情况，默认填充18
        if (pair?.decimals) {
            divisor = new BigNumber(10).pow(pair?.decimals)
        } else {
            divisor = new BigNumber(10).pow(18)
        }
        let priceUsdt = await getTokenPsqlUsdt(params.token_address);
        for (let i = 0; i < data.rows.length; i++) {
            let jsons = data.rows[i];
            // console.log("执行第", i)
            if (jsons) {
                isNull = false;
                switch (params.type) {
                    case PeriodEnum.DAY:
                        //查询1D的数据会生成formatted_timestamp字段，包含格式
                        callData.chart.time.push(jsons?.formatted_timestamp);
                        break;
                    case PeriodEnum.WEEK:
                        //查询1W的数据会生成formatted_timestamp字段，包含格式
                        callData.chart.time.push(jsons?.formatted_timestamp);
                        break;
                    case PeriodEnum.MONTH:
                        callData.chart.time.push(`${jsons?.year}-${jsons?.month}`);
                        break;
                    case PeriodEnum.YEAR:
                        callData.chart.time.push(`${jsons?.year}`);
                        break;
                }
                // console.log(jsons?.token0_sum, jsons?.token1_sum)
                // console.log(divisor.toString());
                let actualToken0 = new BigNumber(jsons?.token0_sum.toString()).div(divisor).toFixed();
                let actualToken1 = new BigNumber(jsons?.token1_sum.toString()).div(divisor).toFixed();
                //当前代币相对于pair来说是token1
                if (pair.token0_addr.toLowerCase() == params.token_address.toLowerCase()) {
                    callData.chart.value.push(actualToken0);
                } else {
                    callData.chart.value.push(actualToken1);
                }
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}


/**
 * 第二种处理
 */
async function getTokenHand_Chart_handle_v2(params, result, token, tokenDecimal, sql) {
    let data_0 = await HQL_sonar(sql.arg_0_v2);
    let data_1 = await HQL_sonar(sql.arg_1_v2);

    let data = [...data_0, ...data_1];
    let callData = {
        chart: {time: [], value: [], filter: [], filter_old: [], filter_new: []}, top: [], all_sum: {
            "type": "all_sum", "sum": token.total_supply
        }
    };
    let fixMap = {};//第一种fix方式
    let fixMap_v2 = {chart_last: null, chart_last_old: []};//第二种fix方式
    let isNull = true;//默认查询为空的标记
    // if(data && data.length > 10000000) {
    //     result.json({
    //         code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
    //     });
    //     return null;
    // }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.count_owner) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.count_owner = new BigNumber(jsonColumn.count_owner);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    // fixMap[jsonColumn.address] = jsonColumn;
                    fixMap_v2['chart_last'] = jsonColumn;
                    break;
                case "chart_last_old":
                    if (jsonColumn.count_owner) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.count_owner = new BigNumber(jsonColumn.count_owner);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    // fixMap[jsonColumn.address] = jsonColumn;
                    fixMap_v2['chart_last_old'].push(jsonColumn);
                    break;
                case "chart_new":
                    callData.chart.filter_new.push(jsonColumn);
                    break;
                case "chart_old":
                    callData.chart.filter_old.push(jsonColumn);
                    break;
                case "all_count":
                    callData.all_count = jsonColumn;
                    break;
                case "range_count":
                    callData.range_count = jsonColumn;
                    break;
                case "top":
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                    }
                    jsonColumn.is_contract = new Boolean(jsonColumn.is_contract);
                    callData.top.push(jsonColumn);
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return null;
    }
    //修复
    // callData.chart.filter = await fixNull(fixMap, callData.chart.filter, {b_min: b_min, b_max: b_max}, 'address_count');
    callData.chart.filter = await fixNull_v2(fixMap_v2, callData.chart, null, 'address_count');
    let jsonColumn;
    let jsonColumn_w;//一周特殊处理变量
    for (let i = 0; i < callData.chart.filter.length; i++) {
        jsonColumn = callData.chart.filter[i];
        switch (params.type) {
            case PeriodEnum.DAY:
                jsonColumn.time = jsonColumn.time.substring(5, 10);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(jsonColumn.count.toFixed());
                break;
            case PeriodEnum.WEEK:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_w = jsonColumn;
                } else {//从第二个开始需要判断是否相差一周
                    if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                        //如果满足相差一周则标记true
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {
                        //如果不满足相差一周则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(jsonColumn.count.toFixed());
                }
                break;
            case PeriodEnum.MONTH:
                callData.chart.time.push(jsonColumn.time.substring(0, 7));
                callData.chart.value.push(jsonColumn.count.toFixed());
                break;
            case PeriodEnum.YEAR:
                callData.chart.time.push(jsonColumn.time.substring(0, 4));
                callData.chart.value.push(jsonColumn.count.toFixed());
                break;
        }
    }
    delete callData.chart.filter;
    delete callData.chart.filter_old;
    delete callData.chart.filter_new;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 持币地址图表
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getTokenHand_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.token_address || !params.q_0_balance || !params.q_1_balance) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    // if (hasSpecial(params.token_address)) {
    //     result.json({
    //         code: 400, msg: '超出运算限额！', data: null
    //     });
    //     return;
    // }
    /**
     * 查询当前token
     * @type {string}
     */
    let b_min;//区间筛选balance
    let b_max;//区间筛选balance
    let tokenDecimal;
    let tokenDecimal_n;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
        tokenDecimal_n = Math.pow(0.1, tokenDecimal);
        if (params.q_0_balance != "0") {
            params.q_0_balance = new BigNumber(params.q_0_balance).multipliedBy(Math.pow(10, tokenDecimal));
        }
        if (params.q_1_balance != "0") {
            params.q_1_balance = new BigNumber(params.q_1_balance).multipliedBy(Math.pow(10, tokenDecimal));
        }
        b_min = params.q_0_balance;
        b_max = params.q_1_balance;
        //判断是否有total_supply
        if (!token.total_supply) {//注意还原!total_supply
            let tokenContract = new Contract(token.address, allAbi, provider);
            token.total_supply = await tokenContract.totalSupply();//总共发行量
            let total_supply = formatStrDecimal(new BigNumber(token.total_supply).toFixed(), token.decimals);
            token.total_supply = total_supply;
            //覆盖代币
            await redis.hSet(FirstAnalyzeEnum.token_json_hdata + "_v2", formatAddress(token.address), JSON.stringify(token));
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    token.total_supply = await fixTotal(token.total_supply,token.decimals);
    params.token_address = params.token_address.replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-01 14:14:40")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.MONTH:
                table_type = 'MV_M';
                q_0_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'MV_Y';
                q_0_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }

    let arg_0_v2 = `WITH
                        /*
                        【地址趋势图ago，<q_0_time之前最后时刻用户总持币人数】
                        */
                        balance_changes_PersonalAddressAgoLast AS (select count(owner) as count_owner
                                                                   from MV_LAST_balance_changes_v2 FINAL
                                                                   where is_contract = false
                                                                     and contract = '${params.token_address}'
                                                                     and f_time < ${q_0_time}
                                                                     and new_balance > ${params.q_0_balance}
                                                                     and new_balance <= ${params.q_1_balance})
                            ,
                        /*
                        【地址趋势图ago，>=q_0_time内最靠近q_0_time的old_balance持币人数】
                        */
                        balance_changes_PersonalAddressAgoLast_afterOld
                            as (select count(owner) as count_owner, f_time_old
                                from (select owner,
                                             f_time_old,
                                             balance
                                      from (select owner,
                                                   Min(f_time)                 as f_time_old,
                                                   argMin(old_balance, f_time) as balance
                                            from ${table_type}_old_balance_changes_v2 final
                                            where is_contract = false
                                              and contract = '${params.token_address}'
                                              and f_time >= ${q_0_time}
                                            group by contract, owner)
                                      where balance > ${params.q_0_balance}
                                        and balance <= ${params.q_1_balance})
                                group by f_time_old),
                        balance_changes_PersonalAddress_new as (select count(owner) as count_new,
                                                                       formatDateTime(f_time, '%Y-%m-%d') as time
                    from ${table_type}_new_balance_changes_v2 final
                    where is_contract = false
                      and contract = '${params.token_address}'
                      and f_time >= ${q_0_time}
                      and f_time <= ${q_1_time}
                      and new_balance
                        > ${params.q_0_balance}
                      and new_balance <= ${params.q_1_balance}
                    group by contract, f_time
                        ),
                        balance_changes_PersonalAddress_old as (
                    select
                        count (owner) as count_old, formatDateTime(f_time, '%Y-%m-%d') as time
                    from ${table_type}_old_balance_changes_v2 final
                    where is_contract = false
                      and contract = '${params.token_address}'
                      and f_time >= ${q_0_time}
                      and f_time <= ${q_1_time}
                      and old_balance
                        > ${params.q_0_balance}
                      and old_balance <= ${params.q_1_balance}
                    group by contract, f_time
                        )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'count_owner', toString(count_owner)
                        )) AS json
    FROM balance_changes_PersonalAddressAgoLast
    union all
    SELECT toJSONString(map(
            'type', 'chart_last_old',
            'count_owner', toString(count_owner),
            'f_time_old', formatDateTime(f_time_old, '%Y-%m-%d')
                        )) AS json
    FROM balance_changes_PersonalAddressAgoLast_afterOld
    union all
    SELECT toJSONString(map(
            'type', 'chart_new',
            'count_new', toString(count_new),
            'time', time
                        )) AS json
    FROM balance_changes_PersonalAddress_new
    union all
    SELECT toJSONString(map(
            'type', 'chart_old',
            'count_old', toString(count_old),
            'time', time
                        )) AS json
    FROM balance_changes_PersonalAddress_old`;

    let arg_1_v2 = `WITH
                        /*
                        【最后持币地址数据】
                        */
                        balance_changes_AllAddressNow AS ( -- 获取当前合约持有的所有地址(最后时刻数据)
                            select contract, owner, f_time, new_balance, is_contract
                            from MV_LAST_balance_changes_v2 FINAL
                            where contract = '${params.token_address}'
                              and new_balance > 0)
                            ,
                        /*
                        【持币总地址数】
                        */
                        balance_changes_AllAddressNowCount AS ( -- 获取当前合约持有的所有地址数量(最后时刻数据)
                            select count(DISTINCT owner) as all_count
                            from balance_changes_AllAddressNow),
                        /*
                        【区间持币地址数】
                        */
                        balance_changes_AllAddressNowRangeCount AS ( -- 获取当前合约持有的区间地址数量(最后时刻数据)
                            select count(DISTINCT owner) as range_count
                            from balance_changes_AllAddressNow
                            where new_balance > ${params.q_0_balance}
                              and new_balance <= ${params.q_1_balance})
                            ,
                        /*
                        【top10排行】
                        */
                        balance_changes_AllAddressNowRangeTop as ( -- TOP排行榜
                            select contract, owner, f_time, new_balance, is_contract
                            from balance_changes_AllAddressNow
                            where new_balance > ${params.q_0_balance}
                              and new_balance <= ${params.q_1_balance}
                            order by new_balance desc LIMIT 10
                        )
    SELECT toJSONString(map(
            'type', 'all_count',
            'count', toString(all_count)
                        )) AS json
    FROM balance_changes_AllAddressNowCount
    union all
    SELECT toJSONString(map(
            'type', 'range_count',
            'count', toString(range_count)
                        )) AS json
    FROM balance_changes_AllAddressNowRangeCount
    union all
    SELECT toJSONString(map(
            'type', 'top',
            'address', toString(owner),
            'time', formatDateTime(f_time, '%Y-%m-%d'),
            'balance', toString(new_balance),
            'is_contract',toString(is_contract)
                        )) AS json
    FROM balance_changes_AllAddressNowRangeTop`;

    let sql = {
        arg_0_v2: arg_0_v2,
        arg_1_v2: arg_1_v2
    }
    await getTokenHand_Chart_handle_v2(params, result, token, tokenDecimal, sql);

}

/**
 * 持币数量图表
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getTokenHandAmount_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.token_address || !params.q_0_balance || !params.q_1_balance) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    // if(hasSpecial(params.token_address)) {
    //     result.json({
    //         code: 400, msg: '超出运算限额！', data: null
    //     });
    //     return;
    // }
    /**
     * 查询当前token
     * @type {string}
     */
    let b_min;//区间筛选balance
    let b_max;//区间筛选balance
    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
        if (params.q_0_balance != "0") {
            params.q_0_balance = Number(params.q_0_balance) * Math.pow(10, tokenDecimal);
        }
        if (params.q_1_balance != "0") {
            params.q_1_balance = Number(params.q_1_balance) * Math.pow(10, tokenDecimal);
        }
        b_min = params.q_0_balance;
        b_max = params.q_1_balance;
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    params.token_address = params.token_address.replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.MINUTE_60:
            // table_type = 'balance_changes_PersonalAddress_DateH';
            // q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 86400000)));//往前退24小时
            // q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
            // break;
            case PeriodEnum.MINUTE_240:
                table_type = 'H';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 7 * 86400000))).getTime());//往前退7天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.DAY:
            // table_type = 'balance_changes_PersonalAddress_DateD';
            // q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 8640000000)));//往前退100天
            // q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
            // break;
            case PeriodEnum.DAY_3:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退365天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.MONTH:
                table_type = 'MV_M';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'MV_Y';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let arg_0_v2 = `WITH balance_changes_AllAddressNow AS ( -- 获取当前合约持有的所有地址(最后时刻数据)
                            select contract, owner, f_time, new_balance, is_contract
                            from MV_LAST_balance_changes_v2 FINAL
                            where contract = '${params.token_address}'
                              and new_balance > 0),
                        /*
                        【合约持币数量】
                        */
                         balance_changes_ContractAddressNowSum AS ( -- 获取持有当前合约币的所有合约地址数量(最后时刻数据)
                             select sum(t1.new_balance) as contract_sum
                             FROM balance_changes_AllAddressNow t1
                             where t1.is_contract = true),
                        /*
                        【用户持币数量】
                        */
                         balance_changes_PersonalAddressNowSum AS ( -- 获取当前合约持有的用户地址数量(最后时刻数据)
                             select sum(t1.new_balance) as personal_sum
                             FROM balance_changes_AllAddressNow t1
                             where t1.is_contract = false),
                        /*
                        【区间用户持币数量】
                        */
                         balance_changes_PersonalAddressNowSum_range AS ( -- 获取当前区间合约持有的用户地址数量(最后时刻数据)
                             select sum(t1.new_balance) as personal_sum_range
                             FROM balance_changes_AllAddressNow t1
                             where t1.is_contract = false
                               and t1.new_balance >= ${params.q_0_balance}
                               and t1.new_balance <= ${params.q_1_balance}),
                        /*
                        【TOP排行榜】
                        */
                         balance_changes_AllAddressNowRangeTop as ( -- TOP排行榜
                             select contract, owner, f_time, new_balance,is_contract
                             from balance_changes_AllAddressNow
                             where new_balance >= ${params.q_0_balance}
                               and new_balance <= ${params.q_1_balance}
                             order by new_balance desc LIMIT 10
                        )
    SELECT toJSONString(map(
            'type', 'contract_sum',
            'sum', toString(contract_sum)
                        )) AS json
    FROM balance_changes_ContractAddressNowSum
    union all
    SELECT toJSONString(map(
            'type', 'personal_sum',
            'sum', toString(personal_sum)
                        )) AS json
    FROM balance_changes_PersonalAddressNowSum
    union all
    SELECT toJSONString(map(
            'type', 'personal_sum_range',
            'sum', toString(personal_sum_range)
                        )) AS json
    FROM balance_changes_PersonalAddressNowSum_range
    union all
    SELECT toJSONString(map(
            'type', 'top',
            'address', toString(owner),
            'time', formatDateTime(f_time, '%Y-%m-%d %H:%i'),
            'balance', toString(new_balance),
            'is_contract',toString(is_contract)
                        )) AS json
    FROM balance_changes_AllAddressNowRangeTop`;


    let arg_1_v2 = `WITH
                        /*
                        【用户持币趋势图ago】
                        */
                        balance_changes_PersonalAddressAgoLast AS ( -- 筛选查询时间之前的最后的持有当前合约币的个人地址
                            select sum(new_balance) as count_owner
                            from MV_LAST_balance_changes_v2 FINAL
                            where is_contract = false
                              and contract = '${params.token_address}'
                              and f_time < ${q_0_time}
                              and new_balance > ${params.q_0_balance}
                              and new_balance <= ${params.q_1_balance}),
                        /*
                        【地址趋势图ago，>=q_0_time内最靠近q_0_time的old_balance持币人数】
                        */
                        balance_changes_PersonalAddressAgoLast_afterOld
                            as (select sum(balance) as count_owner, f_time_old
                                from (select owner,
                                             f_time_old,
                                             balance
                                      from (select owner,
                                                   Min(f_time)                 as f_time_old,
                                                   argMin(old_balance, f_time) as balance
                                            from ${table_type}_old_balance_changes_v2 final
                                            where is_contract = false
                                              and contract = '${params.token_address}'
                                              and f_time >= ${q_0_time}
                                            group by contract, owner)
                                      where balance > ${params.q_0_balance}
                                        and balance <= ${params.q_1_balance})
                                group by f_time_old),
                        balance_changes_PersonalAddress_new as (select sum(new_balance) as count_new,
                                                                       formatDateTime(f_time, '%Y-%m-%d') as time
                    from ${table_type}_new_balance_changes_v2 final
                    where is_contract = false
                      and contract = '${params.token_address}'
                      and f_time >= ${q_0_time}
                      and f_time <= ${q_1_time}
                      and new_balance
                        > ${params.q_0_balance}
                      and new_balance <= ${params.q_1_balance}
                    group by contract, f_time
                        ),
                        balance_changes_PersonalAddress_old as (
                    select
                        sum (old_balance) as count_old, formatDateTime(f_time, '%Y-%m-%d') as time
                    from ${table_type}_old_balance_changes_v2 final
                    where is_contract = false
                      and contract = '${params.token_address}'
                      and f_time >= ${q_0_time}
                      and f_time <= ${q_1_time}
                      and old_balance
                        > ${params.q_0_balance}
                      and old_balance <= ${params.q_1_balance}
                    group by contract, f_time
                        )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'count_owner', toString(count_owner)
                        )) AS json
    FROM balance_changes_PersonalAddressAgoLast
    union all
    SELECT toJSONString(map(
            'type', 'chart_last_old',
            'count_owner', toString(count_owner),
            'f_time_old', formatDateTime(f_time_old, '%Y-%m-%d')
                        )) AS json
    FROM balance_changes_PersonalAddressAgoLast_afterOld
    union all
    SELECT toJSONString(map(
            'type', 'chart_new',
            'count_new', toString(count_new),
            'time', time
                        )) AS json
    FROM balance_changes_PersonalAddress_new
    union all
    SELECT toJSONString(map(
            'type', 'chart_old',
            'count_old', toString(count_old),
            'time', time
                        )) AS json
    FROM balance_changes_PersonalAddress_old`;
    let sql = {
        arg_0_v2: arg_0_v2,
        arg_1_v2: arg_1_v2
    }
    let data_0 = await HQL_sonar(sql.arg_0_v2);
    let data_1 = await HQL_sonar(sql.arg_1_v2);

    let data = [...data_0, ...data_1];
    let callData = {chart: {time: [], value: [], filter: [], filter_old: [], filter_new: []}, top: []};
    let fixMap_v2 = {chart_last: null, chart_last_old: []};
    let isNull = true;//默认查询为空的标记
    // if(data && data.length > 10000000) {
    //     result.json({
    //         code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
    //     });
    //     return;
    // }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.count_owner) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.count_owner = new BigNumber(jsonColumn.count_owner);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    // fixMap[jsonColumn.address] = jsonColumn;
                    fixMap_v2['chart_last'] = jsonColumn;
                    break;
                case "chart_last_old":
                    if (jsonColumn.count_owner) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.count_owner = new BigNumber(jsonColumn.count_owner);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    // fixMap[jsonColumn.address] = jsonColumn;
                    fixMap_v2['chart_last_old'].push(jsonColumn);
                    break;
                case "chart_new":
                    callData.chart.filter_new.push(jsonColumn);
                    break;
                case "chart_old":
                    callData.chart.filter_old.push(jsonColumn);
                    break;
                case "contract_sum":
                    jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                    callData.contract_sum = jsonColumn.sum;
                    break;
                case "personal_sum":
                    jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                    callData.personal_sum = jsonColumn.sum;
                    break;
                case "personal_sum_range":
                    jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                    callData.personal_sum_range = jsonColumn.sum;
                    break;
                case "top":
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = jsonColumn.balance;
                    }
                    jsonColumn.is_contract = new Boolean(jsonColumn.is_contract);
                    callData.top.push(jsonColumn);
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    //修复
    callData.chart.filter = await fixNull_v2(fixMap_v2, callData.chart, null, 'address_balance');
    let jsonColumn;
    let jsonColumn_240;//4小时特殊处理对比变量
    let jsonColumn_3day;//3天特殊处理对比变量
    let jsonColumn_w;//周特殊处理对比变量
    for (let i = 0; i < callData.chart.filter.length; i++) {
        jsonColumn = callData.chart.filter[i];
        switch (params.type) {
            case PeriodEnum.MINUTE_60:
                jsonColumn.time = jsonColumn.time.substring(11, 16);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case PeriodEnum.MINUTE_240:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_240 = jsonColumn;
                } else {//从第二个开始需要判断是否相差4小时
                    if (Math.abs(new Date(jsonColumn_240.time).getTime() - new Date(jsonColumn.time).getTime()) >= 14400000) {
                        //如果满足相差4小时则标记true
                        jsonColumn.flag = true;
                        jsonColumn_240 = jsonColumn;
                    } else {
                        //如果不满足相差4小时则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(11, 16));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case PeriodEnum.DAY:
                jsonColumn.time = jsonColumn.time.substring(5, 10);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case PeriodEnum.DAY_3:
                // 2012-02-02 11:11:11
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_3day = jsonColumn;
                } else {//从第二个开始需要判断是否相差3天
                    if (Math.abs(new Date(jsonColumn_3day.time).getTime() - new Date(jsonColumn.time).getTime()) >= 259200000) {
                        //如果满足相差3天则标记true
                        jsonColumn.flag = true;
                        jsonColumn_3day = jsonColumn;
                    } else {
                        //如果不满足相差3天则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case PeriodEnum.WEEK:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_w = jsonColumn;
                } else {//从第二个开始需要判断是否相差一周
                    if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                        //如果满足相差一周则标记true
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {
                        //如果不满足相差一周则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case PeriodEnum.MONTH:
                jsonColumn.time = jsonColumn.time.substring(0, 7);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
        }
    }
    delete callData.chart.filter;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 合约持币图表
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getContractsHand_Base(request, result) {
    let params = request.body
    if (!params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    /**
     * 查询当前token
     * @type {string}
     */
    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    params.token_address = params.token_address.replace("0x", "");
    let arg = `WITH balance_changes_AllAddressNow AS ( -- 获取当前合约持有的所有地址(最后时刻数据)
        select contract, owner, f_time, new_balance, is_contract
        from MV_LAST_balance_changes_v2 FINAL
        where contract = '${params.token_address}'
          and new_balance > 0
          and is_contract = true),
                    balance_changes_ContractAddressNowSum AS ( -- 获取持有当前合约币的所有合约地址币量(最后时刻数据)
                        select sum(t1.new_balance) as contract_sum FROM balance_changes_AllAddressNow t1),
                    balance_changes_ContractAddressNowCount AS ( -- 获取持有当前合约币的所有合约地址数(最后时刻数据)
                        select count(DISTINCT t1.owner) as contract_count FROM balance_changes_AllAddressNow t1),
                    balance_changes_AllHistoryAddress AS ( -- 获取历史以来合约持有的所有地址(曾经持有但后来没有持有也算进去)
                        select count(DISTINCT owner) as contract_count_history
                        from D_new_balance_changes_v2 FINAL
                        where is_contract = true
                          and contract = '${params.token_address}'
                          and new_balance > 0),
                    balance_changes_ContractAddressNowTop as ( -- TOP排行榜
                        select t1.contract, t1.owner, t1.f_time, t1.new_balance,t1.is_contract
                        FROM balance_changes_AllAddressNow t1
                        order by t1.new_balance desc LIMIT 10
                   )
    SELECT toJSONString(map(-- 合约持币总量（当前区块数据）
            'type', 'all_contract_sum',
            'balance', toString(contract_sum)
                        )) AS json
    FROM balance_changes_ContractAddressNowSum
    union all
    SELECT toJSONString(map(-- 合约持币地址数（当前区块数据）
            'type', 'all_contract_count',
            'balance', toString(contract_count)
                        )) AS json
    FROM balance_changes_ContractAddressNowCount
    union all
    SELECT toJSONString(map(-- 合约总地址数：包含当前持有与曾经持有
            'type', 'history_contract_count',
            'balance', toString(contract_count_history)
                        )) AS json
    FROM balance_changes_AllHistoryAddress
    union all
    SELECT toJSONString(map( -- top100合约持币排行
            'type', 'top',
            'address', toString(owner),
            'balance', toString(new_balance),
            'is_contract',toString(is_contract)
                        )) AS json
    FROM balance_changes_ContractAddressNowTop`;
    let data = await HQL_sonar(arg);
    let callData = {top: []};
    let isNull = true;//默认查询为空的标记
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "all_contract_sum":
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                    }
                    callData.all_contract_sum = jsonColumn.balance;
                    break;
                case "all_contract_count":
                    callData.all_contract_count = jsonColumn.balance;
                    break;
                case "history_contract_count":
                    callData.history_contract_count = jsonColumn.balance;
                    break;
                case "top":
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                    }
                    jsonColumn.is_contract = new Boolean(jsonColumn.is_contract);
                    callData.top.push(jsonColumn);
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 合约持币_走势图
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getContractsHand_Chart(request, result) {
    let params = request.body
    if (!params.type || !params.token_address || !params.address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    // if(hasSpecial(params.token_address)) {
    //     result.json({
    //         code: 400, msg: '超出运算限额！', data: null
    //     });
    //     return;
    // }
    /**
     * 查询当前token
     * @type {string}
     */
    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    /**
     * 处理0x前缀
     * @type {string}
     */
    params.token_address = params.token_address.replace("0x", "");
    params.address = params.address.toLowerCase().replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-01 14:14:40")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退365天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.MONTH:
                table_type = 'MV_M';
                q_0_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'MV_Y';
                q_0_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let arg = `WITH balance_changes_ContractAddressAgoLast AS ( -- 筛选查询时间之前的最后的持有当前合约币的合约地址
        select sum(new_balance) as count_owner
        from MV_LAST_balance_changes_v2 FINAL
        where is_contract = true
          and contract = '${params.token_address}'
          and owner = '${params.address}'
          and f_time < ${q_0_time}
          and new_balance > 0),
                   /*
                 【地址趋势图ago，>=q_0_time内最靠近q_0_time的old_balance持币人数】
                 */
                    balance_changes_ContractAddressAgoLast_afterOld as (select sum(balance) as count_owner, f_time_old
                                                                        from (select owner,
                                                                                     f_time_old,
                                                                                     balance
                                                                              from (select owner,
                                                                                           Min(f_time)                 as f_time_old,
                                                                                           argMin(old_balance, f_time) as balance
                                                                                    from ${table_type}_old_balance_changes_v2 final
                                                                                    where is_contract = true
                                                                                      and contract = '${params.token_address}'
                                                                                      and f_time >= ${q_0_time}
                                                                                      and owner = '${params.address}'
                                                                                    group by contract, owner)
                                                                              where balance > 0)
                                                                        group by f_time_old),
                    balance_changes_ContractAddress_new as (select sum(new_balance) as count_new,
                                                                   formatDateTime(f_time, '%Y-%m-%d') as time
               from ${table_type}_new_balance_changes_v2 final
               where is_contract = true
                 and contract = '${params.token_address}'
                 and f_time >= ${q_0_time}
                 and f_time <= ${q_1_time}
                 and owner = '${params.address}'
                 and new_balance
                   > 0
               group by contract, f_time
                   ),
                   balance_changes_ContractAddress_old as (
               select
                   sum (old_balance) as count_old, formatDateTime(f_time, '%Y-%m-%d') as time
               from ${table_type}_old_balance_changes_v2 final
               where is_contract = true
                 and contract = '${params.token_address}'
                 and f_time >= ${q_0_time}
                 and f_time <= ${q_1_time}
                 and owner = '${params.address}'
                 and old_balance
                   > 0
               group by contract, f_time
                   )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'count_owner', toString(count_owner)
                        )) AS json
    FROM balance_changes_ContractAddressAgoLast
    union all
    SELECT toJSONString(map(
            'type', 'chart_last_old',
            'count_owner', toString(count_owner),
            'f_time_old', formatDateTime(f_time_old, '%Y-%m-%d')
                        )) AS json
    FROM balance_changes_ContractAddressAgoLast_afterOld
    union all
    SELECT toJSONString(map(
            'type', 'chart_new',
            'count_new', toString(count_new),
            'time', time
                        )) AS json
    FROM balance_changes_ContractAddress_new
    union all
    SELECT toJSONString(map(
            'type', 'chart_old',
            'count_old', toString(count_old),
            'time', time
                        )) AS json
    FROM balance_changes_ContractAddress_old`;
    let data = await HQL_sonar(arg);
    let callData = {chart: {time: [], value: [], filter: [], filter_old: [], filter_new: []}, top: []};
    let fixMap = {};
    let fixMap_v2 = {chart_last: null, chart_last_old: []};
    let isNull = true;//默认查询为空的标记
    // if(data && data.length > 10000000) {
    //     result.json({
    //         code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
    //     });
    //     return;
    // }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.count_owner) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.count_owner = new BigNumber(jsonColumn.count_owner);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    // fixMap[jsonColumn.address] = jsonColumn;
                    fixMap_v2['chart_last'] = jsonColumn;
                    break;
                case "chart_last_old":
                    if (jsonColumn.count_owner) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.count_owner = new BigNumber(jsonColumn.count_owner);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    // fixMap[jsonColumn.address] = jsonColumn;
                    fixMap_v2['chart_last_old'].push(jsonColumn);
                    break;
                case "chart_new":
                    callData.chart.filter_new.push(jsonColumn);
                    break;
                case "chart_old":
                    callData.chart.filter_old.push(jsonColumn);
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    //修复
    callData.chart.filter = await fixNull_v2(fixMap_v2, callData.chart, null, 'address_balance');
    let jsonColumn;
    let jsonColumn_w;//一周特殊处理变量
    for (let i = 0; i < callData.chart.filter.length; i++) {
        jsonColumn = callData.chart.filter[i];
        switch (params.type) {
            case "1D":
                jsonColumn.time = jsonColumn.time.substring(5, 10);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case "1W":
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_w = jsonColumn;
                } else {//从第二个开始需要判断是否相差一周
                    if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                        //如果满足相差一周则标记true
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {
                        //如果不满足相差一周则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case "1MO":
                jsonColumn.time = jsonColumn.time.substring(0, 7);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case "1Y":
                jsonColumn.time = jsonColumn.time.substring(0, 4);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
        }
    }
    delete callData.chart.filter;
    delete callData.chart.filter_old;
    delete callData.chart.filter_new;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * 单独接口：合约持币
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getContractsHolder(request, result) {
    let params = request.body
    if (!params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    /**
     * cache 缓存
     * @type {string}
     */
    let key = params.token_address;
    let cache = await redis.get("webmagic_api:getContractsHolder:" + key);
    if (cache) {
        result.json({
            code: 200, msg: '请求成功！', data: JSON.parse(cache)
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase().replace("0x", "");
    let arg = `WITH balance_changes_AllAddressNow AS ( -- 获取当前合约持有的所有地址(最后时刻数据)
        select sum(new_balance) as balance
        from MV_LAST_balance_changes_v2 FINAL
        where contract = '${params.token_address}'
          and new_balance > 0
          and is_contract = true)
               SELECT toJSONString(map(
                       'type', 'contract',
                       'balance', toString(balance)
                                   )) AS json
               FROM balance_changes_AllAddressNow`;
    let data = await HQL_sonar(arg);
    let isNull = true;//默认查询为空的标记
    let resultMap = {contract: {}};
    if (data && data.length > 0) {
        isNull = false;
        let jsonColumn;
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            jsonColumn = JSON.parse(json);
            if (jsonColumn.balance) {
                jsonColumn.balance = new BigNumber(jsonColumn.balance).toFixed(3);
            }
            resultMap.contract = jsonColumn;
        }
        await redis.set("webmagic_api:getContractsHolder:" + key, JSON.stringify(resultMap), 7 * 86400);
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    result.json({
        code: 200, msg: '查询成功！', data: resultMap
    });
}

/**
 *  LP地址（合约内）
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getLpAddressContract(request, result) {
    let params = request.body
    if (!params.type || !params.token_address || !params.min_num || !params.max_num) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */

    let pairAddress;
    let token = await getTokenCache(params.token_address);
    if (token) {
        pairAddress = token.pair_address;
        if (!pairAddress) {
            let pair = await getTokenToPair(token);
            pairAddress = pair.pairAddress;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    if (!pairAddress) {
        result.json({
            code: 500, msg: '当前token没有pair数据！', data: null
        });
        return;
    }
    pairAddress = pairAddress.toLowerCase();
    //查询pair
    let pairData = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", pairAddress);
    if (pairData) {
        pairData = JSON.parse(pairData);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    let pairsDecimal = pairData.decimals;
    if (params.min_num != "0") {
        params.min_num = Number(params.min_num) * Math.pow(10, pairData.decimals);
    }
    if (params.max_num != "0") {
        params.max_num = Number(params.max_num) * Math.pow(10, pairData.decimals);
    }
    let b_min = params.min_num;
    let b_max = params.max_num;
    pairAddress = pairAddress.replace("0x", "");
    params.token_address = params.token_address.replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    let table_date;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        table_date = 'YYYY-MM-DD';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                table_type = 'D';
                table_date = 'YYYY-MM-DD';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                table_date = 'YYYY-MM-DD';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.MONTH:
                table_type = 'M';
                table_date = 'YYYY-MM';
                q_0_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'Y';
                table_date = 'YYYY';
                q_0_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let arg = `WITH
                   /*
                   【合约内lp地址数】
                   */
                   PersonalToContract_transfers as ( -- 用户转向合约的transfer
                       SELECT
                       from,
                   to,
                   sum_value as value
               FROM MV_SUM_LAST_transfers_v2 final
               where address = '${pairAddress}'
                 and to not in ('000000000000000000000000000000000000dead'
                   , '0000000000000000000000000000000000000000')
                 and from_is_contract = false
                 and to_is_contract = true
                 and sum_value
                   >0
                   )
                   , PersonalToContract_personal_address as ( -- 用户转向合约的用户地址集合
               SELECT DISTINCT
               from as p_address
               FROM PersonalToContract_transfers
                   ), PersonalToContract_contract_address as ( -- 用户转向合约的合约地址集
               SELECT DISTINCT to as c_address
               FROM PersonalToContract_transfers
                   ), ContractToPersonal_transfers as ( -- 合约转向用户的transfer
               SELECT
               from, to, sum_value as value
               FROM MV_SUM_LAST_transfers_v2 final
               where address = '${pairAddress}'
                 and
               from in (select c_address from PersonalToContract_contract_address)
                   and to in (select p_address from PersonalToContract_personal_address)
                   and sum_value>0
                   ), PersonalToContract_merge as ( -- 合约内外合并
               SELECT
                   p.from as p_from, p.to as p_to, p.value as p_v, c.from as c_from, c.to as c_to, c.value as c_v
               FROM PersonalToContract_transfers p left JOIN ContractToPersonal_transfers c
               ON p.from = c.to and p.to = c.from
                   ),
                   PersonalToContract_merge_notNull AS ( -- 合约内LP数量运算（转入合约的用户地址数据）-（合约转出到用户地址的数据）=最终剩下的合约LP持有数据
               SELECT p_from, p_to, (p_v-c_v) as value
               from PersonalToContract_merge             -- 因为是left join会存在ov为空的情况所以加条件ov is not null，而用inner join会丢失为空的部分空的那部分也是需要的
                   ), PersonalToContract_merge_Null AS ( -- 合约内LP数量运算（只转入合约没有合约转回的数据）
               SELECT p_from, p_to, p_v as value
               from PersonalToContract_merge
               where c_from = ''
                   )
                   , eoaAddress_merge AS (-- 只取大于0的数据（合约内持有lp数据）
                   (SELECT p_from
                   , p_to
                   , toInt256(value) as value from PersonalToContract_merge_notNull where value
                   >0)
               UNION ALL
               (
               SELECT p_from, p_to, toInt256(value) as value
               from PersonalToContract_merge_Null
               where value
                   >0))
                   , eoaAddress_merge_count AS (-- 合约内持有lp的用户地址数量【合约内lp地址数】
               select count (DISTINCT p_from) as eoa_count
               from eoaAddress_merge
                   ),
                   /*
                      【lp地址总数】
                    */
                   balance_changes_PersonalAddressNow AS ( -- 获取当前lp持有的用户地址数据(最后时刻数据)
               select contract, owner, f_time, new_balance
               from MV_LAST_balance_changes_v2 FINAL
               where contract = '${pairAddress}'
                 and is_contract = false
                 and new_balance
                   > 0
                   )
                   , balance_changes_PersonalEoaAddressNowCount AS ( -- 合并合约内外去重【lp地址总数】
               select count (address) as all_count
               from
                   ((select DISTINCT p_from as address from eoaAddress_merge) union DISTINCT (select DISTINCT owner as address FROM balance_changes_PersonalAddressNow))
                   ),
                   /*
                   lp趋势图ago
                   */
                   PersonalToContract_transfers_rangeAgo AS ( -- 获取当前持有合约lp的所有地址(之前最后时刻数据)
               SELECT
               from, to, sum (sum_value) as value
               FROM MV_SUM_${table_type}_transfers final
               where address = '${pairAddress}'
                 and f_time<=${q_0_time}
                 and to not in ('000000000000000000000000000000000000dead'
                   , '0000000000000000000000000000000000000000')
                 and from_is_contract = false
                 and to_is_contract = true
                 and sum_value
                   >0
               group by
               from, to
                   ), PersonalToContract_personal_address_rangeAgo as ( -- 用户转向合约的用户地址集合(之前最后时刻数据)
               SELECT DISTINCT
               from as p_address
               FROM PersonalToContract_transfers_rangeAgo
                   ), PersonalToContract_contract_address_rangeAgo as ( -- 用户转向合约的合约地址集(之前最后时刻数据)
               SELECT DISTINCT to as c_address
               FROM PersonalToContract_transfers_rangeAgo
                   ), ContractToPersonal_transfers_rangeAgo as ( -- 合约转向用户的transfer(之前最后时刻数据)
               SELECT
               from, to, sum (sum_value) as value
               FROM MV_SUM_${table_type}_transfers final
               where address = '${pairAddress}'
                 and f_time<=${q_0_time}
                 and
               from in (select c_address from PersonalToContract_contract_address_rangeAgo)
                   and to in (select p_address from PersonalToContract_personal_address_rangeAgo)
                   and sum_value>0
               group by
               from, to
                   ), PersonalToContract_merge_rangeAgo as ( -- 合约内外合并
               SELECT
                   p.from as p_from, p.to as p_to, p.value as p_v, c.from as c_from, c.to as c_to, c.value as c_v
               FROM PersonalToContract_transfers_rangeAgo p left JOIN ContractToPersonal_transfers_rangeAgo c
               ON p.from = c.to and p.to = c.from
                   ),
                   PersonalToContract_merge_notNull_rangeAgo AS ( -- 合约内LP数量运算（转入合约的用户地址数据）-（合约转出到用户地址的数据）=最终剩下的合约LP持有数据
               SELECT p_from, p_to, (p_v-c_v) as value
               from PersonalToContract_merge_rangeAgo             -- 因为是left join会存在ov为空的情况所以加条件ov is not null，而用inner join会丢失为空的部分空的那部分也是需要的
                   ), PersonalToContract_merge_Null_rangeAgo AS ( -- 合约内LP数量运算（只转入合约没有合约转回的数据）
               SELECT p_from, p_to, p_v as value
               from PersonalToContract_merge_rangeAgo
               where c_from = ''
                   )
                   , eoaAddress_merge_rangeAgo AS (-- 只取大于0的数据（合约内持有lp数据）
                   (SELECT p_from
                   , p_to
                   , toInt256(value) as value from PersonalToContract_merge_notNull_rangeAgo where value
                   >0)
               UNION ALL
               (
               SELECT p_from, p_to, toInt256(value) as value
               from PersonalToContract_merge_Null_rangeAgo
               where value
                   >0)
                   )
                   ,
                   /*
                  lp趋势图
                  */
                   PersonalToContract_transfers_rangeDate AS (
               SELECT
               from, to, sum_value as value, f_time as time
               FROM MV_SUM_${table_type}_transfers final
               where address = '${pairAddress}'
                 and f_time
                   >${q_0_time}
                 and f_time<=${q_1_time}
                 and to not in ('000000000000000000000000000000000000dead'
                   , '0000000000000000000000000000000000000000')
                 and from_is_contract = false
                 and to_is_contract = true
                 and sum_value
                   >0
                   )
                   , PersonalToContract_personal_address_rangeDate as ( -- 用户转向合约的用户地址集合
               SELECT DISTINCT
               from as p_address
               FROM PersonalToContract_transfers_rangeDate
                   ), PersonalToContract_contract_address_rangeDate as ( -- 用户转向合约的合约地址集
               SELECT DISTINCT to as c_address
               FROM PersonalToContract_transfers_rangeDate
                   ), ContractToPersonal_transfers_rangeDate as ( -- 合约转向用户的transfer
               SELECT
               from, to, f_time as time, sum_value as value
               FROM MV_SUM_${table_type}_transfers final
               where address = '${pairAddress}'
                 and f_time
                   > ${q_0_time}
                 and f_time <= ${q_1_time}
                 and
               from in (select c_address from PersonalToContract_contract_address_rangeDate)
                   and to in (select p_address from PersonalToContract_personal_address_rangeDate)
                   and sum_value>0
                   )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'p_from', toString(p_from),
            'p_to', toString(p_to),
            'value', toString(value)
                        )) AS json
    FROM eoaAddress_merge_rangeAgo
    union all
    SELECT toJSONString(map(
            'type', 'chart_PersonalToContract',
            'r_from', toString(from),
            'r_to', toString(to),
            'time', toString(time),
            'value', toString(value)
                        )) AS json
    FROM PersonalToContract_transfers_rangeDate
    union all
    SELECT toJSONString(map(
            'type', 'chart_ContractToPersonal',
            'r_from', toString(from),
            'r_to', toString(to),
            'time', toString(time),
            'value', toString(value)
                        )) AS json
    FROM ContractToPersonal_transfers_rangeDate
    union all
    SELECT toJSONString(map(
            'type', 'eoa_count',
            'count', toString(eoa_count)
                        )) AS json
    FROM eoaAddress_merge_count
    union all
    SELECT toJSONString(map(
            'type', 'all_count',
            'count', toString(all_count)
                        )) AS json
    FROM balance_changes_PersonalEoaAddressNowCount`;
    let data = await HQL_sonar(arg);
    let callData = {
        chart: {time: [], value: [], filter: []},
        chart_p: {time: [], value: [], filter: []},
        chart_c: {time: [], value: [], filter: []},
        top: []
    };
    let fixMap = {};
    let isNull = true;//默认查询为空的标记
    if (data && data.length > 10000000) {
        result.json({
            code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
        });
        return;
    }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.value) {
                        // jsonColumn.value = formatStrDecimal(jsonColumn.value, pairsDecimal);
                        jsonColumn.value = new BigNumber(jsonColumn.value);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    fixMap[jsonColumn.p_from + '_' + jsonColumn.p_to] = jsonColumn;
                    break;
                case "chart_PersonalToContract":
                    if (jsonColumn.value) {
                        // jsonColumn.value = formatStrDecimal(jsonColumn.value, pairsDecimal);
                        jsonColumn.value = new BigNumber(jsonColumn.value);
                    }
                    callData.chart_p.filter.push(jsonColumn);
                    break;
                case "chart_ContractToPersonal":
                    if (jsonColumn.value) {
                        // jsonColumn.value = formatStrDecimal(jsonColumn.value, pairsDecimal);
                        jsonColumn.value = new BigNumber(jsonColumn.value);
                    }
                    callData.chart_c.filter.push(jsonColumn);
                    break;
                case "eoa_count":
                    callData.eoa_count = jsonColumn.count;
                    break;
                case "all_count":
                    callData.all_count = jsonColumn.count;
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    //修复
    let fixResult = await fixNull_transfer(fixMap, callData.chart_p.filter, callData.chart_c.filter, {
        b_min: b_min, b_max: b_max
    });
    callData.rang_count = fixResult.rang_count;
    callData.chart.filter = fixResult.result;
    callData.rank_list = fixResult.rank_list;//top排行榜图
    /**
     * 转换排行精度
     */
    callData.rank_list.map(item => {
        item.balance = formatStrDecimal(item.balance, pairsDecimal);
    });
    /**
     * 转换趋势图精度
     */
    let jsonColumn;
    let jsonColumn_w;//周特殊处理对比变量
    for (let i = 0; i < callData.chart.filter.length; i++) {
        jsonColumn = callData.chart.filter[i];
        switch (params.type) {
            case PeriodEnum.DAY:
                jsonColumn.time = jsonColumn.time.substring(5, 10);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                break;
            case PeriodEnum.WEEK:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_w = jsonColumn;
                } else {//从第二个开始需要判断是否相差一周
                    if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                        //如果满足相差一周则标记true
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {
                        //如果不满足相差一周则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                }
                break;
            case PeriodEnum.MONTH:
                jsonColumn.time = jsonColumn.time.substring(0, 7);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                break;
            case PeriodEnum.YEAR:
                jsonColumn.time = jsonColumn.time.substring(0, 4);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                break;
        }
    }
    delete callData.chart.filter;
    delete callData.chart_c.filter;
    delete callData.chart_p.filter;
    delete callData.top;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * Lp地址(合约外：EOA)图表
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getLpAddressContractOut(request, result) {
    let params = request.body
    if (!params.type || !params.token_address || !params.min_num || !params.max_num) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */

    let pairAddress;
    let token = await getTokenCache(params.token_address);
    if (token) {
        pairAddress = token.pair_address;
        if (!pairAddress) {
            let pair = await getTokenToPair(token);
            pairAddress = pair.pairAddress;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    if (!pairAddress) {
        result.json({
            code: 500, msg: '当前token没有pair数据！', data: null
        });
        return;
    }
    pairAddress = pairAddress.toLowerCase();
    //查询pair
    let pairData = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", pairAddress);
    if (pairData) {
        pairData = JSON.parse(pairData);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    let pairsDecimal = pairData.decimals;
    let b_min;
    let b_max;
    if (params.min_num != "0") {
        params.min_num = Number(params.min_num) * Math.pow(10, pairData.decimals);
    }
    if (params.max_num != "0") {
        params.max_num = Number(params.max_num) * Math.pow(10, pairData.decimals);
    }
    b_min = params.min_num;
    b_max = params.max_num;
    pairAddress = pairAddress.replace("0x", "");
    params.token_address = params.token_address.replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.MONTH:
                table_type = 'M';
                q_0_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'Y';
                q_0_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let arg = `WITH
                   /*
                   【合约lp地址数】
                   */
                   PersonalToContract_transfers as ( -- 用户转向合约的transfer
                       SELECT
                       from,
                   to,
                   sum_value as value
               FROM MV_SUM_LAST_transfers_v2 final
               where address = '${pairAddress}'
                 and to not in ('000000000000000000000000000000000000dead'
                   , '0000000000000000000000000000000000000000')
                 and from_is_contract = false
                 and to_is_contract = true
                 and sum_value
                   >0
                   )
                   , PersonalToContract_personal_address as ( -- 用户转向合约的用户地址集合
               SELECT DISTINCT
               from as p_address
               FROM PersonalToContract_transfers
                   ), PersonalToContract_contract_address as ( -- 用户转向合约的合约地址集
               SELECT DISTINCT to as c_address
               FROM PersonalToContract_transfers
                   ), ContractToPersonal_transfers as ( -- 合约转向用户的transfer
               SELECT
               from, to, sum_value as value
               FROM MV_SUM_LAST_transfers_v2 final
               where address = '${pairAddress}'
                 and
               from in (select c_address from PersonalToContract_contract_address)
                   and to in (select p_address from PersonalToContract_personal_address)
                   and sum_value>0
                   ), PersonalToContract_merge as ( -- 合约内外合并
               SELECT
                   p.from as p_from, p.to as p_to, p.value as p_v, c.from as c_from, c.to as c_to, c.value as c_v
               FROM PersonalToContract_transfers p left JOIN ContractToPersonal_transfers c
               ON p.from = c.to and p.to = c.from
                   ),
                   PersonalToContract_merge_notNull AS ( -- 合约内LP数量运算（转入合约的用户地址数据）-（合约转出到用户地址的数据）=最终剩下的合约LP持有数据
               SELECT p_from, p_to, (p_v-c_v) as value
               from PersonalToContract_merge             -- 因为是left join会存在ov为空的情况所以加条件ov is not null，而用inner join会丢失为空的部分空的那部分也是需要的
                   ), PersonalToContract_merge_Null AS ( -- 合约内LP数量运算（只转入合约没有合约转回的数据）
               SELECT p_from, p_to, p_v as value
               from PersonalToContract_merge
               where c_from = ''
                   )
                   , eoaAddress_merge AS (-- 只取大于0的数据（合约内持有lp数据）
                   (SELECT p_from
                   , p_to
                   , toInt256(value) as value from PersonalToContract_merge_notNull where value
                   >0)
               UNION ALL
               (
               SELECT p_from, p_to, toInt256(value) as value
               from PersonalToContract_merge_Null
               where value
                   >0)
                   )
                   , eoaAddress_merge_count AS (-- 合约内持有lp的用户地址数量【合约内lp地址数】
               select count (DISTINCT p_from) as eoa_count
               from eoaAddress_merge
                   ),
                   /*
                   【用户lp地址数】
                   */
                   balance_changes_PersonalAddressNow AS ( -- 获取当前lp持有的用户地址数据(最后时刻数据)
               select contract, owner, f_time, new_balance, is_contract
               from MV_LAST_balance_changes_v2 FINAL
               where contract = '${pairAddress}'
                 and new_balance
                   > 0
                 and is_contract = false
                   )
                   , balance_changes_PersonalAddressNowCount AS ( -- 获取当前lp持有的用户地址数量(最后时刻数据)【用户lp地址数】
               select count (DISTINCT owner) as personal_count
               FROM balance_changes_PersonalAddressNow
                   ),
                   /*
                   【lp地址总数】
                   */
                   balance_changes_PersonalEoaAddressNowCount AS ( -- 合并合约内外去重【lp地址总数】
               select count (address) as all_count
               from ((select DISTINCT p_from as address from eoaAddress_merge) union DISTINCT (select DISTINCT owner as address FROM balance_changes_PersonalAddressNow))
                   ),
                   /*
                    lp趋势图ago
                   */
                   balance_changes_PersonalAddressAgo AS ( -- 筛选查询时间之前的最后的持有当前lp的个人地址
               select contract, owner, argMax(new_balance, f_time) as last_balance
               from MV_${table_type}_new_balance_changes_v2 FINAL
               where contract = '${pairAddress}'
                 and f_time <= ${q_0_time}
                 and is_contract = false
               GROUP BY contract, owner
               HAVING last_balance
                    > ${params.min_num}
                  and last_balance <= ${params.max_num}
                   )
                    ,
                   /*
                   lp趋势图
                  */
                   balance_changes_PersonalAddress AS ( -- 筛选持有当前lp币的个人地址
               SELECT contract, owner, new_balance, f_time
               FROM MV_${table_type}_new_balance_changes_v2 FINAL
               where contract = '${pairAddress}'
                 and f_time
                   > ${q_0_time}
                 and f_time <= ${q_1_time}
                 and is_contract = false
                   )
                   , balance_changes_PersonalAddress_DateH AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(小时)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d %H:00') as time
               from balance_changes_PersonalAddress
                   ), balance_changes_PersonalAddress_DateD AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(天)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d') as time
               from balance_changes_PersonalAddress
                   ), balance_changes_PersonalAddress_DateM AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(月)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m') as time
               from balance_changes_PersonalAddress
                   ), balance_changes_PersonalAddress_DateY AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(年)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y') as time
               from balance_changes_PersonalAddress
                   )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(last_balance)
                        )) AS json
    FROM balance_changes_PersonalAddressAgo
    union all
    SELECT toJSONString(map(
            'type', 'chart',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(new_balance),
            'time', toString(time)
                        )) AS json
    FROM balance_changes_PersonalAddress_Date${table_type}
    union all
    SELECT toJSONString(map(
            'type', 'eoa_count',
            'count', toString(eoa_count)
                        )) AS json
    FROM eoaAddress_merge_count
    union all
    SELECT toJSONString(map(
            'type', 'personal_count',
            'count', toString(personal_count)
                        )) AS json
    FROM balance_changes_PersonalAddressNowCount
    union all
    SELECT toJSONString(map(
            'type', 'all_count',
            'count', toString(all_count)
                        )) AS json
    FROM balance_changes_PersonalEoaAddressNowCount`;
    let data = await HQL_sonar(arg);
    let callData = {chart: {time: [], value: [], filter: []}, top: []};
    let fixMap = {};
    let fixMap_address = {};
    let isNull = true;//默认查询为空的标记
    if (data && data.length > 10000000) {
        result.json({
            code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
        });
        return;
    }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, pairsDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    fixMap[jsonColumn.address] = jsonColumn;
                    break;
                case "chart":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, pairsDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    callData.chart.filter.push(jsonColumn);
                    break;
                case "eoa_count":
                    callData.eoa_count = jsonColumn.count;
                    break;
                case "personal_count":
                    callData.personal_count = jsonColumn.count;
                    break;
                case "all_count":
                    callData.all_count = jsonColumn.count;
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    //修复
    let fixResult = await fixNull(fixMap, callData.chart.filter, {b_min: b_min, b_max: b_max}, 'rank_list');
    callData.chart.filter = fixResult.result;
    callData.rank_list = fixResult.rank_list;//top排行榜图
    callData.personal_count_range = fixResult.rangAddress.size;//区间持有数量
    /**
     * 转换排行精度
     */
    callData.rank_list.map(item => {
        item.balance = formatStrDecimal(item.balance, pairsDecimal);
    });
    /**
     * 转换趋势图精度
     */
    let jsonColumn;
    let jsonColumn_w;//周特殊处理对比变量
    for (let i = 0; i < callData.chart.filter.length; i++) {
        jsonColumn = callData.chart.filter[i];
        switch (params.type) {
            case PeriodEnum.DAY:
                jsonColumn.time = jsonColumn.time.substring(5, 10);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                break;
            case PeriodEnum.WEEK:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_w = jsonColumn;
                } else {//从第二个开始需要判断是否相差一周
                    if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                        //如果满足相差一周则标记true
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {
                        //如果不满足相差一周则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                }
                break;
            case PeriodEnum.MONTH:
                jsonColumn.time = jsonColumn.time.substring(0, 7);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                break;
            case PeriodEnum.YEAR:
                jsonColumn.time = jsonColumn.time.substring(0, 4);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, pairsDecimal));
                break;
        }
    }
    delete callData.chart.filter;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

/**
 * Lp占比走势图
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getLpAddressBalance(request, result) {
    let params = request.body
    if (!params.type || !params.token_address || !params.q_0_balance || !params.q_1_balance) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */
    let b_min;//区间筛选balance
    let b_max;//区间筛选balance
    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    let pairAddress;
    if (token) {
        tokenDecimal = token.decimals;
        pairAddress = token.pair_address;
        if (!pairAddress) {
            let pair = await getTokenToPair(token);
            pairAddress = pair.pairAddress;
        }
        pairAddress = pairAddress.replace("0x", "");
        if (params.q_0_balance != "0") {
            params.q_0_balance = Number(params.q_0_balance) * Math.pow(10, tokenDecimal);
        }
        if (params.q_1_balance != "0") {
            params.q_1_balance = Number(params.q_1_balance) * Math.pow(10, tokenDecimal);
        }
        b_min = params.q_0_balance;
        b_max = params.q_1_balance;
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    params.token_address = params.token_address.replace("0x", "");
    let q_0_time;
    let q_1_time;
    let table_type;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.MINUTE_60:
            // table_type = 'balance_changes_PersonalAddress_DateH';
            // q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 86400000)));//往前退24小时
            // q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
            // break;
            case PeriodEnum.MINUTE_240:
                table_type = 'H';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 7 * 86400000))).getTime());//往前退7天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.DAY:
            // table_type = 'balance_changes_PersonalAddress_DateD';
            // q_0_time = getTimeStamp(formatDate_UTCDate(new Date(new Date().getTime() - 8640000000)));//往前退100天
            // q_1_time = getTimeStamp(formatDate_UTCDate(new Date()));
            // break;
            case PeriodEnum.DAY_3:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退365天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.MONTH:
                table_type = 'M';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'Y';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let arg = `WITH PersonalToContract_transfers as ( -- 用户转向合约的transfer
        SELECT
        from,
                    to,
                    sum_value as value
               FROM MV_SUM_LAST_transfers_v2 final
               where address = '${pairAddress}'
                 and to not in ('000000000000000000000000000000000000dead'
                   , '0000000000000000000000000000000000000000')
                 and from_is_contract = false
                 and to_is_contract = true
                 and sum_value
                   > 0
                   )
                   , PersonalToContract_personal_address as ( -- 用户转向合约的用户地址集合
               SELECT DISTINCT
               from as p_address
               FROM PersonalToContract_transfers
                   ), PersonalToContract_contract_address as ( -- 用户转向合约的合约地址集
               SELECT DISTINCT to as c_address
               FROM PersonalToContract_transfers
                   ), ContractToPersonal_transfers as ( -- 合约转向用户的transfer
               SELECT
               from, to, sum_value as value
               FROM MV_SUM_LAST_transfers_v2 final
               where address = '${pairAddress}'
                 and
               from in (select c_address from PersonalToContract_contract_address)
                   and to in (select p_address from PersonalToContract_personal_address)
                   and sum_value > 0
                   ), PersonalToContract_merge as ( -- 合约内外合并
               SELECT
                   p.from as p_from, p.to as p_to, p.value as p_v, c.from as c_from, c.to as c_to, c.value as c_v
               FROM PersonalToContract_transfers p left JOIN ContractToPersonal_transfers c
               ON p.from = c.to and p.to = c.from
                   ),
                   PersonalToContract_merge_notNull AS ( -- 合约内LP数量运算（转入合约的用户地址数据）-（合约转出到用户地址的数据）=最终剩下的合约LP持有数据
               SELECT p_from, p_to, (p_v-c_v) as value
               from PersonalToContract_merge -- 因为是left join会存在ov为空的情况所以加条件ov is not null，而用inner join会丢失为空的部分空的那部分也是需要的
                   ), eoaAddress_merge AS (-- 只取大于0的数据（合约内持有lp数据）
               SELECT p_from, p_to, toInt256(value) as value
               from PersonalToContract_merge_notNull
               where value
                   >0
                   )
                   , eoaAddress_merge_count AS (-- 合约内持有lp的用户地址数量【合约内lp地址数】
               select count (DISTINCT p_from) as eoa_count
               from eoaAddress_merge
                   ), eoaAddress_merge_sum AS (-- 合约内持有lp的用户地址数量【合约内lp地址数】
               select sum (value) as eoa_sum
               from eoaAddress_merge
                   ), balance_changes_PersonalAddressNow AS ( -- 获取当前lp持有的用户地址数据(最后时刻数据)
               select contract, owner, f_time, new_balance, is_contract
               from MV_LAST_balance_changes_v2 FINAL
               where contract = '${pairAddress}'
                 and new_balance
                   > 0
                 and is_contract = false
                   )
                   , balance_changes_PersonalAddressNowCount AS ( -- 获取当前lp持有的用户地址数量(最后时刻数据)【用户lp地址数】
               select count (DISTINCT owner) as personal_count
               FROM balance_changes_PersonalAddressNow
                   ),
                   -- 获取用户持币数量，基于合约内的
                   new_owner_balance_changes_AllAddressNow AS (
               SELECT contract, owner, f_time, new_balance, is_contract
               FROM MV_LAST_balance_changes_v2 FINAL
               WHERE contract = '${params.token_address}'
                 AND new_balance
                   > 0
                 AND owner IN (SELECT DISTINCT owner FROM balance_changes_PersonalAddressNow)
                   )
                   , new_sum_owner_balance_changes_AllAddressNow AS (
               select sum (new_balance) as personal_sum
               FROM new_owner_balance_changes_AllAddressNow
                   ),-- 获取当前合约持有的所有地址(最后时刻数据)
                   owner_balance_changes_AllAddressNow AS (
               SELECT contract, owner, f_time, new_balance, is_contract
               FROM MV_LAST_balance_changes_v2 FINAL
               WHERE contract = '${params.token_address}'
                 AND new_balance
                   > 0
                 AND owner IN (SELECT DISTINCT p_from FROM eoaAddress_merge)
                   )
                   ,
               -- 获取持有当前合约币的所有合约地址数量(最后时刻数据)
                   owner_balance_changes_ContractAddressNowSum AS (
               SELECT sum (t1.new_balance) AS contract_sum
               FROM owner_balance_changes_AllAddressNow t1
               WHERE t1.is_contract = true
                   )
                   ,
               -- 获取当前合约持有的用户地址数量(最后时刻数据)
                   owner_balance_changes_PersonalAddressNowSum AS (
               SELECT sum (t1.new_balance) AS personal_sum
               FROM owner_balance_changes_AllAddressNow t1
               WHERE t1.is_contract = false
                   )
                   ,-- 获取当前区间合约持有的用户地址数量(最后时刻数据)
                   owner_balance_changes_PersonalAddressNowSum_range AS (
               SELECT sum (t1.new_balance) AS personal_sum_range
               FROM owner_balance_changes_AllAddressNow t1
               WHERE t1.is_contract = false
                 AND t1.new_balance >= ${params.q_0_balance}
                 AND t1.new_balance <= ${params.q_1_balance}
                   )
                   ,-- TOP排行榜
                   owner_balance_changes_AllAddressNowRangeTop AS (
               SELECT contract, owner, f_time, new_balance,is_contract
               FROM owner_balance_changes_AllAddressNow
               WHERE new_balance >= ${params.q_0_balance}
                 AND new_balance <= ${params.q_1_balance}
               ORDER BY new_balance DESC LIMIT 10
                   ),-- 获取查询时间之前的最后的数据
                   owner_balance_changes_AllAddressAgo AS (
               SELECT contract, owner, argMax(new_balance, f_time) AS last_balance, is_contract
               FROM MV_${table_type}_new_balance_changes_v2 FINAL
               WHERE contract = '${params.token_address}'
                 AND f_time <= ${q_0_time}
                 AND owner IN (SELECT DISTINCT p_from FROM eoaAddress_merge)
               GROUP BY contract, owner, is_contract
               HAVING last_balance
                    > ${params.q_0_balance}
                  AND last_balance <= ${params.q_1_balance}
                   )
                    ,-- 筛选查询时间之前的最后的持有当前合约币的个人地址
                   owner_balance_changes_PersonalAddressAgo AS (
               SELECT t1.contract, t1.owner, t1.last_balance
               FROM owner_balance_changes_AllAddressAgo t1
               WHERE t1.is_contract = false
                   )
                   ,-- 获取持有当前合约币的所有地址
                   owner_balance_changes_AllAddress AS (
               SELECT contract, owner, new_balance, f_time, is_contract
               FROM MV_${table_type}_new_balance_changes_v2 FINAL
               WHERE contract = '${params.token_address}'
                 AND f_time
                   > ${q_0_time}
                 AND f_time <= ${q_1_time}
                 AND owner IN (SELECT DISTINCT p_from FROM eoaAddress_merge)
                   )
                   ,-- 筛选持有当前合约币的个人地址
                   owner_balance_changes_PersonalAddress AS (
               SELECT t1.contract, t1.owner, t1.new_balance, t1.datetime
               FROM owner_balance_changes_AllAddress t1
               WHERE t1.is_contract = false
                   )
                   ,-- 根据地址和时间格式化分组，取每组最后时刻数据(小时)
                   owner_balance_changes_PersonalAddress_DateH AS (
               SELECT contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d %H:00') AS time
               FROM owner_balance_changes_PersonalAddress
                   ),-- 根据地址和时间格式化分组，取每组最后时刻数据(天)
                   owner_balance_changes_PersonalAddress_DateD AS (
               SELECT contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d') AS time
               FROM owner_balance_changes_PersonalAddress
                   ),-- 根据地址和时间格式化分组，取每组最后时刻数据(月)
                   owner_balance_changes_PersonalAddress_DateM AS (
               SELECT contract, owner, new_balance, formatDateTime(datetime, '%Y-%m') AS time
               FROM owner_balance_changes_PersonalAddress
                   ),-- 根据地址和时间格式化分组，取每组最后时刻数据(年)
                   owner_balance_changes_PersonalAddress_DateY AS (
               SELECT contract, owner, new_balance, formatDateTime(datetime, '%Y') AS time
               FROM owner_balance_changes_PersonalAddress
                   )-- 最终输出（趋势图和历史快照）
    SELECT toJSONString(map(
            'type', 'chart_last',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(last_balance)
                        )) AS json
    FROM owner_balance_changes_PersonalAddressAgo
    UNION ALL
    SELECT toJSONString(map(
            'type', 'chart',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(new_balance),
            'time', toString(time)
                        )) AS json
    FROM owner_balance_changes_PersonalAddress_Date${table_type}
    UNION ALL
    SELECT toJSONString(map(
            'type', 'eoa_count',
            'count', toString(eoa_count)
                        )) AS json
    FROM eoaAddress_merge_count
    UNION ALL
    SELECT toJSONString(map(
            'type', 'eoa_sum',
            'sum', toString(eoa_sum)
                        )) AS json
    FROM eoaAddress_merge_sum
    UNION ALL
    SELECT toJSONString(map(
            'type', 'personal_count',
            'count', toString(personal_count)
                        )) AS json
    FROM balance_changes_PersonalAddressNowCount
    UNION ALL
    SELECT toJSONString(map(
            'type', 'personal_sum',
            'sum', toString(personal_sum)
                        )) AS json
    FROM new_sum_owner_balance_changes_AllAddressNow
    UNION ALL
    SELECT toJSONString(map(
            'type', 'top',
            'address', toString(owner),
            'time', formatDateTime(f_time, '%Y-%m-%d %H:%i'),
            'balance', toString(new_balance),
            'is_contract',toString(is_contract)
                        )) AS json
    FROM owner_balance_changes_AllAddressNowRangeTop`;
    let data = await HQL_sonar(arg);
    let callData = {chart: {time: [], value: [], filter: []}, eoa_count: 0, all_sum: 0, top: []};
    let fixMap = {};
    let isNull = true;//默认查询为空的标记
    if (data && data.length > 10000000) {
        result.json({
            code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
        });
        return;
    }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    fixMap[jsonColumn.address] = jsonColumn;
                    break;
                case "chart":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    callData.chart.filter.push(jsonColumn);
                    break;
                case "eoa_count":
                    callData.eoa_count = jsonColumn.count;
                    break;
                case "personal_count":
                    callData.personal_count = jsonColumn.count;
                    break;
                case "personal_sum_range":
                    jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                    callData.personal_sum_range = jsonColumn.sum;
                    break;
                case "eoa_sum":
                    jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                    callData.eoa_sum = jsonColumn.sum;
                    callData.all_sum += parseFloat(jsonColumn.sum);
                    break;
                case "personal_sum":
                    jsonColumn.sum = formatStrDecimal(jsonColumn.sum, tokenDecimal);
                    callData.personal_sum = jsonColumn.sum;
                    callData.all_sum += parseFloat(jsonColumn.sum);
                    break;
                case "top":
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, tokenDecimal);
                        jsonColumn.balance = jsonColumn.balance;
                        jsonColumn.is_contract = new Boolean(jsonColumn.is_contract);
                    }
                    callData.top.push(jsonColumn);
                    break;
            }
        }
    }
    if (isNull) {
        result.json({
            code: 200, msg: '查询为空！', data: null
        });
        return;
    }
    //修复
    callData.chart.filter = await fixNull(fixMap, callData.chart.filter, {b_min: b_min, b_max: b_max});
    let jsonColumn;
    let jsonColumn_240;//4小时特殊处理对比变量
    let jsonColumn_3day;//3天特殊处理对比变量
    let jsonColumn_w;//周特殊处理对比变量
    for (let i = 0; i < callData.chart.filter.length; i++) {
        jsonColumn = callData.chart.filter[i];
        switch (params.type) {
            case PeriodEnum.MINUTE_60:
                jsonColumn.time = jsonColumn.time.substring(11, 16);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case PeriodEnum.MINUTE_240:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_240 = jsonColumn;
                } else {//从第二个开始需要判断是否相差4小时
                    if (Math.abs(new Date(jsonColumn_240.time).getTime() - new Date(jsonColumn.time).getTime()) >= 14400000) {
                        //如果满足相差4小时则标记true
                        jsonColumn.flag = true;
                        jsonColumn_240 = jsonColumn;
                    } else {
                        //如果不满足相差4小时则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(11, 16));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case PeriodEnum.DAY:
                jsonColumn.time = jsonColumn.time.substring(5, 10);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case PeriodEnum.DAY_3:
                // 2012-02-02 11:11:11
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_3day = jsonColumn;
                } else {//从第二个开始需要判断是否相差3天
                    if (Math.abs(new Date(jsonColumn_3day.time).getTime() - new Date(jsonColumn.time).getTime()) >= 259200000) {
                        //如果满足相差3天则标记true
                        jsonColumn.flag = true;
                        jsonColumn_3day = jsonColumn;
                    } else {
                        //如果不满足相差3天则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case PeriodEnum.WEEK:
                if (i == 0) {//第一个数据必存
                    jsonColumn.flag = true;
                    jsonColumn_w = jsonColumn;
                } else {//从第二个开始需要判断是否相差一周
                    if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                        //如果满足相差一周则标记true
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {
                        //如果不满足相差一周则标记false
                        jsonColumn.flag = false;
                    }
                }
                if (jsonColumn.flag) {//如果为true则记录
                    callData.chart.time.push(jsonColumn.time.substring(5, 10));
                    callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                }
                break;
            case PeriodEnum.MONTH:
                jsonColumn.time = jsonColumn.time.substring(0, 7);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
            case PeriodEnum.YEAR:
                jsonColumn.time = jsonColumn.time.substring(0, 4);
                callData.chart.time.push(jsonColumn.time);
                callData.chart.value.push(formatStrDecimal(jsonColumn.balance, tokenDecimal));
                break;
        }
    }
    delete callData.chart.filter;
    result.json({
        code: 200, msg: '查询成功！', data: callData
    });
}

async function getLpAddressCountRatio(request, result) {
    let params = request.body
    if (!params.type || !params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    params.token_address = params.token_address.toLowerCase();
    if (hasSpecial(params.token_address)) {
        result.json({
            code: 400, msg: '超出运算限额！', data: null
        });
        return;
    }
    /**
     * 查询当前token
     * @type {string}
     */

    let pairAddress;
    let tokenDecimal;
    let token = await getTokenCache(params.token_address);
    if (token) {
        tokenDecimal = token.decimals;
        pairAddress = token.pair_address;
        if (!pairAddress) {
            let pair = await getTokenToPair(token);
            pairAddress = pair.pairAddress;
        }
    } else {
        result.json({
            code: 500, msg: '当前token数据错误！', data: null
        });
        return;
    }
    if (!pairAddress) {
        result.json({
            code: 500, msg: '当前token没有pair数据！', data: null
        });
        return;
    }
    pairAddress = pairAddress.toLowerCase();
    //查询pair
    let pairData = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", pairAddress);
    if (pairData) {
        pairData = JSON.parse(pairData);
    } else {
        result.json({
            code: 500, msg: '当前pair数据错误！', data: null
        });
        return;
    }
    let pairsDecimal = pairData.decimals;
    pairAddress = pairAddress.replace("0x", "");//Psql查询地址小写
    let q_0_time;
    let q_1_time;
    let table_type;
    let table_date;
    if (params.str_time && params.end_time) {//如果有自定义内容
        table_type = 'D';
        q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.str_time))).getTime());
        q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(params.end_time))).getTime());
        if (q_1_time - q_0_time > 4320000) {
            result.json({
                code: 400, msg: `${params.str_time}->${params.end_time},时间范围不可超出50天`, data: null
            });
            return;
        }
    } else {
        switch (params.type) {
            case PeriodEnum.DAY:
                table_type = 'D';
                table_date = '%Y-%m-%d';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.WEEK:
                table_type = 'D';
                table_date = '%Y-%m-%d';
                q_0_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date(new Date().getTime() - 4320000000))).getTime());//往前退50天
                q_1_time = getTimeStamp(formatDate_DayDate(formatDate_UTCDate(new Date())).getTime());
                // q_0_time = getTimeStamp(formatDate_UTCDate(new Date("2023-06-06 13:00:00")));
                // q_1_time = getTimeStamp(formatDate_UTCDate(new Date("2023-08-08 14:14:40")));
                break;
            case PeriodEnum.MONTH:
                table_type = 'M';
                table_date = '%Y-%m';
                q_0_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_MonthDate(formatDate_UTCDate(new Date())).getTime());
                break;
            case PeriodEnum.YEAR:
                table_type = 'Y';
                table_date = '%Y';
                q_0_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date(new Date().getTime() - 63072000000))).getTime());//往前退2年
                q_1_time = getTimeStamp(formatDate_YearDate(formatDate_UTCDate(new Date())).getTime());
                break;
        }
        if (!table_type) {
            result.json({
                code: 400, msg: `${params.type},无效参数`, data: null
            });
            return;
        }
    }
    let in_out_count = 0;
    let all_count = 0;
    let response = {in: null, out: null, all: null};//返回给前端的数据
    /**
     * 计算【合约内，外，内外】
     **/
    let inOutDate = {chart_in: {}, chart_out: {}};
    /**
     * 合约外
     * */
    let arg = `WITH
                   /*
                    持币总地址数
                   */
                   balance_changes_AllAddressNow AS ( -- 获取当前持有合约lp的所有地址(最后时刻数据)
                       select contract, owner, f_time, new_balance, is_contract
                       from MV_LAST_balance_changes_v2 FINAL
                       where contract = '${pairAddress}'
                         and new_balance > 0),
                   balance_changes_AllAddressNowCount AS ( -- 获取当前lp持有的所有地址数量(最后时刻数据)【持币总地址数】
                       select count(DISTINCT owner) as all_count FROM balance_changes_AllAddressNow),
                   /*
                    lp地址数
                   */
                   balance_changes_PersonalAddressNowCount AS ( -- 获取当前lp持有的用户地址数据(最后时刻数据)
                       select count(DISTINCT t1.owner) as personal_count
                       FROM balance_changes_AllAddressNow t1
                       where t1.is_contract = false),
                   /*
                   lp趋势图Ago
                   */
                   balance_changes_PersonalAddressAgo AS ( -- 筛选查询时间之前的最后的持有当前lp的个人地址
                       select contract, owner, argMax(new_balance, f_time) as last_balance
                       from MV_${table_type}_new_balance_changes_v2 FINAL
                       where contract = '${pairAddress}'
                         and f_time <= ${q_0_time}
                         and is_contract = false
                       GROUP BY contract, owner
                       HAVING last_balance
                    > 0
                   )
                    ,
                   /*
                   lp趋势图
                   */
                   balance_changes_PersonalAddress AS ( -- 获取持有当前lp的所有地址
               SELECT contract, owner, new_balance, f_time
               FROM MV_${table_type}_new_balance_changes_v2 FINAL
               where contract = '${pairAddress}'
                 and f_time
                   > ${q_0_time}
                 and f_time <= ${q_1_time}
                 and is_contract = false
                 and new_balance
                   > 0
                   )
                   , balance_changes_PersonalAddress_DateD AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(天)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m-%d') as time
               from balance_changes_PersonalAddress
                   ), balance_changes_PersonalAddress_DateM AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(月)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y-%m') as time
               from balance_changes_PersonalAddress
                   ), balance_changes_PersonalAddress_DateY AS ( -- 根据地址和时间格式化分组，取每组最后时刻数据(年)
               select contract, owner, new_balance, formatDateTime(datetime, '%Y') as time
               from balance_changes_PersonalAddress
                   )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(last_balance)
                        )) AS json
    FROM balance_changes_PersonalAddressAgo
    union all
    SELECT toJSONString(map(
            'type', 'chart',
            'contract', toString(contract),
            'address', toString(owner),
            'balance', toString(new_balance),
            'time', toString(time)
                        )) AS json
    FROM balance_changes_PersonalAddress_Date${table_type}
    union all
    SELECT toJSONString(map(
            'type', 'all_count',
            'count', toString(all_count)
                        )) AS json
    FROM balance_changes_AllAddressNowCount
    union all
    SELECT toJSONString(map(
            'type', 'personal_count',
            'count', toString(personal_count)
                        )) AS json
    FROM balance_changes_PersonalAddressNowCount`;
    let data = await HQL_sonar(arg);
    let callData = {chart: {time: [], value: [], filter: []}};
    let fixMap = {};
    let isNull = true;//默认查询为空的标记
    if (data && data.length > 10000000) {
        result.json({
            code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
        });
        return;
    }
    if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            let json = data[i].json;
            isNull = false;
            let jsonColumn = JSON.parse(json);
            switch (jsonColumn['type']) {
                case "chart_last":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, pairsDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    fixMap[jsonColumn.address] = jsonColumn;
                    break;
                case "chart":
                    if (jsonColumn.balance) {
                        // jsonColumn.balance = formatStrDecimal(jsonColumn.balance, pairsDecimal);
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    callData.chart.filter.push(jsonColumn);
                    break;
                case "personal_count":
                    callData.personal_count = jsonColumn.count;
                    break;
                case "all_count":
                    callData.all_count = jsonColumn.count;
                    break;
            }
        }
    }
    if (!isNull) {
        //修复
        let fixResult = await fixNull(fixMap, callData.chart.filter, null, 'address_count');
        callData.chart.filter = fixResult;
        let jsonColumn;
        let jsonColumn_w;//周特殊处理对比变量
        let time;
        let value;
        for (let i = 0; i < callData.chart.filter.length; i++) {
            jsonColumn = callData.chart.filter[i];
            switch (params.type) {
                case PeriodEnum.DAY:
                    time = jsonColumn.time.substring(5, 10);
                    value = new BigNumber(jsonColumn.count).dividedBy(callData.all_count).multipliedBy(100).toFixed(3);
                    callData.chart.time.push(time);
                    callData.chart.value.push(value);
                    inOutDate.chart_out[jsonColumn.time] = jsonColumn.address;//用于合约内外处理
                    break;
                case PeriodEnum.WEEK:
                    if (i == 0) {//第一个数据必存
                        jsonColumn.flag = true;
                        jsonColumn_w = jsonColumn;
                    } else {//从第二个开始需要判断是否相差一周
                        if (Math.abs(new Date(jsonColumn_w.time).getTime() - new Date(jsonColumn.time).getTime()) >= 7 * 86400000) {
                            //如果满足相差一周则标记true
                            jsonColumn.flag = true;
                            jsonColumn_w = jsonColumn;
                        } else {
                            //如果不满足相差一周则标记false
                            jsonColumn.flag = false;
                        }
                    }
                    if (jsonColumn.flag) {//如果为true则记录
                        time = jsonColumn.time.substring(5, 10);
                        value = new BigNumber(jsonColumn.count).dividedBy(callData.all_count).multipliedBy(100).toFixed(3);
                        callData.chart.time.push(time);
                        callData.chart.value.push(value);
                        inOutDate.chart_out[jsonColumn.time] = jsonColumn.address;//用于合约内外处理
                    }
                    break;
                case PeriodEnum.MONTH:
                    time = jsonColumn.time.substring(0, 7);
                    value = new BigNumber(jsonColumn.count).dividedBy(callData.all_count).multipliedBy(100).toFixed(3);
                    callData.chart.time.push(time);
                    callData.chart.value.push(value);
                    inOutDate.chart_out[jsonColumn.time] = jsonColumn.address;//用于合约内外处理
                    break;
                case PeriodEnum.YEAR:
                    time = jsonColumn.time.substring(0, 4);
                    value = new BigNumber(jsonColumn.count).dividedBy(callData.all_count).multipliedBy(100).toFixed(3);
                    callData.chart.time.push(time);
                    callData.chart.value.push(value);
                    inOutDate.chart_out[jsonColumn.time] = jsonColumn.address;//用于合约内外处理
                    break;
            }
        }
        delete callData.chart.filter;
        response.out = callData;
    }
    all_count = response.out.all_count;//lp总地址
    /**
     * 合约内
     * */
    let arg_in = `WITH
                      /*
                      【合约内lp地址数】
                      */
                      PersonalToContract_transfers as ( -- 用户转向合约的transfer
                          SELECT
                          from,
                      to,
                      sum_value as value
                  FROM MV_SUM_LAST_transfers_v2 final
                  where address = '${pairAddress}'
                    and to not in ('000000000000000000000000000000000000dead'
                      , '0000000000000000000000000000000000000000')
                    and from_is_contract = false
                    and to_is_contract = true
                    and sum_value
                      >0
                      )
                      , PersonalToContract_personal_address as ( -- 用户转向合约的用户地址集合
                  SELECT DISTINCT
                  from as p_address
                  FROM PersonalToContract_transfers
                      ), PersonalToContract_contract_address as ( -- 用户转向合约的合约地址集
                  SELECT DISTINCT to as c_address
                  FROM PersonalToContract_transfers
                      ), ContractToPersonal_transfers as ( -- 合约转向用户的transfer
                  SELECT
                  from, to, sum_value as value
                  FROM MV_SUM_LAST_transfers_v2 final
                  where address = '${pairAddress}'
                    and
                  from in (select c_address from PersonalToContract_contract_address)
                      and to in (select p_address from PersonalToContract_personal_address)
                      and sum_value > 0
                      ), PersonalToContract_merge as ( -- 合约内外合并
                  SELECT
                      p.from as p_from, p.to as p_to, p.value as p_v, c.from as c_from, c.to as c_to, c.value as c_v
                  FROM PersonalToContract_transfers p left JOIN ContractToPersonal_transfers c
                  ON p.from = c.to and p.to = c.from
                      ),
                      PersonalToContract_merge_notNull AS ( -- 合约内LP数量运算（转入合约的用户地址数据）-（合约转出到用户地址的数据）=最终剩下的合约LP持有数据
                  SELECT p_from, p_to, (p_v-c_v) as value
                  from PersonalToContract_merge             -- 因为是left join会存在ov为空的情况所以加条件ov is not null，而用inner join会丢失为空的部分空的那部分也是需要的
                      ), PersonalToContract_merge_Null AS ( -- 合约内LP数量运算（只转入合约没有合约转回的数据）
                  SELECT p_from, p_to, p_v as value
                  from PersonalToContract_merge
                  where c_from = ''
                      )
                      , eoaAddress_merge AS (-- 只取大于0的数据（合约内持有lp数据）
                      (SELECT p_from
                      , p_to
                      , toInt256(value) as value from PersonalToContract_merge_notNull where value
                      >0)
                  UNION ALL
                  (
                  SELECT p_from, p_to, toInt256(value) as value
                  from PersonalToContract_merge_Null
                  where value
                      >0)
                      )
                      , eoaAddress_merge_count AS (-- 合约内持有lp的用户地址数量【合约内lp地址数】
                  select count (DISTINCT p_from) as eoa_count
                  from eoaAddress_merge
                      ),
                      /*
                         【lp地址总数】
                       */
                      balance_changes_PersonalAddressNow AS ( -- 获取当前lp持有的用户地址数据(最后时刻数据)
                  select contract, owner, f_time, new_balance
                  from MV_LAST_balance_changes_v2 FINAL
                  where contract = '${pairAddress}'
                    and is_contract = false
                    and new_balance
                      > 0
                      )
                      , balance_changes_PersonalEoaAddressNowCount AS ( -- 合并合约内外去重【lp地址总数】
                  select count (address) as all_count
                  from
                      ((select DISTINCT p_from as address from eoaAddress_merge) union DISTINCT (select DISTINCT owner as address FROM balance_changes_PersonalAddressNow))
                      ),
                      /*
                      lp趋势图ago
                      */
                      PersonalToContract_transfers_rangeAgo AS ( -- 获取当前持有合约lp的所有地址(之前最后时刻数据)
                  SELECT
                  from, to, sum (sum_value) as value
                  FROM MV_SUM_${table_type}_transfers final
                  where address = '${pairAddress}'
                    and f_time<=${q_0_time}
                    and to not in ('000000000000000000000000000000000000dead'
                      , '0000000000000000000000000000000000000000')
                    and from_is_contract = false
                    and to_is_contract = true
                    and sum_value
                      >0
                  group by
                  from, to
                      ), PersonalToContract_personal_address_rangeAgo as ( -- 用户转向合约的用户地址集合(之前最后时刻数据)
                  SELECT DISTINCT
                  from as p_address
                  FROM PersonalToContract_transfers_rangeAgo
                      ), PersonalToContract_contract_address_rangeAgo as ( -- 用户转向合约的合约地址集(之前最后时刻数据)
                  SELECT DISTINCT to as c_address
                  FROM PersonalToContract_transfers_rangeAgo
                      ), ContractToPersonal_transfers_rangeAgo as ( -- 合约转向用户的transfer(之前最后时刻数据)
                  SELECT
                  from, to, sum (sum_value) as value
                  FROM MV_SUM_${table_type}_transfers final
                  where address = '${pairAddress}'
                    and f_time <= ${q_0_time}
                    and
                  from in (select c_address from PersonalToContract_contract_address_rangeAgo)
                      and to in (select p_address from PersonalToContract_personal_address_rangeAgo)
                      and sum_value > 0
                  group by
                  from, to
                      ), PersonalToContract_merge_rangeAgo as ( -- 合约内外合并
                  SELECT
                      p.from as p_from, p.to as p_to, p.value as p_v, c.from as c_from, c.to as c_to, c.value as c_v
                  FROM PersonalToContract_transfers_rangeAgo p left JOIN ContractToPersonal_transfers_rangeAgo c
                  ON p.from = c.to and p.to = c.from
                      ),
                      PersonalToContract_merge_notNull_rangeAgo AS ( -- 合约内LP数量运算（转入合约的用户地址数据）-（合约转出到用户地址的数据）=最终剩下的合约LP持有数据
                  SELECT p_from, p_to, (p_v-c_v) as value
                  from PersonalToContract_merge_rangeAgo             -- 因为是left join会存在ov为空的情况所以加条件ov is not null，而用inner join会丢失为空的部分空的那部分也是需要的
                      ), PersonalToContract_merge_Null_rangeAgo AS ( -- 合约内LP数量运算（只转入合约没有合约转回的数据）
                  SELECT p_from, p_to, p_v as value
                  from PersonalToContract_merge_rangeAgo
                  where c_from = ''
                      )
                      , eoaAddress_merge_rangeAgo AS (-- 只取大于0的数据（合约内持有lp数据）
                      (SELECT p_from
                      , p_to
                      , toInt256(value) as value from PersonalToContract_merge_notNull_rangeAgo where value
                      >0)
                  UNION ALL
                  (
                  SELECT p_from, p_to, toInt256(value) as value
                  from PersonalToContract_merge_Null_rangeAgo
                  where value
                      >0)
                      )
                      ,
                      /*
                     lp趋势图
                     */
                      PersonalToContract_transfers_rangeDate AS (
                  SELECT
                  from, to, sum_value as value, formatDateTime(f_time, '${table_date}') as time
                  FROM MV_SUM_${table_type}_transfers final
                  where address = '${pairAddress}'
                    and f_time
                      >${q_0_time}
                    and f_time<=${q_1_time}
                    and to not in ('000000000000000000000000000000000000dead'
                      , '0000000000000000000000000000000000000000')
                    and from_is_contract = false
                    and to_is_contract = true
                    and sum_value
                      >0
                      )
                      , PersonalToContract_personal_address_rangeDate as ( -- 用户转向合约的用户地址集合
                  SELECT DISTINCT
                  from as p_address
                  FROM PersonalToContract_transfers_rangeDate
                      ), PersonalToContract_contract_address_rangeDate as ( -- 用户转向合约的合约地址集
                  SELECT DISTINCT to as c_address
                  FROM PersonalToContract_transfers_rangeDate
                      ), ContractToPersonal_transfers_rangeDate as ( -- 合约转向用户的transfer
                  SELECT
                  from, to, sum_value as value, formatDateTime(f_time, '${table_date}') as time
                  FROM MV_SUM_${table_type}_transfers final
                  where address = '${pairAddress}'
                    and f_time
                      > ${q_0_time}
                    and f_time <= ${q_1_time}
                    and
                  from in (select c_address from PersonalToContract_contract_address_rangeDate)
                      and to in (select p_address from PersonalToContract_personal_address_rangeDate)
                      and sum_value>0
                      )
    SELECT toJSONString(map(
            'type', 'chart_last',
            'p_from', toString(p_from),
            'p_to', toString(p_to),
            'value', toString(value)
                        )) AS json
    FROM eoaAddress_merge_rangeAgo
    union all
    SELECT toJSONString(map(
            'type', 'chart_PersonalToContract',
            'r_from', toString(from),
            'r_to', toString(to),
            'time', toString(time),
            'value', toString(value)
                        )) AS json
    FROM PersonalToContract_transfers_rangeDate
    union all
    SELECT toJSONString(map(
            'type', 'chart_ContractToPersonal',
            'r_from', toString(from),
            'r_to', toString(to),
            'time', toString(time),
            'value', toString(value)
                        )) AS json
    FROM ContractToPersonal_transfers_rangeDate
    union all
    SELECT toJSONString(map(
            'type', 'eoa_count',
            'count', toString(eoa_count)
                        )) AS json
    FROM eoaAddress_merge_count
    union all
    SELECT toJSONString(map(
            'type', 'in_out_count',
            'count', toString(all_count)
                        )) AS json
    FROM balance_changes_PersonalEoaAddressNowCount`;
    let data_in = await HQL_sonar(arg_in);
    let callData_in = {
        chart: {time: [], value: [], filter: []},
        chart_p: {time: [], value: [], filter: []},
        chart_c: {time: [], value: [], filter: []}
    };
    let fixMap_in = {};
    let isNull_in = true;//默认查询为空的标记
    if (data && data.length > 10000000) {
        result.json({
            code: 500, msg: '计算量高达：' + data.length + ",已超出运算限额", data: null
        });
        return;
    }
    if (data_in && data_in.length > 0) {
        for (let i = 0; i < data_in.length; i++) {
            let jsons_in = data_in[i].json;
            isNull_in = false;
            let jsonColumn_in = JSON.parse(jsons_in);
            switch (jsonColumn_in['type']) {
                case "chart_last":
                    if (jsonColumn_in.value) {
                        // jsonColumn_in.value = formatStrDecimal(jsonColumn_in.value, pairsDecimal);
                        jsonColumn_in.value = new BigNumber(jsonColumn_in.value);
                    }//new BigNumber(formatUnits(data.rows[i][tokenIndex0], tokenDecimal0)).toFixed();
                    fixMap_in[jsonColumn_in.p_from + '_' + jsonColumn_in.p_to] = jsonColumn_in;
                    break;
                case "chart_PersonalToContract":
                    if (jsonColumn_in.value) {
                        // jsonColumn_in.value = formatStrDecimal(jsonColumn_in.value, pairsDecimal);
                        jsonColumn_in.value = new BigNumber(jsonColumn_in.value);
                    }
                    callData_in.chart_p.filter.push(jsonColumn_in);
                    break;
                case "chart_ContractToPersonal":
                    if (jsonColumn_in.value) {
                        // jsonColumn_in.value = formatStrDecimal(jsonColumn_in.value, pairsDecimal);
                        jsonColumn_in.value = new BigNumber(jsonColumn_in.value);
                    }
                    callData_in.chart_c.filter.push(jsonColumn_in);
                    break;
                case "eoa_count":
                    callData_in.eoa_count = jsonColumn_in.count;
                    break;
                case "in_out_count":
                    callData_in.in_out_count = jsonColumn_in.count;
                    break;
            }
        }
    }
    if (!isNull_in) { //修复
        let fixResult_in = await fixNull_transfer(fixMap_in, callData_in.chart_p.filter, callData_in.chart_c.filter, null, 'address_count');
        callData_in.chart.filter = fixResult_in.result;
        delete callData_in.chart_p;
        delete callData_in.chart_c;
        let jsonColumn_in;
        let jsonColumn_in_w;//周特殊处理对比变量
        let time;
        let value;
        for (let i = 0; i < callData_in.chart.filter.length; i++) {
            jsonColumn_in = callData_in.chart.filter[i];
            switch (params.type) {
                case PeriodEnum.DAY:
                    time = jsonColumn_in.time.substring(5, 10);
                    value = new BigNumber(jsonColumn_in.address.size).dividedBy(all_count).multipliedBy(100).toFixed(3);
                    callData_in.chart.time.push(time);
                    callData_in.chart.value.push(value);
                    inOutDate.chart_in[jsonColumn_in.time] = Array.from(jsonColumn_in.address);//用于合约内外处理
                    break;
                case PeriodEnum.WEEK:
                    if (i == 0) {//第一个数据必存
                        jsonColumn_in.flag = true;
                        jsonColumn_in_w = jsonColumn_in;
                    } else {//从第二个开始需要判断是否相差一周
                        if (Math.abs(new Date(jsonColumn_in_w.time).getTime() - new Date(jsonColumn_in.time).getTime()) >= 7 * 86400000) {
                            //如果满足相差一周则标记true
                            jsonColumn_in.flag = true;
                            jsonColumn_in_w = jsonColumn_in;
                        } else {
                            //如果不满足相差一周则标记false
                            jsonColumn_in.flag = false;
                        }
                    }
                    if (jsonColumn_in.flag) {//如果为true则记录
                        time = jsonColumn_in.time.substring(5, 10);
                        value = new BigNumber(jsonColumn_in.address.size).dividedBy(all_count).multipliedBy(100).toFixed(3);
                        callData_in.chart.time.push(time);
                        callData_in.chart.value.push(value);
                        inOutDate.chart_in[jsonColumn_in.time] = Array.from(jsonColumn_in.address);//用于合约内外处理
                    }
                    break;
                case PeriodEnum.MONTH:
                    time = jsonColumn_in.time.substring(0, 7);
                    value = new BigNumber(jsonColumn_in.address.size).dividedBy(all_count).multipliedBy(100).toFixed(3);
                    callData_in.chart.time.push(time);
                    callData_in.chart.value.push(value);
                    inOutDate.chart_in[jsonColumn_in.time] = Array.from(jsonColumn_in.address);//用于合约内外处理
                    break;
                case PeriodEnum.YEAR:
                    time = jsonColumn_in.time.substring(0, 4);
                    value = new BigNumber(jsonColumn_in.address.size).dividedBy(all_count).multipliedBy(100).toFixed(3);
                    callData_in.chart.time.push(time);
                    callData_in.chart.value.push(value);
                    inOutDate.chart_in[jsonColumn_in.time] = Array.from(jsonColumn_in.address);//用于合约内外处理
                    break;
            }
        }
        delete callData_in.chart.filter;
        response.in = callData_in;
    }
    in_out_count = response.in.in_out_count;//lp内外总地址
    /**
     * 合约内外
     * */
    response.out.in_out_count = in_out_count;//填充合约外缺失的（lp内外总地址）
    response.in.all_count = all_count;//填充合约内缺失的（lp总地址）
    let callData_all = {chart: {time: [], value: [], filter: []}};
    // 合并数组并去重
    let timeSet = new Set([...Object.keys(inOutDate.chart_in), ...Object.keys(inOutDate.chart_out)]);
    // 转换为数组（如果需要）
    let timeArray = Array.from(timeSet);
    timeArray.sort(function (time0, time1) {//正序
        return new Date(time0).getTime() - new Date(time1).getTime();
    });
    if (timeArray.length > 0) {
        let chart_in_item = [];
        let chart_out_item = [];
        for (let i = 0; i < timeArray.length; i++) {
            let time = timeArray[i];
            if (inOutDate.chart_in[time]) {
                chart_in_item = inOutDate.chart_in[time];
            }
            if (inOutDate.chart_out[time]) {
                chart_in_item = inOutDate.chart_out[time];
            }
            let value = new Set([...chart_in_item, ...chart_out_item]);
            callData_all.chart.time.push(time);
            value = new BigNumber(value.size).dividedBy(all_count).multipliedBy(100).toFixed(3);
            callData_all.chart.value.push(value);
        }
        delete callData_all.chart.filter;
        response.all = callData_all;
        response.all.in_out_count = in_out_count;//填充合约外缺失的（lp内外总地址）
        response.all.all_count = all_count;//填充合约内缺失的（lp总地址）
    }
    result.json({
        code: 200, msg: '查询成功！', data: response
    });
}

/**
 * 获取最大供应量
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getTokenMaxTotal(request, result) {
    let params = request.body
    if (!params.token_address) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    let tokenTotalSupper = await redis.hGet("token_max_total_supply", params.token_address);
    if (tokenTotalSupper) {
        result.json({
            code: 200, msg: '获取成功', data: tokenTotalSupper
        });
        return;
    } else {
        result.json({
            code: 500, msg: '当前无token数据！', data: null
        });
        return;
    }
    return;
}

/**
 * 设置最大供应量
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function setTokenMaxTotal(request, result) {
    let params = request.body
    if (!params.token_address || !params.total_supply) {
        result.json({
            code: 400, msg: '参数不完整', data: null
        });
        return;
    }
    await redis.hSet("token_max_total_supply", params.token_address, params.total_supply);
    result.json({
        code: 200, msg: '更新成功'
    });
    return;
}

/**
 * Pk的lp占比，质押
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function getPkLpHold(request, result) {
    try {
        if (!request.body.tokenAddress) {
            result.json({
                code: 400, msg: 'tokenAddress参数不能为空', data: null
            })
            return;
        }
        /**
         * 查询当前token
         * @type {string}
         */
        let token = await getTokenCache(request.body.tokenAddress);
        let params = {};
        if (token) {
            params.token_address = token.address;
            params.pair_address = token.pair_address;
            if (!params.pair_address) {
                let pair = await getTokenToPair(token);
                params.pair_address = pair.pairAddress;
            }
            if (!params.pair_address || params.pair_address == "") {
                result.json({
                    code: 500, msg: '当前token没有交易对！', data: null
                });
                return;
            }
        } else {
            result.json({
                code: 500, msg: '当前token数据错误！', data: null
            });
            return;
        }
        /**
         * 查询当前pair
         * @type {string}
         */
        let decimals;
        let pair = await redis.hGet(FirstAnalyzeEnum.pair_json_hdata + "_v2", params.pair_address);
        if (pair) {
            let obj = JSON.parse(pair);
            decimals = obj.decimals;
            if (decimals == undefined) {
                result.json({
                    code: 500, msg: '当前pair没有decimals！', data: null
                });
                return;
            }
        } else {
            result.json({
                code: 500, msg: '当前pair数据错误！', data: null
            });
            return;
        }
        params.token_address = params.token_address.replace("0x", "");//Psql查询地址小写
        params.pair_address = params.pair_address.replace("0x", "");//Psql查询地址小写
        let arg = `WITH token_balance_contract AS ( -- 获取当前token持有的合约地址数据(最后时刻数据)
            select contract, owner, f_time, new_balance, is_contract
            from MV_LAST_balance_changes_v2 FINAL
            where contract = '${params.token_address}'
              and new_balance > 0
              and is_contract = true),
                        pair_balance_contract AS ( -- 获取当前lp持有的合约地址数据(最后时刻数据)
                            select contract, owner, f_time, new_balance, is_contract
                            from MV_LAST_balance_changes_v2 FINAL
                            where contract = '${params.pair_address}'
                              and new_balance > 0
                              and is_contract = true),
                        pair_balance_personal AS ( -- 获取当前lp持有的用户地址数据(最后时刻数据)
                            select contract, owner, f_time, new_balance, is_contract
                            from MV_LAST_balance_changes_v2 FINAL
                            where contract = '${params.pair_address}'
                              and new_balance > 0
                              and is_contract = false),
                        pair_balance_contract_allBalance AS ( -- 获取当前lp持有的合约地址的持有数量(最后时刻数据)
                            select sum(new_balance) as all_balance
                            from pair_balance_contract),
                        pair_balance_allBalance AS ( -- 获取当前lp持有所有持有数量(最后时刻数据)
                            select sum(new_balance) as all_balance
                            FROM (select all_balance as new_balance
                                  from pair_balance_contract_allBalance
                                  UNION ALL
                                  select new_balance
                                  from pair_balance_personal))
                   SELECT toJSONString(map(
                           'type', 'pairLp_hold',
                           'balance', toString(all_balance)
                                       )) AS json
                   FROM pair_balance_contract_allBalance
                   UNION ALL
                   SELECT toJSONString(map(
                           'type', 'allLp_hold',
                           'balance', toString(all_balance)
                                       )) AS json
                   FROM pair_balance_allBalance
                   UNION ALL
                   SELECT toJSONString(map(
                           'type', 'tokenContract_hold',
                           'contract', toString(contract),
                           'address', toString(owner),
                           'balance', toString(new_balance)
                                       )) AS json
                   FROM token_balance_contract`;
        let data = await HQL_sonar(arg);
        let resultMap = {};
        if (data) {
            let jsonMap = {tokenContract_hold: []}
            let rows = data;
            let jsonColumn;
            for (let i = 0; i < rows.length; i++) {
                jsonColumn = JSON.parse(rows[i].json);
                if (jsonColumn.type == "pairLp_hold") {
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, decimals);//转换精度
                    }
                    jsonMap.pairLp_hold = jsonColumn;
                } else if (jsonColumn.type == "allLp_hold") {
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, decimals);//转换精度
                    }
                    jsonMap.allLp_hold = jsonColumn;
                } else if (jsonColumn.type == "tokenContract_hold") {
                    if (jsonColumn.balance) {
                        jsonColumn.balance = formatStrDecimal(jsonColumn.balance, decimals);//转换精度
                        jsonColumn.balance = new BigNumber(jsonColumn.balance);
                    }
                    jsonMap.tokenContract_hold.push(jsonColumn);
                }
            }
            //合计lp质押总数
            let lp_sum = new BigNumber(jsonMap.pairLp_hold.balance);
            // for (let index = 0; index < jsonMap.pairLp_hold.length; index++) {
            //     lp_sum = lp_sum.plus(jsonMap.pairLp_hold[index].balance);
            // }
            //lp占比
            let lpRate = lp_sum.div(jsonMap.allLp_hold.balance);
            resultMap.lpRate = lpRate.toFixed(3, BigNumber.ROUND_DOWN);//向下取整
            resultMap.lpSum = lp_sum.toFixed(3, BigNumber.ROUND_DOWN);
            //合约持币占比
            resultMap.contractRate = {
                TOP10: new BigNumber(0), TOP20: new BigNumber(0), TOP50: new BigNumber(0), TOP100: new BigNumber(0)
            };
            //倒叙
            jsonMap.tokenContract_hold.sort((a, b) => b['balance'].comparedTo(a['balance']));
            for (let index = 0; index < jsonMap.tokenContract_hold.length; index++) {
                if (index < 10) {
                    resultMap.contractRate.TOP10 = resultMap.contractRate.TOP10.plus(jsonMap.tokenContract_hold[index].balance);
                }
                if (index < 20) {
                    resultMap.contractRate.TOP20 = resultMap.contractRate.TOP20.plus(jsonMap.tokenContract_hold[index].balance);
                }
                if (index < 50) {
                    resultMap.contractRate.TOP50 = resultMap.contractRate.TOP50.plus(jsonMap.tokenContract_hold[index].balance);
                }
                if (index < 100) {
                    resultMap.contractRate.TOP100 = resultMap.contractRate.TOP100.plus(jsonMap.tokenContract_hold[index].balance);
                }
            }
            for (let key in resultMap.contractRate) {
                resultMap.contractRate[key] = resultMap.contractRate[key].toFixed();
            }
            resultMap.ago_time = Date.now();
        }
        result.json({
            code: 200, msg: '成功', data: resultMap
        });
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        });
    }
}

module.exports = {
    getLpAddressContract,
    getLpAddressContractOut,
    getTradeAmount_Chart,
    getTradeTop_Chart,
    getPool_Chart,
    getPoolRatio_Chart,
    getChangeHand_Chart,
    getTokenHand_Chart,
    getTokenHandAmount_Chart,
    getContractsHolder,
    getCirculationRatio_Chart,
    getContractsHand_Base,
    getContractsHand_Chart,
    getLpAddressBalance,
    getLpAddressCountRatio,
    getTokenMaxTotal,
    setTokenMaxTotal,
    getPkLpHold,
    getTokenPsqlUsdt,
    USD_LIST
}

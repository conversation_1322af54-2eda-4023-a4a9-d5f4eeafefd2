const redis = require('../database/redis');
const constant = require("../utils/constant");
const PairModel = require("../model/mysql/PairModel");
const {dbPairQuery, dbInsertPair, dbUpdatePair} = require("../model/mysql/AviPairModel");
const TokenModel = require("../model/mysql/TokenModel");
const {default: BigNumber} = require('bignumber.js');
BigNumber.config({DECIMAL_PLACES: 100});
const {Op} = require("sequelize");
const {getStableTokens} = require("./stableTokenService");
const {getTokenIndexByAddressAndTokens} = require("./SecurityService");
const {checkStableCoins, formatAddress, getPairReserveUsd, isBusd, getCurrentPriceUsd} = require("../utils/functions");
let uuid = require("uuid");
const kafka = require("../utils/kafka/kafkajs");
const {slAvePairs} = require("../utils/doris/avePairsSL");
const {slAveTokens} = require("../utils/doris/aveTokensSL");

/**
 * 获取交易对信息 不能从这里获取价格
 * @param { String } address
 * @returns
 */
async function getCachePairByAddress(address) {
    // let row = await redis.hGet(constant.PAIR_CACHE_KEY, address);
    // if (row && row != "-1") {
    //     row = JSON.parse(row);
    // } else {
        let pair = await PairModel().findOne({
            where: {address: address},
            attributes: ['id', 'chain_id', 'swap_id', 'address', 'token0_id', 'token1_id', 'decimals', 'token_id', 'token_index', 'status', 'path'],
        });
        if (pair) {
            row = pair.dataValues;
            // await redis.hSet(constant.PAIR_CACHE_KEY, address, pair.dataValues);
        } else {
            console.log("没有ave_pair表没有数据：address=" + address);
        }
    // }
    return row;
}

/**
 *
 * @param { Number } pairId
 * @returns
 */
async function getCachePairById(pairId) {
    if (!pairId) return false;
    const exists =null;// await redis.hExists(constant.PAIR_CACHE_KEY, pairId);
    let row = false;
    if (exists) {
        row = await redis.hGet(constant.PAIR_CACHE_KEY, pairId).catch(e => {
            console.error(constant.PAIR_CACHE_KEY + "获取失败", e.message);
        });
        if (row) row = JSON.parse(row);
    } else {
        let pair = await PairModel().findByPk(pairId, {
            attributes: ['id', 'chain_id', 'swap_id', 'address', 'token0_id', 'token1_id', 'decimals', 'token_id', 'token_index', 'status', 'path'],
        });
        if (pair) {
            row = pair.dataValues;
            // await redis.hSet(constant.PAIR_CACHE_KEY, pairId, pair.dataValues).catch(e => {
            //     console.error(constant.PAIR_CACHE_KEY + "设置失败", e.message);
            // });
        }
    }
    return row;
}

/**
 *
 * @param { Number } pairId
 * @returns { PairModel }
 */
async function getPairById(pairId) {
    return await PairModel().findOne({
        where: {id: pairId},
        include: [
            {
                model: TokenModel(),
                as: 'token0',
            },
            {
                model: TokenModel(),
                as: 'token1',
            },
        ],
    });
}

/**
 *
 * @param { Number } pairId
 * @param { Array<String> } attributes
 * @returns { PairModel }
 */
async function getOnlyPairById(pairId, attributes) {
    return await PairModel().findOne({
        where: {id: pairId},
        attributes: attributes,
    });
}

/**
 *
 * @param { object } docs
 * @param { Number } docs.chain_id 链ID 如 币安链 56
 * @param { String } docs.address 交易对地址
 * @param { Number } docs.swap_id 交易所ID
 * @param { Number } docs.pair_index 交易对index
 * @param { Array<String> } tokens
 * @param { String } tokens[0] token0地址
 * @param { String } tokens[1] token1地址
 * @returns
 */
async function savePair(docs, docs_1, tokens) {
    try {
        let pairInfos = await dbPairQuery([{
            type: "string",
            column: "address",
            value: docs.address
        }]);
        //mysql查询
        let pairInfo = await PairModel().findOne({
            where: {
                address: docs.address,
            }
        });

        // let tokenModel = TokenModel();
        // let token0Info = await tokenModel.findOne({
        //     where: {
        //         address:tokens[0]
        //     }
        // });
        // if(!token0Info){
        //     console.error("没有token0代币数据");
        //     token0Info = await tokenService.saveToken(tokens[0]);
        //     if(!token0Info){
        //         return;
        //     }
        // }
        //
        // let token1Info = await tokenModel.findOne({
        //     where: {
        //         address:tokens[1]
        //     }
        // });
        // if(!token1Info){
        //     console.error("没有token1代币数据");
        //     token1Info = await tokenService.saveToken(tokens[1]);
        //     if(!token1Info){
        //         return;
        //     }
        // }
        // const pairContract = new Contract(docs.address, pairAbi, provider);
        if (!pairInfos || pairInfos.length == 0) {
            const reserves = [0, 0];//await pairContract.getReserves().catch(e => console.log("错误9",e.message));
            //doris
            docs.reserve0 = reserves[0] ? reserves[0] + '' : '';
            docs.reserve1 = reserves[1] ? reserves[1] + '' : '';
            const decimals = 18;//await pairContract.decimals().catch(e => console.log("错误10",e.message));
            //doris
            docs.decimals = Number(decimals);
        } else {
            //doris
            docs.reserve0 = pairInfo.reserve0 ? pairInfo.reserve0 + '' : '';
            docs.reserve1 = pairInfo.reserve1 ? pairInfo.reserve1 + '' : '';
        }

        if (!pairInfo) {
            const reserves = [0, 0];//await pairContract.getReserves().catch(e => console.log("错误9",e.message));
            // docs.reserve0 = formatUnits(reserves[0],token0Info.decimals);
            // docs.reserve1 = formatUnits(reserves[1],token1Info.decimals);
            //mysql
            docs_1.reserve0 = reserves[0] ? reserves[0] + '' : '';
            docs_1.reserve1 = reserves[1] ? reserves[1] + '' : '';
            const decimals = 18;//await pairContract.decimals().catch(e => console.log("错误10",e.message));
            // if(decimals == void 0){
            //     return;
            // }
            //mysql
            docs_1.decimals = Number(decimals);
        } else {
            //mysql
            docs_1.reserve0 = pairInfo.reserve0 ? pairInfo.reserve0 + '' : '';
            docs_1.reserve1 = pairInfo.reserve1 ? pairInfo.reserve1 + '' : '';
        }
        // const reserve1 = new BigNumber(docs.reserve1);
        // const isZero = reserve1.eq(0);
        // const ratio = !isZero ? new BigNumber(docs.reserve0).div(reserve1) : new BigNumber(0);
        //doris
        const totalSupply = '0';//await pairContract.totalSupply().catch(e => console.log("错误7",e.message));
        docs.swap_id = 0;
        docs.total_supply = totalSupply;
        docs.token0_id = '';//token0Info.id;
        docs.token1_id = '';//token1Info.id;
        docs.token0_price_usd = '0';
        docs.token0_price_eth = '0';
        docs.token1_price_usd = '0';
        docs.token1_price_eth = '0';
        //mysql
        const totalSupply2 = 0;//await pairContract.totalSupply().catch(e => console.log("错误7",e.message));
        docs_1.swap_id = 0;
        docs_1.total_supply = totalSupply2;
        docs_1.token0_id = 0;//token0Info.id;
        docs_1.token1_id = 0;//token1Info.id;
        docs_1.token0_price_usd = 0;
        docs_1.token0_price_eth = 0;
        docs_1.token1_price_usd = 0;
        docs_1.token1_price_eth = 0;
        // docs.path = "";
        //BUSD 的价格为 1
        // if((isToken0 = isBusd(tokens[0])) || isBusd(tokens[1])){
        //     if(isToken0){
        //         console.log("token0------------>BUSD")
        //         docs.token0_price_usd = 1;
        //         docs.token1_price_usd = new BigNumber(1).multipliedBy(ratio).toFixed(40);
        //         docs.token_index = 1;
        //         docs.token_id = token1Info.id;
        //     }else{
        //         console.log("token1------------>BUSD")
        //         docs.token0_price_usd = !isZero ? new BigNumber(1).div(ratio).toFixed(40) : 0;
        //         docs.token1_price_usd = 1;
        //         docs.token_index = 0;
        //         docs.token_id = token0Info.id;
        //     }
        // }else{
        //     if(!pairInfo || !pairInfo.token_confirm){
        //         docs = await tokenConfirm(docs, token0Info, token1Info, ratio, isZero);
        //     }else if(pairInfo && pairInfo.token_confirm === 1){
        //         docs = await updateTokenPrice(docs,pairInfo,ratio,isZero);
        //     }
        //     docs.reserve_usd = 0;
        //     console.log("token_confirm",pairInfo.token_confirm);
        //     console.log("is_open",pairInfo.is_open);
        //     if(pairInfo && pairInfo.token_confirm == 1 && pairInfo.is_open == 1){
        //         docs.reserve_usd = getPairReserveUsd(docs).toFixed(40);
        //     }
        //     console.log("交易对地址",docs.address);
        //     console.log("token0_price_usd",typeof docs.token0_price_usd == 'object' ? docs.token0_price_usd.toString() : docs.token0_price_usd);
        //     console.log("token1_price_usd",typeof docs.token1_price_usd == 'object' ? docs.token1_price_usd.toString() : docs.token1_price_usd);
        //     console.log("reserve_usd",typeof docs.reserve_usd == 'object' ? docs.reserve_usd.toString() : docs.reserve_usd);
        // }

        if (!pairInfos || pairInfos.length == 0) {
            // console.log(docs);
            // const path = docs.path;
            docs.id = uuid.v4();
            // docs.path = docs.id;
            await dbInsertPair([docs])
            //  pairInfo.update({
            //     path: path ? pairInfo.id + ',' + path : pairInfo.id,
            // });
        }
        if (!pairInfo) {
            await PairModel().create(docs_1);
        } else {
            // let docs_1 = docs.path ? pairInfos[0].id + ',' + docs.path : pairInfos[0].id;
            // docs.path = pairInfo.update(docs);
            // //删除主键字段，因为doris不允许更新主键
            // delete docs.id;
            // delete docs.address;
            // delete docs.created_time;
            // await dbUpdatePair([{
            //     type: "string",
            //     column: "id",
            //     value: pairInfos[0].id
            // }], [docs])
        }
        //console.log("开始创建");
    } catch (ex) {
        //console.log("错误：");
        console.log("savePair当前错误：", ex);
        throw ex;
    }

}

async function savePairList(slAvePairs,pairs,blockNumber,partition) {
    try {
        if (pairs && pairs.length > 0) {
            let doris_arry = [];
            let mysql_arry = [];
            for (let i = 0; i < pairs.length; i++) {
                let docs_0 = pairs[i].docs_0;
                let docs_1 = pairs[i].docs_1;
                const reserves = [0, 0];//await pairContract.getReserves().catch(e => console.log("错误9",e.message));
                //doris,mysql
                docs_0.reserve0 = reserves[0] ? reserves[0] + '' : '';
                docs_0.reserve1 = reserves[1] ? reserves[1] + '' : '';
                docs_1.reserve0 = reserves[0] ? reserves[0] + '' : '';
                docs_1.reserve1 = reserves[1] ? reserves[1] + '' : '';
                const decimals = 18;//await pairContract.decimals().catch(e => console.log("错误10",e.message));
                //doris,mysql
                docs_0.decimals = Number(decimals);
                docs_1.decimals = Number(decimals);
                //doris
                const totalSupply = '0';//await pairContract.totalSupply().catch(e => console.log("错误7",e.message));
                docs_0.id = uuid.v4();
                docs_0.swap_id = 0;
                docs_0.total_supply = totalSupply;
                docs_0.token0_id = '';//token0Info.id;
                docs_0.token1_id = '';//token1Info.id;
                docs_0.token0_price_usd = '0';
                docs_0.token0_price_eth = '0';
                docs_0.token1_price_usd = '0';
                docs_0.token1_price_eth = '0';
                //mysql
                const totalSupply2 = 0;//await pairContract.totalSupply().catch(e => console.log("错误7",e.message));
                docs_1.swap_id = 0;
                docs_1.total_supply = totalSupply2;
                docs_1.token0_id = 0;//token0Info.id;
                docs_1.token1_id = 0;//token1Info.id;
                docs_1.token0_price_usd = 0;
                docs_1.token0_price_eth = 0;
                docs_1.token1_price_usd = 0;
                docs_1.token1_price_eth = 0;
                let row = `${docs_0.address?docs_0.address:''},${docs_0.created_time?docs_0.created_time:''},${docs_0.token0_id?docs_0.token0_id:''},${docs_0.token0_addr?docs_0.token0_addr:''},${docs_0.token1_id?docs_0.token1_id:''},${docs_0.token1_addr?docs_0.token1_addr:''},${docs_0.swap_id?docs_0.swap_id:''},${docs_0.decimals?docs_0.decimals:18},${docs_0.created_block?docs_0.created_block:0},${docs_0.chain_id?docs_0.chain_id:''},${docs_0.created_hash?docs_0.created_hash:''},${docs_0.reserve0?docs_0.reserve0:''},${docs_0.reserve1?docs_0.reserve1:''},${docs_0.pair_index?docs_0.pair_index:0},${docs_0.token0_price_usd?docs_0.token0_price_usd:''},${docs_0.token0_price_eth?docs_0.token0_price_eth:''},${docs_0.token1_price_usd?docs_0.token1_price_usd:''},${docs_0.token1_price_eth?docs_0.token1_price_eth:''},${docs_0.reserve_usd?docs_0.reserve_usd:''},${docs_0.path?docs_0.path:''},${docs_0.token_id?docs_0.token_id:''},${docs_0.token_index?docs_0.token_index:0},${docs_0.status?docs_0.status:1},${docs_0.holders?docs_0.holders:0},${docs_0.tx_count?docs_0.tx_count:0},${docs_0.tx_volume?docs_0.tx_volume:''},${docs_0.tx_volume_usd?docs_0.tx_volume_usd:''},${docs_0.tx_volume_u_24h?docs_0.tx_volume_u_24h:''},${docs_0.tx_count_24h?docs_0.tx_count_24h:0},${docs_0.tx_volume_24h?docs_0.tx_volume_24h:''},${docs_0.price_change_24h?docs_0.price_change_24h:''},${docs_0.token_confirm?docs_0.token_confirm:0},${docs_0.open_price?docs_0.open_price:''},${docs_0.open_time?docs_0.open_time:0},${docs_0.is_open?docs_0.is_open:0},${docs_0.total_supply?docs_0.total_supply:''},${docs_0.create_time?docs_0.create_time:''},${docs_0.update_time?docs_0.update_time:''},${docs_0.lp_proportion?docs_0.lp_proportion:0},${docs_0.publish_time?docs_0.publish_time:''},${docs_0.open_tag?docs_0.open_tag:''},${docs_0.id?docs_0.id:''}`;
                doris_arry.push(row);
                mysql_arry.push(docs_1);
            }

            let threadPromise = [];//开启多线程
            threadPromise.push(new Promise(async (resolve, reject) => {
                try {
                    //以下是steamLoad
                    slAvePairs(doris_arry,blockNumber);
                    resolve();
                    //以下是kafka
                    // let p = await kafka.getProducer();
                    // let s = JSON.stringify(doris_arry);
                    // let all = "" + s;
                    // const objs = [{
                    //     topic: 'avePairs',
                    //     messages: all,
                    //     partition:partition
                    // }];
                    // p.send(objs, (err, data) => {
                    //     if (err) {
                    //         throw err
                    //     }
                    //     resolve();
                    // });
                } catch (error) {
                    reject(error);
                }
            }));
            threadPromise.push(new Promise(async (resolve, reject) => {
                try {
                    await PairModel().bulkCreate(mysql_arry, {ignoreDuplicates: true});
                    resolve();
                } catch (e) {
                    reject(e);
                }
            }));
            let p = Promise.all(threadPromise);
            await p.then(arr => {
                pairs.length = 0;
            }, e => {
                throw e;
            })//等待所有线程解析完成
        }
    } catch (ex) {
        console.log("savePairList当前错误");
        throw ex;
    }

}

/**
 *
 * @param { PairModel } pairInfo
 */
async function updateTokenInfo(pairInfo) {
    const pair = await PairModel().findOne({
        where: {
            'token_id': pairInfo.token_id
        },
        order: [
            ['reserve_usd', 'DESC'],
        ],
    });
    await TokenModel().update({
        current_price_usd: getCurrentPriceUsd(pair),
        pair_id: pairInfo.id,
    }, {
        where: {
            id: pairInfo.token_id,
        }
    });
}

/**
 *
 * @param { object } docs
 * @param { PairModel } pairInfo
 * @param { BigNumber } ratio
 * @param { boolean } isZero
 * @returns
 */
async function updateTokenPrice(docs, pairInfo, ratio, isZero) {
    const tokenId = pairInfo.token_index === 0 ? pairInfo.token0_id : pairInfo.token1_id;
    const tokenBId = pairInfo.token_index === 0 ? pairInfo.token1_id : pairInfo.token0_id;
    const pairBInfo = await findTokenMaxReservePair(tokenBId, docs);

    const tokenBPrice = await getPairTokenAPrice(pairBInfo.address, tokenBId);
    if (pairInfo.token_index === 0) {
        docs.token1_price_usd = tokenBPrice;
        docs.token0_price_usd = !isZero ? new BigNumber(docs.token1_price_usd).div(ratio).toFixed(40) : 0;

        console.log(docs.token1_price_usd);
        console.log(docs.token0_price_usd);
    } else {
        docs.token0_price_usd = tokenBPrice;
        docs.token1_price_usd = new BigNumber(docs.token0_price_usd).multipliedBy(ratio).toFixed(40);

        console.log(docs.token0_price_usd);
        console.log(docs.token1_price_usd);
    }
    docs.token_id = tokenId;
    docs.path = pairBInfo.path;
    return docs;
}

/**
 *
 * @param {object} docs
 * @param {String} token0
 * @param {String} token1
 * @param {BigNumber} ratio
 * @param {boolean} isZero
 * @returns
 */
async function tokenConfirm(docs, token0, token1, ratio, isZero) {
    let tokenIndex = await getTokenIndexByAddressAndTokens(docs.address, token0.address, token1.address);
    let maxTokenPair = {};
    if (tokenIndex == -1) {
        const stableTokens = await getStableTokens();
        const stableToken0 = checkStableCoins(stableTokens, token0.address);
        const stableToken1 = checkStableCoins(stableTokens, token1.address);

        if (stableToken0 && stableToken1) {
            console.log("都是文档币");
            const token0Pair = await findStableUsdtPair(token0.id, docs);
            const token1Pair = await findStableUsdtPair(token1.id, docs);
            maxTokenPair = compareMaxReservePair(token0Pair, token1Pair);
            //console.log("什么情况看",maxTokenPair);
        } else if (stableToken0) {
            console.log("0------是文档币");
            const tokenPair = await findStableUsdtPair(token0.id, docs);
            maxTokenPair = {
                pair: tokenPair,
                token_index: 1,
            }
        } else if (stableToken1) {
            console.log("1------是文档币");
            const tokenPair = await findStableUsdtPair(token1.id, docs);
            maxTokenPair = {
                pair: tokenPair,
                token_index: 0,
            }
        } else {
            console.log("都不是的----");
            const token0MaxReservePair = await findTokenMaxReservePair(token0.id, docs);
            //console.log("token0MaxReservePair--->",token0MaxReservePair);
            const token1MaxReservePair = await findTokenMaxReservePair(token1.id, docs);
            //console.log("token1MaxReservePair--->",token1MaxReservePair);
            //console.log("token0MaxReservePair",token0MaxReservePair);
            //console.log("token1MaxReservePair",token1MaxReservePair);token1
            maxTokenPair = compareMaxReservePair(token0MaxReservePair, token1MaxReservePair);
        }
    } else {
        let tokenPair = await findTokenMaxReservePair(tokenIndex == 0 ? token0.id : token1.id, docs);
        maxTokenPair = {
            token_index: tokenIndex,
            pair: tokenPair,
        };
    }


    if (maxTokenPair.token_index == 0 && maxTokenPair.pair) {
        docs.token_id = token0.id;
        docs.token_index = 0;
        docs.path = maxTokenPair.pair.path;

        //console.log("哈哈哈哈",maxTokenPair.pair.dataValues)
        docs.token1_price_usd = await getPairTokenAPrice(maxTokenPair.pair.address, token1.id);
        docs.token0_price_usd = !isZero ? new BigNumber(docs.token1_price_usd).div(ratio).toFixed(40) : 0;
    } else if (maxTokenPair.token_index == 1 && maxTokenPair.pair) {
        docs.token_id = token1.id;
        docs.token_index = 1;
        docs.path = maxTokenPair.pair.path;

        docs.token0_price_usd = await getPairTokenAPrice(maxTokenPair.pair.address, token0.id);
        docs.token1_price_usd = new BigNumber(docs.token0_price_usd).multipliedBy(ratio).toFixed(40);
    }
    return docs;
}

/**
 *
 * @param {Number} tokenId
 * @param {object} docs
 * @returns
 */
async function findTokenMaxReservePair(tokenId, docs) {
    const pairList = await PairModel().findAll({
        where: {
            swap_id: docs.swap_id,
            address: {
                [Op.ne]: docs.address
            },
            token_confirm: 1,
            is_open: 1,
            status: 1,
            token_id: tokenId
        },
    });
    let tokenPair = null;
    for (const item of pairList) {
        if (!tokenPair && new BigNumber(item.reserve_usd).comparedTo(1000000000000000) < 0) {
            tokenPair = item
            continue;
        }
        ;
        if (new BigNumber(tokenPair.tx_volume_usd).comparedTo(0) > 0 || new BigNumber(item.tx_volume_usd).comparedTo(0) > 0) {
            if (new BigNumber(tokenPair.tx_volume_usd).comparedTo(item.tx_volume_usd) < 0) {
                tokenPair = item;
            }
        } else {
            let tokenReserve = tokenPair.token0_id == tokenId ? tokenPair.reserve0 : tokenPair.reserve1;
            let itemReserve = item.token0_id == tokenId ? item.reserve0 : item.reserve1;
            let reserveComp = new BigNumber(tokenReserve).comparedTo(itemReserve);
            if (reserveComp < 0) {
                tokenPair = item;
            } else if (reserveComp == 0) {
                if (new BigNumber(tokenPair.reserve_usd).comparedTo(item.reserve_usd) < 0) {
                    if (new BigNumber(item.reserve_usd).comparedTo(1000000000000000) < 0) {
                        if (new BigNumber(item.token0_price_usd).comparedTo(1000000) < 0 &&
                            new BigNumber(item.token1_price_usd).comparedTo(1000000) < 0) {
                            tokenPair = item;
                        }
                    }
                }
            }
        }
    }
    return tokenPair;
}

/**
 *
 * @param {Number} tokenId
 * @returns
 */
async function getTokenMaxReservePair(tokenId) {
    const pairList = await PairModel().findAll({
        where: {
            status: 1,
            token_id: tokenId
        },
    });
    let tokenPair = null;
    for (let i = 0; i < pairList.length; i++) {
        if (!tokenPair) {
            if (new BigNumber(pairList[i].reserve_usd).comparedTo(1000000000000000) < 0) {
                tokenPair = pairList[i]
            }
            continue;
        }
        ;
        if (new BigNumber(tokenPair.tx_volume_usd).comparedTo(0) > 0 && new BigNumber(pairList[i].tx_volume_usd).comparedTo(0) > 0) {
            if (new BigNumber(tokenPair.tx_volume_usd).comparedTo(pairList[i].tx_volume_usd) < 0) {
                tokenPair = pairList[i];
            }
        } else {
            if (new BigNumber(tokenPair.reserve_usd).comparedTo(pairList[i].reserve_usd) < 0) {
                if (new BigNumber(pairList[i].reserve_usd).comparedTo(1000000000000000) < 0) {
                    if (new BigNumber(pairList[i].token0_price_usd).comparedTo(1000000) < 0 &&
                        new BigNumber(pairList[i].token1_price_usd).comparedTo(1000000) < 0) {
                        tokenPair = pairList[i];
                    }
                }
            }
        }
    }
    return tokenPair;
}

/**
 * 获取稳定币的usdt交易对
 * @param {Number} tokenId
 * @param {object} docs
 * @returns
 */
async function findStableUsdtPair(tokenId, docs) {
    const busd = await TokenModel().findOne({
        where: {
            address: formatAddress(constant.BUSD_ADDRESS)
        }
    });
    if (!busd) {
        return false;
    }
    const pairList = await PairModel().findAll({
        where: {
            swap_id: docs.swap_id,
            token_id: tokenId,
            [Op.or]: [
                {token1_id: busd.id},
                {token0_id: busd.id},
            ]
        }
    });

    if (pairList.length == 0) {
        return false;
    }

    if (pairList.length == 1) {
        return pairList[0];
    }

    //获取最大值
    let max = pairList.reduce((max, current) => {
        return comparePairDir(max, current);
    }, pairList[0]);

    return max;
}

/**
 *
 * @param { PairModel } pair0
 * @param { PairModel } pair1
 * @returns PairModel
 */
function comparePairDir(pair0, pair1) {
    const txVolume1 = new BigNumber(pair1.tx_volume);
    const txVolume0 = new BigNumber(pair0.tx_volume);

    // 当两个 tx_volume 都为 0 时，比较 tx_count 和 reserve_usd
    if (txVolume1.isEqualTo(0) && txVolume0.isEqualTo(0)) {
        if (pair0.tx_count === pair1.tx_count) {
            return new BigNumber(pair0.reserve_usd).isGreaterThan(new BigNumber(pair1.reserve_usd)) ? pair0 : pair1;
        }
        return pair0.tx_count > pair1.tx_count ? pair0 : pair1;
    }

    // 正常情况下比较 tx_volume
    return txVolume0.isGreaterThan(txVolume1) ? pair0 : pair1;
}

/**
 *
 * @param { PairModel } pair0
 * @param { PairModel } pair1
 * @returns { object} data
 * @returns { PairModel} data.pair
 * @returns { Number} data.token_index
 */
function compareMaxReservePair(pair0, pair1) {
    const check0 = {pair: pair0, token_index: 1};
    const check1 = {pair: pair1, token_index: 0};
    if (pair0 && pair1) {
        const maxPair = comparePairDir(pair0, pair1);
        return pair0.id === maxPair.id ? check0 : check1;
    }
    return pair0 ? check0 : check1;
}

/**
 * 获取pair中的token价格
 * @param { String } pairAddress
 * @param { Number } tokenId
 * @returns { String }
 */
async function getPairTokenAPrice(pairAddress, tokenId) {
    const pair = await PairModel().findOne({
        where: {
            address: pairAddress
        }
    });
    return pair.token0_id == tokenId ? pair.token0_price_usd : pair.token1_price_usd;
}

/**
 *
 * @param { String | int} pair  交易对地址或者ID
 * @returns
 */
async function getPairTxCountByPairId(pairId) {
    if (!address) return false;
    const exists = await redis.hExists(constant.PAIR_TX_COUNT_CACHE_KEY, pairId);
    let row = false;
    if (exists) {
        row = await redis.hGet(constant.PAIR_TX_COUNT_CACHE_KEY, pairId).catch(e => {
            console.error(constant.PAIR_TX_COUNT_CACHE_KEY + "获取失败", e.message);
        });
        if (row) row = JSON.parse(row);
    } else {
        let pair = await PairModel().findOne({
            where: {id: pairId},
            attributes: ['id', 'tx_count', 'tx_volume', 'tx_volume_usd'],
        });
        if (pair) {
            await redis.hSet(constant.PAIR_TX_COUNT_CACHE_KEY, pairId, pair).catch(e => {
                console.error(constant.PAIR_TX_COUNT_CACHE_KEY + "设置失败", e.message);
            });
        }
    }
    return row;
}

module.exports = {
    getCachePairByAddress,
    getCachePairById,
    savePair,
    savePairList,
    getTokenMaxReservePair
}
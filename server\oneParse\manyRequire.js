const { ethers } = require('ethers');
// const provider = new ethers.providers.Web3Provider(web3.currentProvider); // 假设你有一个web3提供器
const {queryProvider} = require('../avi/factory_provider');
const provider = queryProvider();
const { Provider, setMulticallAddress } = require('ethers-multicall');
setMulticallAddress(56,"******************************************");
const multicallProvider = new Provider(provider);
const { parseLogAbi, factoryAbi, allAbi,V3AllAbi } = require("../abi");
async function batchCallContracts(contracts, functionsAndArgs) {
    const contractInstances = contracts.map(address => new ethers.Contract(address, factoryAbi, provider));
    let calls = [];
    for (let i = 0; i < contractInstances.length; i++) {
        const contract = contractInstances[i];
        const functionName = functionsAndArgs[i][0];
        const args = functionsAndArgs[i][1];

        const call = {
              reference: 'call'+i,
              contract: contract,
              method: functionName,
              params: args
            };
        calls.push(call);
    }
    let results = await multicallProvider.all(calls);
    console.log(results);
    // 如果需要发送事务，则等待它们被挖掘
    // if (transactions.length > 0) {
    //     const txPromises = transactions.map(tx => tx.wait());
    //     await Promise.all(txPromises);
    // }
}

// 使用示例
const contracts = ["******************************************"];
const functionsAndArgs = [
    ['allPairs', [0]]
];

batchCallContracts(contracts, functionsAndArgs)
    .then(() => console.log('Batch calls completed'))
    .catch(error => console.error('Error during batch calls:', error));
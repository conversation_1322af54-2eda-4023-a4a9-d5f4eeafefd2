const {Contract,JsonRpc<PERSON>rovider} = require('ethers')
const marketAbi = require("../abi/market.json")
const netbodyAbi = require("../abi/netbody.json")
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
const {RPC_NEW} = require("../utils/constant");
const provider = new JsonRpcProvider(RPC_NEW)
const marketAddress = "******************************************"
const netbodyAddresss = "******************************************"
let addressArray = ["******************************************"];
const resultsList = [];
let operateNum = 0;
let vipNum = 0

async function main(netbody, market, address) {
    let result = await netbody.getNetBodyInfo(address)
    let list = result[2];
    list.forEach(async item => {
        let res = await market.identity(item);
        if (res[0] == 2 || res[0] == 1) {
            let infos = {
                address: item,
                supperAddress: address,
                identity: res[0],
                startTime:res[3]
            }

            //如果数据重复了1
            // const newResultsList = Array.from(
            //     resultsList.reduce((map, item) => {
            //         if (map.has(item.address)) {
            //             // 如果 address 已存在，修改 supperAddress
            //             console.log("发现重复地址：",item.address)
            //             const existingItem = map.get(item.address);
            //             existingItem.supperAddress = null; // 去链上查询
            //         } else {
            //             // 如果 address 不存在，直接存入
            //             map.set(item.address, item);
            //         }
            //         return map;
            //     }, new Map()).values()s
            // );
            // console.log("还在执行---", item)
            resultsList.push(infos);
            if (res[0] == 1) {
                const numRes = await market.opeInviteAmount(item);
                // console.log("查询赠送运营商返回结构", numRes);
                //除以四取整
                operateNum += parseInt(parseInt(numRes) / 4);
            } else {
                const numRes = await market.vipInviteAmount(item);
                // console.log("查询赠送VIP返回结构", numRes);
                //除以四取整
                vipNum += parseInt(parseInt(numRes) / 4);

            }
        }
        await main(netbody, market, item)
    })
}

async function getMemberList() {
    console.log("开始执行解析会员网体")
    operateNum=0;
    vipNum=0;
    const netbody = new Contract(netbodyAddresss, netbodyAbi, provider)
    const market = new Contract(marketAddress, marketAbi, provider)
    // 直接上父级地址
    await main(netbody, market, "******************************************");
    setTimeout(() => {
        try {
            let allStr = JSONbig.stringify(resultsList)
            redis.set("member_identity_list", allStr);
            resultsList.length = 0;
            redis.set("member_identity_operate_num", operateNum);
            redis.set("member_identity_vip_num", vipNum);
            console.log("存储结束")
        } catch (e) {
            console.error("加载会员网体报错:", e);
        }
    }, 15000)
}

//  getMemberList()
module.exports = {
    getMemberList
}
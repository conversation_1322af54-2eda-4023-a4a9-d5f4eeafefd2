const kafka = require('kafka-node');

class KafkaService {
  constructor(kafkaHost, topic) {
    this.kafkaHost = kafkaHost;
    this.topic = topic;
    this.client = new kafka.KafkaClient({ kafkaHost: this.kafkaHost });
    this.consumer = this.createConsumer();
  }

  createConsumer() {
    const topics = [{ topic: this.topic }];
    const options = {
      autoCommit: true,
      fetchMaxWaitMs: 1000,
      fetchMaxBytes: 1073741824,
      encoding: 'utf8',
      fromOffset: false
    };
    const consumer = new kafka.Consumer(this.client, topics, options);

    consumer.on('message', (message) => this.onMessage(message));
    consumer.on('error', (error) => this.onError(error));

    return consumer;
  }

  onMessage(message) {
    console.log('Received Message:', message);
    // 这里可以添加更多的逻辑来处理消息
  }

  onError(error) {
    console.error('Consumer Error:', error);
  }

  // 根据需要可以添加其他方法...
}

module.exports = KafkaService;



const redis = require("../database/redis");
const {formatAddress} = require("../utils/functions");
const {formatTimestampUTC} = require("../utils/DateUtils")
const token_key = "token_json";
const key = "token_check_json";

async function batchCallContracts() {
    while(true){
        try {
            let address = await redis.lPop(key);
            if(!address){
                return;
            }
            //await redis.lPush(key,address);

            let token = await redis.hGet(token_key,address);

            let obj_time0;
            try{
                obj_time0=JSON.parse(token);
            }catch(e){
                obj_time0={
                    address:address,
                    block_time:token,
                    created_block:0
                }
                await redis.hSet(token_key, address,JSON.stringify(obj_time0));//获取一个token地址的创建时间
                continue;
            }
            try {
                if(isNaN(obj_time0.block_time)){
                    continue;
                }
                console.log("遇到了",address)
                console.log(obj_time0.block_time)
                obj_time0={
                    address:address,
                    block_time:formatTimestampUTC( obj_time0.block_time),
                    created_block:0
                }
                await redis.hSet(token_key, address,JSON.stringify(obj_time0));//获取一个token地址的创建时间
            } catch (error) {
                continue;
            }
        } catch (error) {
            console.log(error);
        }
    }
    
}

async function start(){
    for (let index = 0; index < 100; index++) {
        new Promise(async (resolves, rejects) => {
            batchCallContracts();
        });
        
    }
    
}

start();
const { Interface } = require("ethers");
const constant = require("./constant");
const dayjs = require("dayjs");
const { default: BigNumber } = require("bignumber.js");
const PeriodEnum = require("./enums/PeriodEnum");

/**
 * 判断交易的方法
 * @param {string} data 交易哈希
 * @param {Interface} iface abi数据
 * @param {string} functionName 方法名
 * @returns 
 */
function isTransactionFunction(data,iface,functionName){
    if(data == null || data == "" || data == undefined) return false;
    const fn = iface.getFunction(functionName);
    if(!fn) return false;
    return data.indexOf(fn.selector) !== -1;
}

/**
 * 统一地址
 * @param {*} address 
 * @returns 
 */
const formatAddress = (address) => address ? address.toLocaleLowerCase() : '';

//比较两个地址是否一样
const sameAddress = (address1, address2) => Number(address1) === Number(address2);

/**
 * 排序地址
 * @param {*} arr 
 * @returns 
 */
const sortAddress = (arr) => arr.sort((a,b) => Number(a) - Number(b));

/**
 * 延时
 * @param {*} ms 
 * @returns 
 */
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 是否BUSD
 * @param {*} address 
 * @returns 
 */
const isBusd = (address) => sameAddress(address,constant.BUSD_ADDRESS);


/**
 * 是否USDT
 * @param {*} address 
 * @returns 
 */
const isUsdt = (address) => sameAddress(address,constant.USDT_ADDRESS);

/**
 * 是否USDC
 * @param {*} address
 * @returns
 */
const isUsdc = (address) => sameAddress(address,constant.USDT_ADDRESS);

/**
 * 是否BNB
 * @param {*} address
 * @returns
 */
const isBnb = (address) => sameAddress(address,constant.BNB_ADDRESS);



/**
 * 格式化数量
 * @param { BigNumber | String } quantity 
 * @param { Number } integer 
 * @param { Number } decimal 
 * @returns 
 */
const formatQuantity = (quantity,integer = 80,decimal = 20) => {
    if(quantity instanceof BigNumber){
        quantity = quantity.toFixed(decimal);
    }else{
        quantity = new BigNumber(quantity).toFixed(decimal);
    }
    return quantity.padStart(integer + decimal + 1,"0");
}; 
/**
 * 获取k线时间
 * @param {*} timestamp 
 * @param {*} period 
 * @returns 
 */
function getKlineTime(timestamp,period){
    let dayStartTime = dayjs.unix(timestamp).startOf('day').unix();
    switch(period){
        case PeriodEnum.MINUTE_1: 
            return dayjs(dayjs.unix(timestamp).format("YYYY-MM-DD HH:mm")).unix();
        case PeriodEnum.MINUTE_5:
            dayStartTime += parseInt((timestamp - dayStartTime)/300) * 300;
            return dayjs(dayjs.unix(dayStartTime).format("YYYY-MM-DD HH:mm")).unix();
        case PeriodEnum.MINUTE_15:
            dayStartTime += parseInt((timestamp - dayStartTime)/900) * 900;
            return dayjs(dayjs.unix(dayStartTime).format("YYYY-MM-DD HH:mm")).unix();
        case PeriodEnum.MINUTE_30:
            dayStartTime += parseInt((timestamp - dayStartTime)/1800) * 1800;
            return dayjs(dayjs.unix(dayStartTime).format("YYYY-MM-DD HH:mm")).unix();
        case PeriodEnum.MINUTE_60:
            dayStartTime += parseInt((timestamp - dayStartTime)/3600) * 3600;
            return dayjs(dayjs.unix(dayStartTime).format("YYYY-MM-DD HH")).unix();
        case PeriodEnum.MINUTE_120:
            dayStartTime += parseInt((timestamp - dayStartTime)/7200) * 7200;
            return dayjs(dayjs.unix(dayStartTime).format("YYYY-MM-DD HH")).unix();
        case PeriodEnum.MINUTE_240:
            dayStartTime += parseInt((timestamp - dayStartTime)/14400) * 14400;
            return dayjs(dayjs.unix(dayStartTime).format("YYYY-MM-DD HH")).unix();
        case PeriodEnum.DAY:
            return dayjs.unix(timestamp).startOf('day').unix();
        case PeriodEnum.WEEK:
            return dayjs.unix(timestamp).startOf('week').unix();
    }
    return false;
}
/**
 * 
 * @param { PairModel } pair 
 * @returns { Number }
 */
function getCurrentPriceUsd(pair){return pair.token_index == 0 ? pair.token0_price_usd : pair.token1_price_usd;}

/**
 * 获取交易对lp价值
 * @param {*} pair 
 * @returns 
 */
function getPairReserveUsd(pair){
    const reserve0_usd = new BigNumber(pair.reserve0).multipliedBy(pair.token0_price_usd);
    const reserve1_usd = new BigNumber(pair.reserve1).multipliedBy(pair.token1_price_usd)
    return reserve0_usd.plus(reserve1_usd);
}

// 是否稳定币
function checkStableCoins(tokens, token) {
    for (const tokenRow of tokens) {
        if(sameAddress(tokenRow.address, token)){
            return tokenRow;
        }
    }
    return false;
}

// 获取当天开始的时间戳
function getStartOfDayTimestamp(time) {
    const date = new Date(time * 1000);
    date.setHours(0, 0, 0, 0);
    return Math.floor(date.getTime() / 1000);
}


/**
 * 获取数量分隔数据
 * @param { string } quantity
 * @returns { { quantity_40th: string, quantity: string } } 
 */
const formatQuantityTh = (quantity) => {
    const format = { quantity: '0', quantity_40th: '0' };
    if (!quantity) return format;

    const fullQuantity = new BigNumber(quantity).toFixed(20);
    if (fullQuantity.length > 61) {
        const splitIndex = fullQuantity.length - 61;
        format.quantity_40th = fullQuantity.substring(0, splitIndex);
        format.quantity = fullQuantity.substring(splitIndex);
    } else {
        format.quantity = fullQuantity;
    }
    return format;
};


module.exports = { 
    isTransactionFunction,
    sameAddress, 
    isUsdt,
    isBusd,
    isUsdc,
    isBnb,
    formatAddress,
    delay, 
    sortAddress,
    getKlineTime,
    checkStableCoins,
    getPairReserveUsd,
    formatQuantity,
    getCurrentPriceUsd,
    getStartOfDayTimestamp,
    formatQuantityTh,
};
const { isAddress, Contract, formatUnits } = require("ethers");
const { provider } = require('../avi/factory_provider');
const { default: BigNumber } = require("bignumber.js");
const { tokenAbi } = require("../abi");
const AddressTypeEnum = require("./enums/AddressTypeEnum");
const TransUserModel = require("../model/mysql/TransUserModel");
const TokenModel = require("../model/mysql/TokenModel");
const { formatQuantityTh } = require("./functions");

/**
 * 是否是合约地址
 * @param {*} address 
 * @returns 
 */
const isContractAddress = async (address) => {
    // 验证地址是否有效
    if (!isAddress(address)) return false;
    // 获取合约代码
    const code = await provider.getCode(address);
    return !!Number(code);
}

const isPledgeZeroAddress = (address)=>{
    return /^[0x]*$/.test(address);
}
/**
 *  获取余额 
 * @param { String } address 合约地址
 * @param { String } walletAddress 地址
 * @param { Number } decimals 精度
 * @returns 
 */
const getBalance = async (address,walletAddress,decimals) => {
    try {
        let contract = new Contract(address,tokenAbi,provider);
        let balance = await contract.balanceOf(walletAddress);
        return formatUnits(balance,decimals);
    } catch (error) {
        console.error("获取余额失败",error);
        return 0;
    }
};

/**
 * 获取最大供应量
 * @param { String } address 代币地址
 * @param { Number } decimals 精度
 * @param { String } totalSupply 发行总量
 * @returns { BigNumber }
 */
const getMaxSupply = async (address,decimals,totalSupply) => {
    let maxSupply = new BigNumber(0);
    try {
        const zero0Address = "******************************************";//黑洞地址0
        const zero1Address = "******************************************";//黑洞地址1

        let token = null;
        const getBalanceOf = async (zeroAddress) => {
            const zeroUser = await TransUserModel().findOne({
                where: {
                    address: zeroAddress,
                }
            });
            if(!zeroUser){
                const contract = new Contract(address,tokenAbi,provider);
                let balance = await contract.balanceOf(zeroAddress);
                balance = formatUnits(balance,decimals);
                let proportion = new BigNumber(balance).div(totalSupply);
                if(!token){
                    token = await TokenModel().findOne({
                        where: {
                            address:address,
                        }
                    });
                    if(!token) return balance;
                }

                let quantity = formatQuantityTh(balance);
                await TransUserModel().create({
                    current: new BigNumber(balance).toFixed(20),
                    proportion: proportion.toFixed(20),
                    token_id: token.id,
                    address: zeroAddress,
                    type: AddressTypeEnum.ZERO,
                    quantity_40th: quantity.quantity_40th,
                    quantity: quantity.quantity,
                    publish_time: (new Date().getTime() / 1000),
                });
                return balance;
            }
            return zeroUser.current;
        }
   
        const zero0Balance = await getBalanceOf(zero0Address);
        const zero1Balance = await getBalanceOf(zero1Address);
        maxSupply = new BigNumber(totalSupply).minus(zero0Balance).minus(zero1Balance);
    } catch (error) {
        console.error("获取最大供应量失败",error);
    }
    return maxSupply;
}

/**
 * 获取地址类型
 * @param { String } address 
 * @returns { AddressTypeEnum } 
 */
async function getAddressType(address){
    if(isZeroAddress(address))
    return AddressTypeEnum.ZERO;
    const isContract = await isContractAddress(address);
    if(isContract)
    return AddressTypeEnum.CONTRACT;
    return AddressTypeEnum.WALLET;
}

/**
 * 是否是黑洞地址
 * @param { String } address 
 * @returns { boolean }
 */
async function isZeroAddress(address){
    // 验证地址是否有效
    if (!isAddress(address)) return false;
    return Number(address) == Number("******************************************") || 
    Number(address) == Number("******************************************");
}


module.exports = { 
    isContractAddress,
    getMaxSupply,
    getBalance,
    getAddressType,
    isZeroAddress,
    isPledgeZeroAddress
};
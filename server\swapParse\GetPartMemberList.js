const {Contract} = require('ethers')
const {ethers} = require('ethers')
const marketAbi = require("../abi/market.json")
const netbodyAbi = require("../abi/netbody.json")
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../database/redis");
const {userlist} = require("./userList");
const {mannalist} = require("./mannaList");
const dayjs = require("dayjs");
const rpc_listen = "https://bsc.blockpi.network/v1/rpc/156f516494b3a4024171a8045490d2d622e3a3b8"
const provider = new ethers.JsonRpcProvider(rpc_listen)
const marketAddress = "******************************************"
const netbodyAddresss = "******************************************"
const resultsList = [];
const sectionAddress = [
    "******************************************",
    "******************************************",
    "******************************************",
]

const netbody = new Contract(netbodyAddresss, netbodyAbi, provider)
const market = new Contract(marketAddress, marketAbi, provider)
async function getMemberList() {
    sectionAddress.forEach(async item => {
        let res = await market.identity(item);
        if (res[0] == 1) {
            let strTime;
            if(res[2]>0){
                strTime = res[2];
            }
            else {
                strTime = res[3];
            }
            let infos = {
                address: item,
                identity: res[0],
                strTime:strTime
            }
            // resultsList.push(`${item},`);
            resultsList.push(infos);
        }
    })
    setTimeout(() => {
        try {
            getMemberOperatorList(resultsList);
            // let allStr = JSONbig.stringify(resultsList)
            // redis.set("member_section_identity_list", allStr);
            // console.log("存储结束", JSONbig.stringify(resultsList))
            // resultsList.length = 0;
        } catch (e) {
            console.error("加载会员网体报错:", e);
        }
    }, 3000)
}

async function formatTime(date, template) {
    return dayjs(date).format(template);
}
async function getMemberOperatorList(memberList) {

    for (let i = 0; i <memberList.length; i++) {
        let item = memberList[i];
        // let superAllAddress = await getSuperAdress(item.address);
        // //如果查询结果不止一层上级的话
        // if(superAllAddress.indexOf("<")>0){
        //     let listSuperAddress = superAllAddress.split('<');
        //     item.directSupperAddress = listSuperAddress[0];
        //     item.supperAllAddress = listSuperAddress[listSuperAddress.length-2];
        // }
        // else {
        //     item.supperAllAddress = "NULL"
        //     item.directSupperAddress= "NULL";
        // }
        // superAllAddress = item.address+'<'+superAllAddress;
        // let date = await formatTime(parseInt(item.strTime)*1000,'YY-MM-DD HH:mm')
        // console.log(`${i},${item.address},${item.supperAllAddress},${item.directSupperAddress},${date},${superAllAddress}`)
    }
    return;
}
async function getSuperAdress(address){
    // console.log("开始调用",address)
    let result = await netbody.getNetBodyInfo(address)
    // console.log("查询结果",result)
    let superAddress = result[1];
    if(!superAddress || superAddress.toLowerCase() =='0x0000000000000000000000000000000000000000' || superAddress.toLowerCase() ==netbodyAddresss.toLowerCase() || superAddress.toLowerCase() ==marketAddress.toLowerCase()){
        return;
    }
    else {
        let newSuperAddress = await getSuperAdress(superAddress);
        if(newSuperAddress){
            return `${superAddress}<${newSuperAddress}`;

        }
        else {

            return superAddress;
        }
    }
}
getMemberList();
// getSectionList()

const { ethers } = require('ethers');
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis_in");

const { delay, isUsdt, isBusd, isBnb, formatAddress } = require("../utils/functions");
const { } = require("../utils")
const key = "pair_json_check";
const token_key = "token_address_data";
async function batchCallContracts() {
    console.log("开始时间：", Date.now())
    while (true) {
        try {
            //console.log(number);

            let pair_address = await redis.lPop(key);
            //await redis.lPush(key,pair_address);
            if(!pair_address){
                return;
            }

            let flag_now = await redis.hGet("pair_json_hdata", formatAddress(pair_address));
            if (!flag_now) {
                console.log("不存在");
                continue;
            }
            
            flag_now = JSONbig.parse(flag_now);
            let token0 = flag_now.token0_addr;
            let token1 = flag_now.token1_addr;
            let token0_time = await redis.hGet("token_json", token0);
            let token1_time = await redis.hGet("token_json", token1);

            let token_index = 0;
            if (isUsdt(token0)) {
                token_addr = token1;
                token_index = 1;
                let pair = {
                    address: formatAddress(pair_address),
                    create_time: '',
                    symbol: flag_now.symbol,
                    token0_addr: token0,
                    token1_addr: token1,
                    token_addr: token_addr,
                    token_index: token_index,
                    decimals: flag_now.decimals,
                    total_supply: flag_now.total_supply,
                    reserve0: flag_now.reserve0,
                    reserve1: flag_now.reserve1,
                }
                await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
            } else if (isBusd(token0)) {
                if (isUsdt(token1)) {
                    token_addr = token0;
                    token_index = 0;
                    let pair = {
                        address: formatAddress(pair_address),
                        create_time: '',
                        symbol: flag_now.symbol,
                        token0_addr: token0,
                        token1_addr: token1,
                        token_addr: token_addr,
                        token_index: token_index,
                        decimals: flag_now.decimals,
                        total_supply: flag_now.total_supply,
                        reserve0: flag_now.reserve0,
                        reserve1: flag_now.reserve1,
                    }
                    await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                    await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
                } else {
                    token_addr = token1;
                    token_index = 1;
                    let pair = {
                        address: formatAddress(pair_address),
                        create_time: '',
                        symbol: flag_now.symbol,
                        token0_addr: token0,
                        token1_addr: token1,
                        token_addr: token_addr,
                        token_index: token_index,
                        decimals: flag_now.decimals,
                        total_supply: flag_now.total_supply,
                        reserve0: flag_now.reserve0,
                        reserve1: flag_now.reserve1,
                    }
                    await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                    await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
                }
            } else if (isBnb(token0)) {
                if (isBusd(token1) || isUsdt(token1)) {
                    token_addr = token0;
                    token_index = 0;
                    let pair = {
                        address: formatAddress(pair_address),
                        create_time: '',
                        symbol: flag_now.symbol,
                        token0_addr: token0,
                        token1_addr: token1,
                        token_addr: token_addr,
                        token_index: token_index,
                        decimals: flag_now.decimals,
                        total_supply: flag_now.total_supply,
                        reserve0: flag_now.reserve0,
                        reserve1: flag_now.reserve1,
                    }
                    await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                    await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
                } else {
                    token_addr = token1;
                    token_index = 1;
                    let pair = {
                        address: formatAddress(pair_address),
                        create_time: '',
                        symbol: flag_now.symbol,
                        token0_addr: token0,
                        token1_addr: token1,
                        token_addr: token_addr,
                        token_index: token_index,
                        decimals: flag_now.decimals,
                        total_supply: flag_now.total_supply,
                        reserve0: flag_now.reserve0,
                        reserve1: flag_now.reserve1,
                    }
                    await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                    await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
                }
            } else if (isBusd(token1) || isUsdt(token1) || isBnb(token1)) {
                token_addr = token0;
                token_index = 0;
                let pair = {
                    address: formatAddress(pair_address),
                    create_time: '',
                    symbol: flag_now.symbol,
                    token0_addr: token0,
                    token1_addr: token1,
                    token_addr: token_addr,
                    token_index: token_index,
                    decimals: flag_now.decimals,
                    total_supply: flag_now.total_supply,
                    reserve0: flag_now.reserve0,
                    reserve1: flag_now.reserve1,
                }
                await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
            } else {
                //console.log(token0_time,token1_time,token0_time < token1_time);
                // console.log("当前的是：",token0,token1,address)
                // console.log("当前结果：",token0_time == null || token1_time == null)
                if (token0_time == null || token1_time == null) {
                    //console.log("是否进来了");
                    if (token0_time == null) {
                        await redis.rPush(token_key, token0);
                    }
                    if (token1_time == null) {
                        await redis.rPush(token_key, token1);
                    }
                    await redis.rPush(key, address);

                } else {
                    let obj_time0;
                    try {
                        obj_time0 = JSON.parse(token0_time);
                    } catch (e) {
                        obj_time0 = {
                            address: token0,
                            block_time: token0_time,
                            created_block: 0
                        }
                        //await redis.hSet(FirstAnalyzeEnum.token_json, key,JSON.stringify(obj));//获取一个token地址的创建时间
                    }

                    let obj_time1;
                    try {
                        obj_time1 = JSON.parse(token1_time);
                    } catch (e) {
                        obj_time1 = {
                            address: token1,
                            block_time: token1_time,
                            created_block: 0
                        }
                        //await redis.hSet(FirstAnalyzeEnum.token_json, key,JSON.stringify(obj));//获取一个token地址的创建时间
                    }

                    if (new Date(obj_time0['block_time']).getTime() <= new Date(obj_time1['block_time']).getTime()) {
                        token_addr = token1;
                        token_index = 1;
                        let pair = {
                            address: formatAddress(pair_address),
                            create_time: '',
                            symbol: flag_now.symbol,
                            token0_addr: token0,
                            token1_addr: token1,
                            token_addr: token_addr,
                            token_index: token_index,
                            decimals: flag_now.decimals,
                            total_supply: flag_now.total_supply,
                            reserve0: flag_now.reserve0,
                            reserve1: flag_now.reserve1,
                        }
                        await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                        await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
                    } else if (new Date(obj_time0['block_time']).getTime() > new Date(obj_time1['block_time']).getTime()) {
                        token_addr = token0;
                        token_index = 0;
                        let pair = {
                            address: formatAddress(pair_address),
                            create_time: '',
                            symbol: flag_now.symbol,
                            token0_addr: token0,
                            token1_addr: token1,
                            token_addr: token_addr,
                            token_index: token_index,
                            decimals: flag_now.decimals,
                            total_supply: flag_now.total_supply,
                            reserve0: flag_now.reserve0,
                            reserve1: flag_now.reserve1,
                        }
                        await redis.hSet("pair_json_hdata", formatAddress(pair_address),JSONbig.stringify(pair));
                        await redis.rPush("pair_json_consumer", JSONbig.stringify(pair));
                    } else {
                        await redis.rPush(token_key, token0);
                        await redis.rPush(token_key, token1);
                        await redis.rPush(key, address);
                    }
                }


            }
            console.log("完成");
        } catch (error) {

        }
    }
}
async function start() {
    for (let index = 0; index < 10; index++) {
        new Promise(async (resolves, rejects) => {
            batchCallContracts();
        });
    }
}
start();
const {default: BigNumber} = require("bignumber.js");
BigNumber.config({DECIMAL_PLACES: 100});
const JSONbig = require('json-bigint')({storeAsString: true});
const redis = require("../../database/redis");

const {getMemberList} = require("../../swapParse/testGetMemberList");
const {PqueryQL_sonar, HQL_sonar} = require("../../database/PqueryQL/PostgresSQL");

//追踪详情
async function verifyMember(request, result) {
    let params = request.body

    if(!params.list || params.list.length==0 ){
        result.json({
            code: 500,
            msg: '内容为空',
            data: null
        })
    }

    let addressList  = '';
    for (let i = 0; i < params.list.length; i++) {
        let address = params.list[i];
        if(address.indexOf('0x')==0){
            address = address.replace("0x", "");
        }
        if(i==params.list.length-1){
            addressList+=`'${address}'`
        }
        else {
            addressList+=`'${address}',`

        }
    }
    console.log("查询地址列表字符串",addressList)
    let arg = `
        SELECT * FROM buy_identity WHERE 
        address in (${addressList})
    `
    let data = await PqueryQL_sonar(arg);
    if(data && data.rows.length>0){
        result.json({
            code: 200,
            data: data.rows
        })
    }
    else {
        result.json({
            code: 500,
            msg: '内容为空',
            data: null
        })
    }
}
//追踪详情
async function getChinaMemberInfo(request, result) {
    let params = request.body

    let json = await redis.get("member_identity_list");
    if(json){
        let operateNum = await redis.get("member_identity_operate_num");
        let vipNum = await redis.get("member_identity_vip_num");
        let jsonList= JSONbig.parse(json);
        let data = {
            list:buildTreeWithStats(jsonList,params.address),
            operateNum:operateNum,
            vipNum:vipNum,
        }
        result.json({
            code: 200,
            data: data
        })
    }
    else {
        getMemberList()
        result.json({
            code: 500,
            msg: '内容为空',
            data: null
        })
    }
}
function buildTreeWithStats(data, targetAddress = null) {
    // 构建一个字典方便快速查找节点
    const map = new Map();
    data.forEach(item => {
        // 将地址转换为小写，避免大小写不一致问题
        const addressLower = item.address.toLowerCase();
        const supperAddressLower = item.supperAddress ? item.supperAddress.toLowerCase() : null;

        map.set(addressLower, {
            ...item,
            address: addressLower,  // 将原地址替换为小写地址
            supperAddress: supperAddressLower, // 将上级地址替换为小写地址
            children: [],
            identityOperatorCount: 0,
            identityVipCount: 0
        });
    });

    // 构造树
    const tree = [];
    data.forEach(item => {
        const addressLower = item.address.toLowerCase();
        const supperAddressLower = item.supperAddress ? item.supperAddress.toLowerCase() : null;

        // 处理子节点和父节点的关系，确保比较时地址是小写的
        if (map.has(supperAddressLower)) {
            map.get(supperAddressLower).children.push(map.get(addressLower));
        } else {
            tree.push(map.get(addressLower));
        }
    });

    // 递归统计子节点的 identity 总数（不包含自己）
    function calculateStats(node) {
        let identityOperatorCount = 0;
        let identityVipCount = 0;

        // 遍历子节点并累加统计数
        for (const child of node.children) {
            const childStats = calculateStats(child);
            identityOperatorCount += childStats.identityOperatorCount; // 子节点的 identity 统计
            identityVipCount += childStats.identityVipCount;
        }

        // 加上直接子节点的 identity 值
        identityOperatorCount += node.children.filter(child => child.identity === 1).length;
        identityVipCount += node.children.filter(child => child.identity === 2).length;

        // 更新当前节点的统计数
        node.identityOperatorCount = identityOperatorCount;
        node.identityVipCount = identityVipCount;

        return { identityOperatorCount, identityVipCount };
    }

    // 如果传了 targetAddress，则只从指定地址开始统计
    if (targetAddress) {
        const targetNode = map.get(targetAddress.toLowerCase()); // 同样传入的 targetAddress 也要转换为小写
        if (!targetNode) {
            throw new Error(`Address ${targetAddress} not found in the data`);
        }
        calculateStats(targetNode);
        return targetNode; // 返回单一节点的树
    }

    // 对每个根节点递归计算统计数
    tree.forEach(root => calculateStats(root));

    return tree; // 返回整个树
}


module.exports = {
    getChinaMemberInfo,
    verifyMember
}

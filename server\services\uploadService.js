/**
 * 描述: 业务逻辑处理 - 上传相关接口
 * 作者: <PERSON>
 * 日期: 2020-06-20
*/
  const fs = require("fs")
  const path = require("path");
  //const multer = require("multer");

//   let uploader = multer({
//     // storage: multer.diskStorage({
//     //     //设置文件存储位置
//     //     destination: function(req, file, cb) {
//     //         let date = new Date();
//     //         let year = date.getFullYear();
//     //         let month = (date.getMonth() + 1).toString().padStart(2, '0');
//     //         let day = date.getDate();
//     //         let dir = "./public/uploads/images/" + year + month + day;

//     //         //判断目录是否存在，没有则创建
//     //         if (!fs.existsSync(dir)) {
//     //             fs.mkdirSync(dir, {
//     //                 recursive: true
//     //             });
//     //         }

//     //         //dir就是上传文件存放的目录
//     //         cb(null, dir);
//     //     },
//     //     //设置文件名称
//     //     filename: function(req, file, cb) {            
//     //         // 解决中文名乱码的问题
//     //         file.originalname = Buffer.from(file.originalname, "latin1").toString(
//     //             "utf8"
//     //         );
//     //         let fileName = file.originalname
//     //         // 判断文件是否存在
//     //         fs.access(fileName, error => {
//     //             if (!error) {
//     //                 // The check succeeded
//     //                 //fileName就是上传文件的文件名
//     //                 cb(null, fileName);
//     //             } else {
//     //                 // The check failed
//     //                 let ext = path.extname(file.originalname);
//     //                 let basename = path.basename(file.originalname)
//     //                 let str = basename.split("."); // 以‘.’分割；
//     //                 fileName = str[0] + '-' + Date.now() + ext;
//     //                 //fileName就是上传文件的文件名
//     //                 cb(null, fileName);
//     //             }
//     //         });        
//     //     }
//     // })
// });



function upload(req,res){
    let file = req.file;
    res.json({
        code: 200,
        msg: "上传成功",
        data: file.path,
    })
}

module.exports = {
    //uploader:uploader,
    upload:upload,
}

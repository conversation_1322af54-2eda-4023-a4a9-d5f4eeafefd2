const redis = require("../database/redis_in");
// const kafka = require("../utils/kafka/kafkajs");
// const {slBaseSyncs} = require("../utils/doris/baseSyncsSL");
const { parseLogAbi, factoryAbi, allAbi,V3AllAbi } = require("../abi");
const { Interface, formatUnits, Contract } = require("ethers");
const { sortAddress, formatAddress, delay } = require("../utils/functions");
const _ = require('lodash');
const { NumberToDate, formatTimestampUTC } = require("../utils/DateUtils");
const constant = require("../utils/constant");
const FirstAnalyzeEnum = require("../utils/enums/AnalyzeDorisKeyEnum");
const routers = [formatAddress(constant.PANCAKE_SMART_ROUTER), formatAddress(constant.PANCAKE_ROUTER)];

const oneKey = "initial_pair_token";
const oneKeyError = "initial_pair_token_error";

const tokenKey = "token_json_new";
const pairKey = FirstAnalyzeEnum.pair_json;

let listenBlockNullKey = "block-listen_null";//记录mongodb原始数据表没有查到的区块

async function parseOne() {
    try {
        let abi = new Interface(allAbi);
       // let abi_v3 = new Interface(V3AllAbi);
        while (true) {
            let blcok_no = "";
            let blockInfo = await redis.lPop(oneKey);
            //await redis.lPush(oneKey,blockInfo);

            if (!blockInfo) {
                continue;
            }
            let start = Date.now() / 1000;
            console.log("开始获取redis", start);
            blockInfo = JSON.parse(blockInfo);
            let promises = [];
            console.log("开始解析数据", blockInfo.length);
            for (let index = 0; index < blockInfo.length; index++) {

                const element = blockInfo[index];

                if (index == 0) {
                    blcok_no = element.block_number;
                }
                let promise = new Promise(async (resolve, reject) => {
                    try{
                        let trans_hash = {
                            hash: element.trans_hash,
                            from: element.address_from,
                            to: element.address_to,
                            block_no: element.block_number,
                            block_time: element.block_time,
                            logshashs: element.logs_hashs,
                            contract_address: element.contract_address,
                            trans_data: element.trans_data
                        }
                        //将单条hash进行解码
    
                        //开始解析数据
                        if (trans_hash.contract_address != 'null') {
                            //创建代币
                            //看是否需要走链上
                            let tokenObj={
                                    address:formatAddress(trans_hash.contract_address),
                                    block_time:trans_hash.block_time,
                                    created_block:trans_hash.block_no
                            }
                            let tokenJSON=JSON.stringify(tokenObj);
                            await redis.hSet(tokenKey, trans_hash.contract_address, tokenJSON);
                            await redis.rPush(FirstAnalyzeEnum.token_json_consumer, tokenJSON);
                            resolve();
                        }
                        resolve();

                    }catch(e){
                        console.log(e);

                        await redis.hSet(oneKeyError, blcok_no, blcok_no);

                        reject(e);
                    }
                });
                promises.push(promise);
            }
            try {
                const res_all = await Promise.allSettled(promises);
                // 处理结果
            } catch (error) {
                // 错误处理
                console.error('Promise 被拒绝:', error);
            }
        }
    } catch (error) {
        if (error.toString().includes('ER_LOCK_DEADLOCK')) {
            //此异常是相同冲突，无需理会
        } else {
            throw error;
        }
    }
    // });
}


parseOne();

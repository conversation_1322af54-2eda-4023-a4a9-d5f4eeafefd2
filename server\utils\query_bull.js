const Bull = require('bull');
const { connectQueue } = require('../database/config');

var query_names = [];

//创建新的任务队列
function createQuery(name_renwu){
    var query = connectQueue(name_renwu);
    //console.log("创建的新的队列为：",query)
    query_names.push({
        name:name_renwu,
        query: query,
    });
    return query;
}

//添加到任务队列
/**
 * 
 * @param { Bull } name_renwu 
 * @returns 
 */
async function initQuery(name_renwu){
    for(var i = 0;i<query_names.length;i++){
        if(name_renwu == query_names[i].name){
            return query_names[i].query;
        }
    }
    var query = connectQueue(name_renwu);
    console.log("创建的新的队列为："+name_renwu)
    query_names.push({
        name:name_renwu,
        query: query,
    });
    return query;
}

module.exports={
    initQuery,
    createQuery
}
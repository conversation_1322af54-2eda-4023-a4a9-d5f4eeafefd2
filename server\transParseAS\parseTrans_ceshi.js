
const JSONbig = require('json-bigint')({ storeAsString: true });
const redis = require("../database/redis_in");
const { getTokenAAndBPriceByToken } = require("../utils/klineUtils");
const TradeTypeEnum = require("../utils/enums/TradeTypeEnum");
const { dbTransferQuery } = require('../model/mysql/AviBaseTransferModel');
//const {slAveTrade} = require('../utils/doris/aveTradeSL')
const {slAveTransfer} = require('../utils/doris/aveTransferSL')
const {slAveTransferUser} = require('../utils/doris/aveTransferUserSL')
const key_all = "token_json_hdata";
const { formatUnits } = require("ethers");
const { default: BigNumber } = require('bignumber.js');
const mapLimit = require('async/mapLimit');
const { StringToTimeStamp, formatTimestamp } = require("../utils/DateUtils");
async function startSwaps() {
    try {

        let address = "******************************************";

        let token = await redis.hGet(key_all,address);

        console.log(token);
        let pair_address = "******************************************";

        let pair = await redis.hGet("pair_json_hdata",pair_address);

        console.log(pair);

    } catch (ex) {
        console.log(ex);
    }



}

startSwaps();

module.exports = {
    startSwaps
}
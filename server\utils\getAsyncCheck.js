
const mapLimit = require('async/mapLimit');

const {queryProvider} = require('../avi/factory_provider')

const provider = queryProvider();

async function finished(hash) {
  //console.log("当前hash", hash);
  let transaction = await provider.getTransaction(hash);
  let receipt = await provider.getTransactionReceipt(hash);
  if(transaction){
    //console.log("得到transaction数据");
  }

  if(receipt){
    //console.log("得到receipt数据");
  }

  let baseInfo = {
      logs: null,
      from: null,
      to: null,
      contractAddress: null,
      transData: null,
  };

  if (transaction) {
      baseInfo = {
          ...baseInfo,
          from: transaction.from,
          to: transaction.to,
          transData: transaction.input,
      };
  }

  if (receipt) {
    const logs = receipt.logs.map(log => ({
      ...log,
      blockNumber: Number(log.blockNumber),
      transactionIndex: Number(log.transactionIndex),
      logIndex: Number(log.logIndex),
    }));

    baseInfo = {
        ...baseInfo,
        logs: logs,
        from: receipt.from || baseInfo.from,
        to: receipt.to || baseInfo.to,
        contractAddress: receipt.contractAddress,
    };
  }

  return baseInfo;
}


async function getDocByBlock(model_res,block_no) {
  try {

      let logs_hashs = [];
      let address_from = [];
      let address_to = [];
      let contract_address = [];
      let trans_data = [];
      let trans_hashs = model_res.transactions;
      //await getAlltrans(trans_hashs,logs_hashs,address_from,address_to,contract_address,trans_data);

      await new Promise((resolve, reject) => {
        mapLimit(trans_hashs, 100, async (hashData) => {
          try {
            await new Promise(async (resolves, rejects) => {
              try {
                //console.log("当前hash",hashData)
                const item = await finished(hashData);
               // console.log("当前查询的hash：",hashData['hash'])
                if (item) {
                  logs_hashs.push(item.logs);
                  address_from.push(item.from);
                  address_to.push(item.to);
                  contract_address.push(item.contractAddress);
                  trans_data.push(item.transData);
                }
                resolves();
              }catch(error){
                rejects(error);
              }
            });
            //console.log("100已完成",logs_hashs.length);
          } catch (err) {
            console.error("Error processing transaction:", err);
          }
        }, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      const docs = {
        _id: block_no,
        block_no,
        block_time: model_res.timestamp,
        trans_hashs,
        logs_hashs,
        address_from,
        address_to,
        contract_address,
        trans_data,
        name_renwu: "test_logs",
      };
      console.log("docs结果",docs.trans_hashs.length);
      return docs;
  } catch (error) {
    console.error("Error in startPairs:", error);
  }
}

// async function getAlltrans(trans_hashs,logs_hashs,address_from,address_to,contract_address,trans_data){
//   mapLimit(trans_hashs, 100, async (hashData) => {
//     try {
//       //console.log("当前查询的hash：",hashData['hash'])
//       // const item = await finished(hashData);
//       // if (item) {
//       //   logs_hashs.push(item.logs);
//       //   address_from.push(item.from);
//       //   address_to.push(item.to);
//       //   contract_address.push(item.contractAddress);
//       //   trans_data.push(item.transData);
//       // }
//       await new Promise(async (resolves, rejects) => {
//         try {
//           //console.log("当前查询的hash：",hashData['hash'])
//           const item = await finished(hashData);
//           if (item) {
//             logs_hashs.push(item.logs);
//             address_from.push(item.from);
//             address_to.push(item.to);
//             contract_address.push(item.contractAddress);
//             trans_data.push(item.transData);
//           }
//           resolves();
//         }catch(error){
//           rejects(error);
//         }
//       });
//       //console.log("结果是：",hashData['hash']);
//       console.log("100已完成",logs_hashs.length);
//     }catch(error){
//       resolves();
//     }
//   });
// }

module.exports={
  getDocByBlock
}

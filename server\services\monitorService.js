const boom = require("boom");
const {
  monitorModel,
  monitorDateModel,
  monitorAddressModel,
} = require("../model/model");
const { querySql } = require("../database/config");
const { body, validationResult } = require("express-validator");
const { decode } = require("../utils/user-jwt");

const { ethers, getAddress } = require("ethers");
/**
 * 描述: 业务逻辑处理 - 监控相关接口
 */
/**
 * 监控类型
 */
async function cateList(req, res, next) {
  let { tag } = req.body;
  console.log(tag);
  if (tag == undefined || tag == null || tag == "") {
    tag = "home";
  }
  console.log(tag);
  const connect = await querySql();
  var monitor_model = monitorModel(connect);
  var date_array = await monitor_model.find({ parent: tag });
  console.log("监控类型：", date_array);
  res.json({
    code: 200,
    msg: "success",
    data: date_array,
  });
}

/**添加监控数据 */
async function addMonitorAddress(req, res, next) {
  const err = validationResult(req);
  // 如果验证错误，empty不为空
  if (!err.isEmpty()) {
    // 获取错误信息
    const [{ msg }] = err.errors;
    // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回
    next(boom.badRequest(msg));
  } else {
    var user = decode(req);
    console.log(user);
    let { address, chainname } = req.body;
    //判断一个地址是个人地址还是合约地址
    const connect = await querySql();
    var address_model = monitorAddressModel(connect);
    var date_res = await address_model.findOne({
      address: address,
      chainname: chainname,
      username: user.username,
    });
    var name = "asdic";
    var image = "http://*************:8088/image/image.jpg";
    if (date_res == null || date_res == undefined) {
      var docs = {
        name: name,
        image: image,
        address: address,
        chainname: chainname,
        username: user.username,
        type: "contract",
      };
      var all = await address_model.create(docs);
      console.log(all);
      const connect = await querySql();
      //查询出地址的全部监控类型，然后添加到监控数据表中
      var monitor_model = monitorModel(connect);
      var date_array = await monitor_model.find({ parent: "home" });
      for (var i = 0; i < date_array.length; i++) {
        var tag = date_array[i].tag;
        var date_array1 = await monitor_model.find({ parent: tag });
        for (var j = 0; j < date_array1.length; j++) {
          var tag1 = date_array1[j].tag;
          var docs1 = {
            tag: tag1,
            name: date_array1[j].name,
            price: 0,
            frequency: 0,
            max: 0,
            min: 0,
            address_id: all._id,
            parent: tag,
          };
          const connect = await querySql();
          var date_model = monitorDateModel(connect);
          var all1 = await date_model.create(docs1);
        }
      }
      res.json({
        code: 200,
        msg: "success",
        data: docs,
      });
    } else {
      date_res.username = user.username;
      var all = await address_model.updateOne({ _id: date_res._id }, date_res);
      res.json({
        code: 200,
        msg: "success",
        data: date_res,
      });
    }
  }
}

async function addMonitorUserAddress(req, res, next) {
  const err = validationResult(req);
  // 如果验证错误，empty不为空
  if (!err.isEmpty()) {
    // 获取错误信息
    const [{ msg }] = err.errors;
    // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回
    next(boom.badRequest(msg));
  } else {
    var user = decode(req);
    console.log(user);
    let { address, chainname, name } = req.body;
    const connect = await querySql();
    var address_model = monitorAddressModel(connect);
    var date_res = await address_model.findOne({
      address: address,
      chainname: chainname,
      username: user.username,
    });
    var image = "";
    if (date_res == null || date_res == undefined) {
      var docs = {
        name: name,
        image: image,
        address: address,
        chainname: chainname,
        username: user.username,
        type: "address",
      };
      var all = await address_model.create(docs);
      console.log(all);
      const connect = await querySql();
      //查询出地址的全部监控类型，然后添加到监控数据表中
      var monitor_model = monitorModel(connect);
      var date_model = monitorDateModel(connect);
      var date_array = await monitor_model.find({ parent: "address" });
      for (var i = 0; i < date_array.length; i++) {
        var tag = date_array[i].tag;
        var date_array1 = await monitor_model.find({ parent: tag });
        for (var j = 0; j < date_array1.length; j++) {
          var tag1 = date_array1[j].tag;
          var docs1 = {
            tag: tag1,
            name: date_array1[j].name,
            price: 0,
            frequency: 0,
            max: 0,
            min: 0,
            address_id: all._id,
            parent: tag,
          };

          var all1 = await date_model.create(docs1);
        }
      }
      res.json({
        code: 200,
        msg: "success",
        data: docs,
      });
    } else {
      date_res.username = user.username;
      var all = await address_model.updateOne({ _id: date_res._id }, date_res);
      res.json({
        code: 200,
        msg: "success",
        data: date_res,
      });
    }
  }
}

/**查询当前用户所有的监控地址 */
async function findAllMonitorAddress(req, res, next) {
  const err = validationResult(req);
  // 如果验证错误，empty不为空
  if (!err.isEmpty()) {
    // 获取错误信息
    const [{ msg }] = err.errors;
    // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回
    next(boom.badRequest(msg));
  } else {
    var user = decode(req);
    let { type } = req.body;
    const connect = await querySql();
    var address_model = monitorAddressModel(connect);
    var date_array = await address_model.find({
      username: user.username,
      type: type,
    });
    res.json({
      code: 200,
      msg: "success",
      data: date_array,
    });
  }
}

/**根据当前类型添加监控数据 */
async function addCate(req, res, next) {
  const err = validationResult(req);
  // 如果验证错误，empty不为空
  if (!err.isEmpty()) {
    // 获取错误信息
    const [{ msg }] = err.errors;
    // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回
    next(boom.badRequest(msg));
  } else {
    var user = decode(req);
    console.log(user);
    let { date } = req.body;
    const connect = await querySql();
    var monitor_model = monitorDateModel(connect);
    var flag = true;
    for (let index = 0; index < date.length; index++) {
      const element = date[index];
      var all = await monitor_model.updateOne({ _id: element._id }, element);
      if (all.ok != 1) {
        flag = false;
      }
    }
    if (flag) {
      res.json({
        code: 200,
        msg: "success",
        data: date,
      });
    } else {
      res.json({
        code: 500,
        msg: "error",
      });
    }

    // var monitor_model = monitorModel(connect);
    // var date_array = await monitor_model.findOne({ tag: tag });
    // if (date_array == null) {
    //   res.json({
    //     code: 500,
    //     msg: "当前类型错误",
    //   });
    // } else {
    //   var docs = {
    //     tag: tag,
    //     name: name,
    //     price: price,
    //     frequency: frequency,
    //     max: max,
    //     min: min,
    //     address_id:address_id
    //   };
    //   var date_model = monitorDateModel(connect);
    //   var date_res =await date_model.findOne({tag:tag,address_id:address_id})
    //   if(date_res ==null ||date_res == undefined){
    //     var all = await date_model.create(docs)
    //     res.json({
    //       code: 200,
    //       msg: "success",
    //       data: docs,
    //     });
    //   }else{
    //     date_res.price = price;
    //     date_res.frequency = frequency;
    //     date_res.max = max;
    //     date_res.min = min;
    //     var all = await date_model.updateOne({_id:date_res._id},date_res);
    //     res.json({
    //       code: 200,
    //       msg: "success",
    //       data: date_res,
    //     });
    //   }
    //}
  }
}

/**根据地址查询当前交易对的基础数据 */
async function findAddressInfo(req, res, next) {
  const err = validationResult(req);
  // 如果验证错误，empty不为空
  if (!err.isEmpty()) {
    // 获取错误信息
    const [{ msg }] = err.errors;
    // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回
    next(boom.badRequest(msg));
  } else {
    const connect = await querySql();
    var user = decode(req);
    let { _id } = req.body;
    var monitor_model = monitorDateModel(connect);
    var date_cake = await monitor_model.findOne({ _id: _id });
    var address_model = monitorAddressModel(connect);
    var date_address = await address_model.findOne({
      _id: date_cake.address_id,
    });
    //根据地址查询当前交易对的基础数据
    var docs = {
      price: "0",
      reserve_one: "0",
      reserve_two: "0",
      blockHash: "0",
      name_index: "0",
      chg_hours: "0",
      user_num: "0",
      tranc_hours: "0",
      circulation: "0",
      total_market: "0",
      liquidity: "0",
      change_hands: "0",
      tranc_all_hours: "0",
      safety: "0",
      reserve_lp: "0",
    };
    res.json({
      code: 200,
      msg: "success",
      data: docs,
    });
  }
}

/**查询当前用户所有的监控地址下的监控类型 */
async function findAllCate(req, res, next) {
  const err = validationResult(req);
  // 如果验证错误，empty不为空
  if (!err.isEmpty()) {
    // 获取错误信息
    const [{ msg }] = err.errors;
    // 抛出错误，交给我们自定义的统一异常处理程序进行错误返回
    next(boom.badRequest(msg));
  } else {
    const connect = await querySql();
    var user = decode(req);
    console.log(user);
    let { address_id } = req.body;
    var address_model = monitorAddressModel(connect);
    var date_address = await address_model.findOne({ _id: address_id });
    if (date_address.type == "contract") {
      var res_date = new Object();
      var monitor_model = monitorModel(connect);
      var date_array = await monitor_model.find({ parent: "home" });
      var monitor_model = monitorDateModel(connect);
      var date_monitor = await monitor_model.find({ address_id: address_id });
      for (var i = 0; i < date_array.length; i++) {
        res_date[date_array[i].tag] = JSON.parse(JSON.stringify(date_array[i]));
        res_date[date_array[i].tag].info = new Array();
        for (var j = 0; j < date_monitor.length; j++) {
          if (date_array[i].tag == date_monitor[j].parent) {
            res_date[date_array[i].tag].info.push(date_monitor[j]);
          }
        }
      }
      res.json({
        code: 200,
        msg: "success",
        data: res_date,
      });
    } else {
      const connect = await querySql();
      var res_date = new Object();
      var monitor_model = monitorModel(connect);
      var date_array = await monitor_model.find({ parent: "address" });
      var monitor_model = monitorDateModel(connect);
      var date_monitor = await monitor_model.find({ address_id: address_id });
      for (var i = 0; i < date_array.length; i++) {
        res_date[date_array[i].tag] = JSON.parse(JSON.stringify(date_array[i]));
        res_date[date_array[i].tag].info = new Array();
        for (var j = 0; j < date_monitor.length; j++) {
          if (date_array[i].tag == date_monitor[j].parent) {
            res_date[date_array[i].tag].info.push(date_monitor[j]);
          }
        }
      }
      res.json({
        code: 200,
        msg: "success",
        data: res_date,
      });
    }
  }
}
module.exports = {
  cateList,
  addCate,
  findAllCate,
  findAllMonitorAddress,
  addMonitorAddress,
  findAddressInfo,
  addMonitorUserAddress,
};

const {formatDateToString, formatTimestamp} = require("../../utils/DateUtils")
const FirstAnalyzeEnum = require("../../utils/enums/AnalyzeDorisKeyEnum");
const {default: BigNumber} = require("bignumber.js");
BigNumber.config({DECIMAL_PLACES: 100});
const {redis} = require("../../utils/redisUtils");
const {Contract, JsonRpcProvider, formatUnits} = require("ethers");
const {dbPairQuery} = require("../../model/mysql/AviPairModel");
const {pairAbi, allAbi} = require("../../abi");
const {RPC_NEW} = require("../../utils/constant");
const provider = new JsonRpcProvider(RPC_NEW, undefined, {polling: true});
const blackHoldAddressOne = '******************************************';//黑洞地址
const blackHoldAddressTwo = '******************************************';//黑洞地址
const {HQL_sonar} = require("../../database/PqueryQL/PostgresSQL");
const {formatStrDecimal, getTokenCache} = require("../../utils/BaseDataUtils");

function decode(encode_data) {
    return JSON.parse(global.decodeURIComponent(global.atob(encode_data).replace(/\+/g, " ")));
}


/**
 * 交易分页查询
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function tradeGetPage(request, result) {
    try {
        let params = request.body;
        if (!params.md_id || !params.token_address || params.page < 0 || !params.size) {
            result.json({
                code: 400, msg: 'md_id,token_address,page,size参数不能为空', data: null
            })
            return;
        }
        params.token_address = params.token_address.toLowerCase().replace("0x", "");//数据库全是小写
        let arg = `WITH
                    pairs AS (  -- 获取当前合约持有的所有地址(最后时刻数据)
                         select address as pairAddress,token0,token1,datetime from MV_pairs_token0 FINAL where token0 = '${params.token_address}'
                         union all
                         select address as pairAddress,token0,token1,datetime from MV_pairs_token1 FINAL where token1 = '${params.token_address}' 
                    ),
                    pairReserves as (
                         select address as pairAddress,reserve0,reserve1 from MV_LAST_syncs FINAL
                         where address in(select pairAddress from pairs)
                    ),
                    pairDecimals as (
                         select address as tokenAddress,decimals from MV_LAST_contracts FINAL
                         where address in(select token0 from pairs) or address in(select token1 from pairs) or address in(select pairAddress from pairs)
                    )
                    SELECT toJSONString(map(
                        'type','pairs',
                        'pairAddress',toString(pairAddress),
                        'token0',toString(token0),
                        'token1',toString(token1),
                        'time',formatDateTime(datetime,'%Y-%m-%d %H:%i:%s')
                    )) AS json FROM pairs
                    union all
                    SELECT toJSONString(map(
                        'type','pairResreves',
                        'pairAddress',toString(pairAddress),
                        'reserve0',toString(reserve0),
                        'reserve1',toString(reserve1)
                    )) AS json FROM pairReserves
                    union all
                    SELECT toJSONString(map(
                        'type','pairDecimals',
                        'tokenAddress',toString(tokenAddress),
                        'decimals',toString(decimals)
                    )) AS json FROM pairDecimals`;
        let data = await HQL_sonar(arg);
        let listPairs = [];
        let mapReserves = {};
        let mapDecimals = {};
        if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                let json = data[i].json;
                let jsonColumn = JSON.parse(json);
                switch (jsonColumn['type']) {
                    case "pairs":
                        if (jsonColumn.token0 == params.token_address) {
                            jsonColumn.token_index = 0;
                            jsonColumn.target_token = jsonColumn.token0;
                        } else {
                            jsonColumn.token_index = 1;
                            jsonColumn.target_token = jsonColumn.token1;
                        }
                        listPairs.push(jsonColumn);
                        break;
                    case "pairResreves":
                        mapReserves[jsonColumn.pairAddress] = {
                            reserve0: jsonColumn.reserve0,
                            reserve1: jsonColumn.reserve1
                        };
                        break;
                    case "pairDecimals":
                        mapDecimals[jsonColumn.tokenAddress] = {
                            decimals: jsonColumn.decimals
                        };
                        break;
                }
            }
            for (let i = 0; i < listPairs.length; i++) {
                let item = listPairs[i];
                let reserves = mapReserves[item.pairAddress];
                let decimals = mapDecimals[item.pairAddress];
                let decimals0 = mapDecimals[item.token0];
                let decimals1 = mapDecimals[item.token1];
                item.decimals = decimals.decimals;
                if (reserves) {
                    item.reserve0 = formatStrDecimal(reserves.reserve0, decimals0.decimals);
                    item.reserve1 = formatStrDecimal(reserves.reserve1, decimals1.decimals);
                } else {
                    item.reserve0 = 0;
                    item.reserve1 = 0;
                }
            }
        } else {
            result.json({
                code: 200, msg: '没有数据！', data: null
            })
            return;
        }
        let index_max = 0;
        let max = new BigNumber(0);
        for (let index = 0; index < listPairs.length; index++) {
            let element = listPairs[index];
            if (element['token_index'] == 0) {
                let reserve0Big = new BigNumber(element['reserve0']);
                if (reserve0Big.comparedTo(max) > 0) {
                    max = reserve0Big;
                    index_max = index;
                }
            } else {
                let reserve1Big = new BigNumber(element['reserve1']);
                if (reserve1Big.comparedTo(max) > 0) {
                    max = reserve1Big;
                    index_max = index;
                }
            }
        }
        let pair_max = listPairs[index_max];//获取最大的储备交易对

        let buy = await redis.hGet("monitor:listenCount", params.md_id + "|trade_buy");
        let sell = await redis.hGet("monitor:listenCount", params.md_id + "|trade_sell");
        if (buy || sell) {
            let amountTotal = {
                buy: new BigNumber(buy ? buy : 0),
                sell: new BigNumber(sell ? sell : 0)
            }
            amountTotal.buy = amountTotal.buy.toFixed(3);
            amountTotal.sell = amountTotal.sell.toFixed(3);
            let Reserves = pair_max["reserve" + pair_max.token_index];
            let obj = {
                amountTotal: amountTotal,//所有买入卖出数量
                reserve: Reserves//底池代币数量
            }
            result.json({
                code: 200, msg: '请求成功！', data: obj
            })
        } else {
            result.json({
                code: 200, msg: '没有数据！', data: null
            })
        }
    } catch
        (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 流动性分页查询
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function liquidityGetPage(request, result) {
    try {
        let params = request.body;
        if (!params.token_address || params.page < 0 || !params.size) {
            result.json({
                code: 400, msg: 'token_address,page,size参数不能为空', data: null
            })
            return;
        }
        if (!params.block_time) {
            params.block_time = formatDateToString(new Date());//默认当前时间
        } else {
            params.block_time = formatTimestamp(params.block_time);
        }
        params.token_address = params.token_address.toLowerCase().replace("0x", "");//数据库全是小写
        let arg = `WITH
                    pairs AS (  -- 获取当前合约持有的所有地址(最后时刻数据)
                         select address as pairAddress,token0,token1,datetime from MV_pairs_token0 FINAL where token0 = '${params.token_address}'
                         union all
                         select address as pairAddress,token0,token1,datetime from MV_pairs_token1 FINAL where token1 = '${params.token_address}' 
                    ),
                    pairReserves as (
                         select address as pairAddress,reserve0,reserve1 from MV_LAST_syncs FINAL
                         where address in(select pairAddress from pairs)
                    ),
                    pairDecimals as (
                         select address as tokenAddress,decimals from MV_LAST_contracts FINAL
                         where address in(select token0 from pairs) or address in(select token1 from pairs) or address in(select pairAddress from pairs)
                    )
                    SELECT toJSONString(map(
                        'type','pairs',
                        'pairAddress',toString(pairAddress),
                        'token0',toString(token0),
                        'token1',toString(token1),
                        'time',formatDateTime(datetime,'%Y-%m-%d %H:%i:%s')
                    )) AS json FROM pairs
                    union all
                    SELECT toJSONString(map(
                        'type','pairResreves',
                        'pairAddress',toString(pairAddress),
                        'reserve0',toString(reserve0),
                        'reserve1',toString(reserve1)
                    )) AS json FROM pairReserves
                    union all
                    SELECT toJSONString(map(
                        'type','pairDecimals',
                        'tokenAddress',toString(tokenAddress),
                        'decimals',toString(decimals)
                    )) AS json FROM pairDecimals`;
        let data = await HQL_sonar(arg);
        let listPairs = [];
        let mapReserves = {};
        let mapDecimals = {};
        if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                let json = data[i].json;
                let jsonColumn = JSON.parse(json);
                switch (jsonColumn['type']) {
                    case "pairs":
                        if (jsonColumn.token0 == params.token_address) {
                            jsonColumn.token_index = 0;
                            jsonColumn.target_token = jsonColumn.token0;
                        } else {
                            jsonColumn.token_index = 1;
                            jsonColumn.target_token = jsonColumn.token1;
                        }
                        listPairs.push(jsonColumn);
                        break;
                    case "pairResreves":
                        mapReserves[jsonColumn.pairAddress] = {
                            reserve0: jsonColumn.reserve0,
                            reserve1: jsonColumn.reserve1
                        };
                        break;
                    case "pairDecimals":
                        mapDecimals[jsonColumn.tokenAddress] = {
                            decimals: jsonColumn.decimals
                        };
                        break;
                }
            }
            for (let i = 0; i < listPairs.length; i++) {
                let item = listPairs[i];
                let reserves = mapReserves[item.pairAddress];
                let decimals = mapDecimals[item.pairAddress];
                let decimals0 = mapDecimals[item.token0];
                let decimals1 = mapDecimals[item.token1];
                item.decimals = Number(decimals.decimals);
                item.decimals0 = Number(decimals0.decimals);
                item.decimals1 = Number(decimals1.decimals);
                if (reserves) {
                    item.reserve0 = formatStrDecimal(reserves.reserve0, decimals0.decimals);
                    item.reserve1 = formatStrDecimal(reserves.reserve1, decimals1.decimals);
                } else {
                    item.reserve0 = 0;
                    item.reserve1 = 0;
                }
            }
        } else {
            result.json({
                code: 200, msg: '没有数据！', data: null
            })
            return;
        }
        let index_max = 0;
        let max = new BigNumber(0);
        for (let index = 0; index < listPairs.length; index++) {
            let element = listPairs[index];
            if (element['token_index'] == 0) {
                let reserve0Big = new BigNumber(element['reserve0']);
                if (reserve0Big.comparedTo(max) > 0) {
                    max = reserve0Big;
                    index_max = index;
                }
            } else {
                let reserve1Big = new BigNumber(element['reserve1']);
                if (reserve1Big.comparedTo(max) > 0) {
                    max = reserve1Big;
                    index_max = index;
                }
            }
        }
        let pair_max = listPairs[index_max];//获取最大的储备交易对
        let tokenStr = await redis.hGet(FirstAnalyzeEnum.token_json_hdata+"_v2", "0x" + params.token_address);//送入队列重新解析
        if (!tokenStr) {
            throw Error("token没有找到！");
        }
        let pairContract = new Contract("0x" + pair_max.pairAddress, allAbi, provider);
        let total_supply = await pairContract.totalSupply();//总共发行量
        let total_supply_decimals = formatUnits(total_supply, pair_max.decimals);
        let oneTotal = formatUnits(await pairContract.balanceOf(blackHoldAddressOne), pair_max["decimal" + pair_max.token_index]);//黑洞持有lp
        let twoTotal = formatUnits(await pairContract.balanceOf(blackHoldAddressTwo), pair_max["decimal" + pair_max.token_index]);//黑洞持有lp
        let blackTotal = new BigNumber(oneTotal).plus(twoTotal);//黑洞总共持有
        let liquidity = new BigNumber(total_supply_decimals).minus(blackTotal);//现存流动性
        let obj = {
            usableLiquidity: liquidity,//现存流动性
            blackTotal: blackTotal//黑洞持有
        }
        result.json({
            code: 200, msg: '请求成功！', data: obj
        })
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

/**
 * 转账分页查询
 * @param request
 * @param result
 * @returns {Promise<void>}
 */
async function transferGetPage(request, result) {
    try {
        let params = request.body;
        if (!params.md_id || !params.token_address || params.page < 0 || !params.size) {
            result.json({
                code: 400, msg: 'md_id,token_address,page,size参数不能为空', data: null
            })
            return;
        }
        let amount = await redis.hGet("monitor:listenCount", params.md_id + "|trans");
        if (amount) {
            let amountTotal = new BigNumber(amount ? amount : 0);
            amountTotal = amountTotal.toFixed(3);
            let obj = {
                amountTotal: amountTotal,//转账总数数量
            }
            result.json({
                code: 200, msg: '请求成功！', data: obj
            })
        } else {
            result.json({
                code: 200, msg: '没有数据了！', data: null
            })
        }
    } catch (e) {
        result.json({
            code: 500, msg: e, data: null
        })
    }
}

module.exports = {
    tradeGetPage, liquidityGetPage, transferGetPage
}
